#!/usr/bin/env python3
"""
PlexMovieAutomator/09_plex_deployment.py

Final Plex Deployment Script - Smart Multi-Drive Content Organization

This script handles the final deployment of processed movies and TV shows to your Plex drives:
- Scans folder 6 (final_plex_ready) for completed movies and TV shows
- Analyzes Drive F (Plex) and Drive G (Plex2) storage capacity
- Uses smart alphabetical organization logic for movies (meaningful first letter)
- Deploys TV shows directly to Shows/ directory (or tv_shows/)
- Implements best-fit algorithm for optimal space usage
- Adds .plex_automator marker for tracking automated content
- Cleans up temp backup files after successful deployment

Architecture:
- Smart Storage Management: Fill Drive F first, then Drive G
- Intelligent Organization: Movies organized by meaningful first letter
- TV Show Organization: Series deployed directly to Shows/ directory
- Space Optimization: Best-fit algorithm for multiple items
- Automated Cleanup: Remove temp backups after deployment
- Tracking System: .plex_automator markers for identification

Content Support:
- Movies: Alphabetical organization (A-M, N-Z, #) in movies/ directory
- TV Shows: Direct series organization in Shows/ directory (or tv_shows/)
- Dual Structure: Supports both new (movies/tv_shows subdirs) and legacy layouts
- Command Line: --movies-only, --tv-only, --all options for selective processing

Flow: Folder 6 → Storage Analysis → Smart Organization → Plex Drives → Cleanup
"""

import sys
import os
import logging
import shutil
import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from utils.common_helpers import setup_logging, load_settings
except ImportError:
    # Fallback if utils not available
    def setup_logging():
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        return logging.getLogger(__name__)
    
    def load_settings(path):
        return {}

# Setup logging
logger = setup_logging()

@dataclass
class MovieInfo:
    """Information about a movie or TV show ready for deployment."""
    name: str
    path: Path
    size_gb: float
    resolution: str
    content_type: str = "movies"  # "movies" or "tv_shows"  # 1080p, 4k, 720p
    
@dataclass
class DriveInfo:
    """Information about a Plex drive."""
    letter: str
    path: Path
    total_gb: float
    free_gb: float
    used_gb: float
    movies_path: Path
    tv_shows_path: Path

def get_drive_info(drive_letter: str) -> Optional[DriveInfo]:
    """
    Get storage information for a drive.
    
    Args:
        drive_letter: Drive letter (F or G)
        
    Returns:
        DriveInfo object or None if drive not accessible
    """
    try:
        if os.name == 'nt':  # Windows
            drive_path = Path(f"{drive_letter}:\\")
        else:  # Unix-like (Git Bash)
            drive_path = Path(f"/{drive_letter.lower()}")
        
        if not drive_path.exists():
            logger.warning(f"⚠️ Drive {drive_letter}: not accessible")
            return None
        
        # Get disk usage
        if os.name == 'nt':
            import shutil
            total, used, free = shutil.disk_usage(drive_path)
        else:
            # For Git Bash environment
            stat = os.statvfs(drive_path)
            total = stat.f_blocks * stat.f_frsize
            free = stat.f_bavail * stat.f_frsize
            used = total - free
        
        # Convert to GB
        total_gb = total / (1024**3)
        free_gb = free / (1024**3)
        used_gb = used / (1024**3)
        
        movies_path = drive_path / "movies"
        
        # Handle TV shows path - G drive uses "Shows", others use "tv_shows"
        if drive_letter.upper() == 'G':
            tv_shows_path = drive_path / "Shows"
        else:
            tv_shows_path = drive_path / "tv_shows"
        
        return DriveInfo(
            letter=drive_letter,
            path=drive_path,
            total_gb=total_gb,
            free_gb=free_gb,
            used_gb=used_gb,
            movies_path=movies_path,
            tv_shows_path=tv_shows_path
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to get info for drive {drive_letter}: {e}")
        return None

def get_folder_size_gb(folder_path: Path) -> float:
    """
    Calculate folder size in GB.
    
    Args:
        folder_path: Path to folder
        
    Returns:
        Size in GB
    """
    try:
        total_size = 0
        for file_path in folder_path.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return total_size / (1024**3)
    except Exception as e:
        logger.warning(f"⚠️ Failed to calculate size for {folder_path}: {e}")
        return 0.0

def determine_alphabetical_folder(movie_name: str) -> Tuple[str, str]:
    """
    Determine the correct alphabetical organization for a movie.
    Uses smart logic to handle "The", "A", "An" prefixes.
    
    Args:
        movie_name: Movie name (e.g., "The Matrix (1999)")
        
    Returns:
        Tuple of (main_folder, sub_folder) e.g., ("A-M", "M")
    """
    # Remove year and clean up
    clean_name = re.sub(r'\s*\(\d{4}\).*$', '', movie_name).strip()
    
    # Handle common prefixes - use the meaningful word
    prefixes_to_skip = ['The ', 'A ', 'An ']
    for prefix in prefixes_to_skip:
        if clean_name.startswith(prefix):
            clean_name = clean_name[len(prefix):].strip()
            break
    
    if not clean_name:
        return "#", "0"
    
    first_char = clean_name[0].upper()
    
    # Numbers go to # folder
    if first_char.isdigit():
        return "#", first_char
    
    # Letters A-M go to A-M folder
    if 'A' <= first_char <= 'M':
        return "A-M", first_char
    
    # Letters N-Z go to N-Z folder  
    if 'N' <= first_char <= 'Z':
        return "N-Z", first_char
    
    # Special characters go to # folder
    return "#", "0"

def discover_movies_ready_for_deployment() -> List[MovieInfo]:
    """
    Discover movies in folder 6 ready for Plex deployment.
    
    Returns:
        List of MovieInfo objects containing only movies
    """
    logger.info("🔍 Discovering movies ready for Plex deployment...")
    
    ready_movies = []
    folder6_base = Path("workspace") / "6_final_plex_ready"
    
    if not folder6_base.exists():
        logger.warning("⚠️ Folder 6 (final_plex_ready) not found")
        return ready_movies
    
    # Check for new structure (movies/tv_shows subdirectories)
    content_type_dirs = []
    for item in folder6_base.iterdir():
        if item.is_dir():
            if item.name == 'movies':
                # New structure: scan movies subdirectory only
                for resolution_dir in item.iterdir():
                    if resolution_dir.is_dir() and resolution_dir.name in ['1080p', '4k', '720p']:
                        content_type_dirs.append((resolution_dir, item.name))
                        logger.debug(f"📁 Found {item.name}/{resolution_dir.name}")
            elif item.name in ['1080p', '4k', '720p']:
                # Legacy structure: direct resolution directories (assume movies)
                content_type_dirs.append((item, 'movies'))
                logger.debug(f"📁 Found legacy {item.name}")

    if not content_type_dirs:
        logger.warning("No movie resolution directories found in final_plex_ready")
        return ready_movies

    # Process each resolution directory for movies
    for resolution_dir, content_type in content_type_dirs:
        logger.info(f"📁 Scanning {content_type}/{resolution_dir.name} content...")
            
        for movie_dir in resolution_dir.iterdir():
                if movie_dir.is_dir():
                    # Check if we have both movie file and poster
                    movie_files = list(movie_dir.glob("*.mkv"))
                    poster_files = list(movie_dir.glob("poster.jpg"))
                    
                    if movie_files and poster_files:
                        size_gb = get_folder_size_gb(movie_dir)
                        movie_info = MovieInfo(
                            name=movie_dir.name,
                            path=movie_dir,
                            size_gb=size_gb,
                            resolution=resolution_dir.name,
                            content_type=content_type
                        )
                        ready_movies.append(movie_info)
                        logger.info(f"  ✅ Ready: {movie_dir.name} ({size_gb:.2f} GB) [{content_type}]")
                    else:
                        logger.warning(f"  ⚠️ Incomplete: {movie_dir.name} (missing files)")
    
    logger.info(f"📊 Found {len(ready_movies)} movies ready for deployment")
    return ready_movies


def discover_tv_shows_ready_for_deployment() -> List[MovieInfo]:
    """
    Discover TV shows in folder 6 ready for Plex deployment.
    TV shows are organized by series/season structure with episode files.
    
    Returns:
        List of MovieInfo objects containing only TV shows
    """
    logger.info("📺 Discovering TV shows ready for Plex deployment...")
    
    ready_tv_shows = []
    folder6_base = Path("workspace") / "6_final_plex_ready"
    
    if not folder6_base.exists():
        logger.warning("⚠️ Folder 6 (final_plex_ready) not found")
        return ready_tv_shows
    
    # Check for new structure (movies/tv_shows subdirectories)
    content_type_dirs = []
    for item in folder6_base.iterdir():
        if item.is_dir():
            if item.name == 'tv_shows':
                # New structure: scan tv_shows subdirectory
                for resolution_dir in item.iterdir():
                    if resolution_dir.is_dir() and resolution_dir.name in ['1080p', '4k', '720p']:
                        content_type_dirs.append((resolution_dir, item.name))
                        logger.debug(f"📁 Found {item.name}/{resolution_dir.name}")
            elif item.name in ['1080p', '4k', '720p']:
                # Legacy structure: check for TV show patterns in resolution directories
                # Look for series directories that contain season subdirectories
                for series_candidate in item.iterdir():
                    if series_candidate.is_dir():
                        # Check if this looks like a TV series (has season directories or episodes)
                        has_seasons = any(d.is_dir() and 'season' in d.name.lower() 
                                        for d in series_candidate.iterdir())
                        has_episodes = any(f.is_file() and f.suffix.lower() == '.mkv' 
                                         for f in series_candidate.rglob('*.mkv'))
                        
                        if has_seasons or has_episodes:
                            # This looks like a TV series
                            content_type_dirs.append((item, 'tv_shows_legacy'))
                            logger.debug(f"📁 Found legacy TV shows in {item.name}")
                            break

    if not content_type_dirs:
        logger.warning("No TV show resolution directories found in final_plex_ready")
        return ready_tv_shows

    # Process each resolution directory for TV shows
    for resolution_dir, content_type in content_type_dirs:
        logger.info(f"📁 Scanning {content_type}/{resolution_dir.name} content...")
            
        for series_dir in resolution_dir.iterdir():
            if series_dir.is_dir():
                # Check if this is a TV series with poster and episodes
                poster_files = list(series_dir.glob("poster.jpg"))
                
                # Look for episodes in the series directory (either direct or in season subdirs)
                episode_files = list(series_dir.rglob("*.mkv"))
                
                # Check for season subdirectories
                season_dirs = [d for d in series_dir.iterdir() if d.is_dir() and 
                             any(pattern in d.name.lower() for pattern in ['season', 's0', 's1', 's2', 's3', 's4', 's5', 's6', 's7', 's8', 's9'])]
                
                has_episodes = len(episode_files) > 0
                has_seasons = len(season_dirs) > 0
                
                if (has_episodes or has_seasons) and poster_files:
                    size_gb = get_folder_size_gb(series_dir)
                    tv_show_info = MovieInfo(  # Using MovieInfo but for TV shows
                        name=series_dir.name,
                        path=series_dir,
                        size_gb=size_gb,
                        resolution=resolution_dir.name,
                        content_type='tv_shows'
                    )
                    ready_tv_shows.append(tv_show_info)
                    logger.info(f"  ✅ Ready: {series_dir.name} ({size_gb:.2f} GB) [{len(episode_files)} episodes, {len(season_dirs)} seasons]")
                else:
                    missing_items = []
                    if not has_episodes and not has_seasons:
                        missing_items.append("episodes")
                    if not poster_files:
                        missing_items.append("poster.jpg")
                    logger.warning(f"  ⚠️ Incomplete: {series_dir.name} (missing {', '.join(missing_items)})")
    
    logger.info(f"📊 Found {len(ready_tv_shows)} TV shows ready for deployment")
    return ready_tv_shows


def discover_all_content_ready_for_deployment() -> List[MovieInfo]:
    """
    Discover both movies and TV shows ready for Plex deployment.
    
    Returns:
        List of MovieInfo objects containing both movies and TV shows
    """
    logger.info("🔍 Discovering all content ready for Plex deployment...")
    
    ready_movies = discover_movies_ready_for_deployment()
    ready_tv_shows = discover_tv_shows_ready_for_deployment()
    
    all_content = ready_movies + ready_tv_shows
    
    logger.info(f"📊 Total content ready for deployment:")
    logger.info(f"   🎬 Movies: {len(ready_movies)}")
    logger.info(f"   📺 TV Shows: {len(ready_tv_shows)}")
    logger.info(f"   📊 Total: {len(all_content)}")
    
    return all_content


def optimize_movie_placement(movies: List[MovieInfo], drives: List[DriveInfo]) -> Dict[str, List[MovieInfo]]:
    """
    Optimize movie placement across drives using best-fit algorithm.

    Args:
        movies: List of movies to place
        drives: List of available drives

    Returns:
        Dictionary mapping drive letters to lists of movies
    """
    logger.info(f"🧠 Optimizing placement of {len(movies)} movies across {len(drives)} drives...")

    # Sort movies by size (largest first for better packing)
    sorted_movies = sorted(movies, key=lambda m: m.size_gb, reverse=True)

    # Sort drives by preference (F first, then G)
    sorted_drives = sorted(drives, key=lambda d: d.letter)

    placement = {drive.letter: [] for drive in sorted_drives}

    for movie in sorted_movies:
        placed = False

        # Try to place in preferred order
        for drive in sorted_drives:
            current_movies = placement[drive.letter]
            current_size = sum(m.size_gb for m in current_movies)

            # Check if movie fits (leave 5GB buffer)
            if current_size + movie.size_gb <= drive.free_gb - 5.0:
                placement[drive.letter].append(movie)
                logger.debug(f"📦 Placed {movie.name} ({movie.size_gb:.2f} GB) on Drive {drive.letter}")
                placed = True
                break

        if not placed:
            logger.warning(f"⚠️ Could not place {movie.name} ({movie.size_gb:.2f} GB) - insufficient space")

    # Log placement summary
    for drive_letter, drive_movies in placement.items():
        if drive_movies:
            total_size = sum(m.size_gb for m in drive_movies)
            logger.info(f"📊 Drive {drive_letter}: {len(drive_movies)} movies, {total_size:.2f} GB")

    return placement


def add_plex_automator_marker(movie_dir: Path) -> bool:
    """
    Add .plex_automator marker file to identify automated movies.

    Args:
        movie_dir: Path to movie directory

    Returns:
        True if successful, False otherwise
    """
    try:
        marker_file = movie_dir / ".plex_automator"

        # Create marker with timestamp and info
        marker_content = f"""# Plex Automator Marker
# This movie was processed through the PlexMovieAutomator system
# Created: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Movie: {movie_dir.name}
#
# This marker helps distinguish automated movies from manually curated ones.
# You can use this to identify movies that may need additional poster work.
"""

        marker_file.write_text(marker_content, encoding='utf-8')
        logger.debug(f"🏷️ Added Plex Automator marker to {movie_dir.name}")
        return True

    except Exception as e:
        logger.warning(f"⚠️ Failed to add marker to {movie_dir.name}: {e}")
        return False


def deploy_movie_to_plex(movie: MovieInfo, drive: DriveInfo) -> bool:
    """
    Deploy a single movie or TV show to the specified Plex drive.

    Args:
        movie: Movie or TV show to deploy
        drive: Target drive

    Returns:
        True if successful, False otherwise
    """
    try:
        content_name = "TV show" if movie.content_type == "tv_shows" else "movie"
        logger.info(f"🚀 Deploying {content_name} {movie.name} to Drive {drive.letter}...")

        # Select appropriate target path based on content type
        if movie.content_type == "tv_shows":
            target_path = drive.tv_shows_path
            # TV shows go directly into Shows directory
            dest_path = target_path / movie.name
            main_folder = sub_folder = None  # Not used for TV shows
        else:
            target_path = drive.movies_path
            # Movies use alphabetical organization
            main_folder, sub_folder = determine_alphabetical_folder(movie.name)
            dest_path = target_path / main_folder / sub_folder / movie.name
            
        dest_path.mkdir(parents=True, exist_ok=True)

        # Add Plex Automator marker before moving
        add_plex_automator_marker(movie.path)

        # Copy movie files
        files_copied = 0
        for file_path in movie.path.iterdir():
            if file_path.is_file():
                dest_file = dest_path / file_path.name
                shutil.copy2(file_path, dest_file)
                files_copied += 1
                logger.debug(f"📁 Copied: {file_path.name}")

        # Log success with appropriate path
        if movie.content_type == "tv_shows":
            logger.info(f"✅ Successfully deployed {movie.name} to {drive.letter}:\\Shows\\{movie.name}\\")
        else:
            logger.info(f"✅ Successfully deployed {movie.name} to {drive.letter}:\\movies\\{main_folder}\\{sub_folder}\\")
        logger.info(f"   📁 Files copied: {files_copied}")
        logger.info(f"   💾 Size: {movie.size_gb:.2f} GB")

        return True

    except Exception as e:
        logger.error(f"❌ Failed to deploy {movie.name} to Drive {drive.letter}: {e}")
        return False


def cleanup_temp_backup(movie_name: str) -> bool:
    """
    Clean up temp backup files after successful deployment.

    Args:
        movie_name: Name of the movie to clean up

    Returns:
        True if successful, False otherwise
    """
    try:
        temp_backup_base = Path("workspace") / "temp_backup"

        if not temp_backup_base.exists():
            logger.debug(f"🗑️ No temp backup folder found")
            return True

        # Find and remove movie backup
        for resolution_dir in temp_backup_base.iterdir():
            if resolution_dir.is_dir():
                movie_backup = resolution_dir / movie_name
                if movie_backup.exists():
                    if movie_backup.is_dir():
                        shutil.rmtree(movie_backup)
                        logger.info(f"🗑️ Cleaned up temp backup: {movie_name}")
                        return True
                    elif movie_backup.is_file():
                        movie_backup.unlink()
                        logger.info(f"🗑️ Cleaned up temp backup file: {movie_name}")
                        return True

        logger.debug(f"🗑️ No temp backup found for: {movie_name}")
        return True

    except Exception as e:
        logger.warning(f"⚠️ Failed to cleanup temp backup for {movie_name}: {e}")
        return False


def cleanup_processed_movie(movie: MovieInfo) -> bool:
    """
    Remove movie from folder 6 after successful deployment.

    Args:
        movie: Movie that was successfully deployed

    Returns:
        True if successful, False otherwise
    """
    try:
        if movie.path.exists():
            shutil.rmtree(movie.path)
            logger.info(f"🗑️ Cleaned up processed movie: {movie.name}")
            return True
        return True

    except Exception as e:
        logger.warning(f"⚠️ Failed to cleanup processed movie {movie.name}: {e}")
        return False


def process_plex_deployment():
    """
    Main function to process Plex deployment for both movies and TV shows.
    Handles the complete workflow from discovery to cleanup.
    """
    logger.info("🚀 Starting Plex Deployment (Script 9)")
    logger.info("🎬📺 Processing both movies and TV shows")
    logger.info("=" * 60)

    # Load settings
    try:
        settings = load_settings("_internal/config/settings.ini")
        logger.info("📋 Settings loaded")
    except Exception as e:
        logger.warning(f"⚠️ Failed to load settings: {e}, using defaults")
        settings = {}

    # Discover content ready for deployment
    ready_content = discover_all_content_ready_for_deployment()
    if not ready_content:
        logger.info("✅ No content ready for deployment")
        return

    # Get drive information
    logger.info("💾 Analyzing Plex drive storage...")
    drives = []

    for drive_letter in ['F', 'G']:
        drive_info = get_drive_info(drive_letter)
        if drive_info:
            drives.append(drive_info)
            logger.info(f"📊 Drive {drive_letter}: {drive_info.free_gb:.1f} GB free / {drive_info.total_gb:.1f} GB total")
        else:
            logger.warning(f"⚠️ Drive {drive_letter} not accessible")

    if not drives:
        logger.error("❌ No Plex drives accessible - cannot deploy content")
        return

    # Optimize content placement
    placement = optimize_movie_placement(ready_content, drives)

    # Deploy content
    successful_deployments = []
    failed_deployments = []

    logger.info(f"\n🎬📺 Starting deployment of {len(ready_content)} items...")
    logger.info("=" * 50)

    for drive_letter, content_to_deploy in placement.items():
        if not content_to_deploy:
            continue

        # Find the drive info
        drive = next(d for d in drives if d.letter == drive_letter)

        logger.info(f"\n📀 Deploying to Drive {drive_letter}:")

        for content in content_to_deploy:
            if deploy_movie_to_plex(content, drive):
                successful_deployments.append(content)

                # Cleanup temp backup
                cleanup_temp_backup(content.name)

                # Cleanup processed content from folder 6
                cleanup_processed_movie(content)

            else:
                failed_deployments.append(content)

    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 Plex Deployment Summary:")
    logger.info(f"   ✅ Successfully deployed: {len(successful_deployments)}")
    logger.info(f"   ❌ Failed deployments: {len(failed_deployments)}")
    logger.info(f"   📁 Total processed: {len(ready_content)}")

    if successful_deployments:
        total_size = sum(c.size_gb for c in successful_deployments)
        movies_count = sum(1 for c in successful_deployments if c.content_type == 'movies')
        tv_shows_count = sum(1 for c in successful_deployments if c.content_type == 'tv_shows')
        
        logger.info(f"   💾 Total size deployed: {total_size:.2f} GB")
        logger.info(f"   🎬 Movies deployed: {movies_count}")
        logger.info(f"   📺 TV shows deployed: {tv_shows_count}")
        logger.info(f"\n🎉 {len(successful_deployments)} items are now in your Plex library!")

        # List successful deployments by type
        if movies_count > 0:
            logger.info(f"\n📋 Successfully deployed movies:")
            for content in successful_deployments:
                if content.content_type == 'movies':
                    main_folder, sub_folder = determine_alphabetical_folder(content.name)
                    logger.info(f"   ✅ {content.name} → movies/{main_folder}/{sub_folder}/")
        
        if tv_shows_count > 0:
            logger.info(f"\n📋 Successfully deployed TV shows:")
            for content in successful_deployments:
                if content.content_type == 'tv_shows':
                    logger.info(f"   ✅ {content.name} → Shows/ (or tv_shows/)")

    if failed_deployments:
        logger.info(f"\n❌ Failed deployments:")
        for content in failed_deployments:
            content_type = "TV show" if content.content_type == 'tv_shows' else "movie"
            logger.info(f"   ❌ {content.name} ({content.size_gb:.2f} GB) [{content_type}]")


def process_movies_plex_deployment():
    """
    Process Plex deployment for movies only.
    """
    logger.info("🚀 Starting Movies Plex Deployment (Script 9)")
    logger.info("🎬 Processing movies only")
    logger.info("=" * 60)

    # Load settings
    try:
        settings = load_settings("_internal/config/settings.ini")
        logger.info("📋 Settings loaded")
    except Exception as e:
        logger.warning(f"⚠️ Failed to load settings: {e}, using defaults")
        settings = {}

    # Discover movies ready for deployment
    ready_movies = discover_movies_ready_for_deployment()
    if not ready_movies:
        logger.info("✅ No movies ready for deployment")
        return

    # Get drive information
    logger.info("💾 Analyzing Plex drive storage...")
    drives = []

    for drive_letter in ['F', 'G']:
        drive_info = get_drive_info(drive_letter)
        if drive_info:
            drives.append(drive_info)
            logger.info(f"📊 Drive {drive_letter}: {drive_info.free_gb:.1f} GB free / {drive_info.total_gb:.1f} GB total")
        else:
            logger.warning(f"⚠️ Drive {drive_letter} not accessible")

    if not drives:
        logger.error("❌ No Plex drives accessible - cannot deploy movies")
        return

    # Optimize movie placement
    placement = optimize_movie_placement(ready_movies, drives)

    # Deploy movies
    successful_deployments = []
    failed_deployments = []

    logger.info(f"\n🎬 Starting deployment of {len(ready_movies)} movies...")
    logger.info("=" * 50)

    for drive_letter, movies_to_deploy in placement.items():
        if not movies_to_deploy:
            continue

        # Find the drive info
        drive = next(d for d in drives if d.letter == drive_letter)

        logger.info(f"\n📀 Deploying to Drive {drive_letter}:")

        for movie in movies_to_deploy:
            if deploy_movie_to_plex(movie, drive):
                successful_deployments.append(movie)

                # Cleanup temp backup
                cleanup_temp_backup(movie.name)

                # Cleanup processed movie from folder 6
                cleanup_processed_movie(movie)

            else:
                failed_deployments.append(movie)

    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 Movies Plex Deployment Summary:")
    logger.info(f"   ✅ Successfully deployed: {len(successful_deployments)}")
    logger.info(f"   ❌ Failed deployments: {len(failed_deployments)}")
    logger.info(f"   📁 Total processed: {len(ready_movies)}")

    if successful_deployments:
        total_size = sum(m.size_gb for m in successful_deployments)
        logger.info(f"   💾 Total size deployed: {total_size:.2f} GB")
        logger.info(f"\n🎉 {len(successful_deployments)} movies are now in your Plex library!")

        # List successful deployments
        logger.info(f"\n📋 Successfully deployed movies:")
        for movie in successful_deployments:
            main_folder, sub_folder = determine_alphabetical_folder(movie.name)
            logger.info(f"   ✅ {movie.name} → movies/{main_folder}/{sub_folder}/")

    if failed_deployments:
        logger.info(f"\n❌ Failed movie deployments:")
        for movie in failed_deployments:
            logger.info(f"   ❌ {movie.name} ({movie.size_gb:.2f} GB)")


def process_tv_shows_plex_deployment():
    """
    Process Plex deployment for TV shows only.
    """
    logger.info("🚀 Starting TV Shows Plex Deployment (Script 9)")
    logger.info("📺 Processing TV shows only")
    logger.info("=" * 60)

    # Load settings
    try:
        settings = load_settings("_internal/config/settings.ini")
        logger.info("📋 Settings loaded")
    except Exception as e:
        logger.warning(f"⚠️ Failed to load settings: {e}, using defaults")
        settings = {}

    # Discover TV shows ready for deployment
    ready_tv_shows = discover_tv_shows_ready_for_deployment()
    if not ready_tv_shows:
        logger.info("✅ No TV shows ready for deployment")
        return

    # Get drive information
    logger.info("💾 Analyzing Plex drive storage...")
    drives = []

    for drive_letter in ['F', 'G']:
        drive_info = get_drive_info(drive_letter)
        if drive_info:
            drives.append(drive_info)
            logger.info(f"📊 Drive {drive_letter}: {drive_info.free_gb:.1f} GB free / {drive_info.total_gb:.1f} GB total")
        else:
            logger.warning(f"⚠️ Drive {drive_letter} not accessible")

    if not drives:
        logger.error("❌ No Plex drives accessible - cannot deploy TV shows")
        return

    # Optimize TV show placement
    placement = optimize_movie_placement(ready_tv_shows, drives)

    # Deploy TV shows
    successful_deployments = []
    failed_deployments = []

    logger.info(f"\n📺 Starting deployment of {len(ready_tv_shows)} TV shows...")
    logger.info("=" * 50)

    for drive_letter, tv_shows_to_deploy in placement.items():
        if not tv_shows_to_deploy:
            continue

        # Find the drive info
        drive = next(d for d in drives if d.letter == drive_letter)

        logger.info(f"\n📀 Deploying to Drive {drive_letter}:")

        for tv_show in tv_shows_to_deploy:
            if deploy_movie_to_plex(tv_show, drive):
                successful_deployments.append(tv_show)

                # Cleanup temp backup
                cleanup_temp_backup(tv_show.name)

                # Cleanup processed TV show from folder 6
                cleanup_processed_movie(tv_show)

            else:
                failed_deployments.append(tv_show)

    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 TV Shows Plex Deployment Summary:")
    logger.info(f"   ✅ Successfully deployed: {len(successful_deployments)}")
    logger.info(f"   ❌ Failed deployments: {len(failed_deployments)}")
    logger.info(f"   📁 Total processed: {len(ready_tv_shows)}")

    if successful_deployments:
        total_size = sum(tv.size_gb for tv in successful_deployments)
        logger.info(f"   💾 Total size deployed: {total_size:.2f} GB")
        logger.info(f"\n🎉 {len(successful_deployments)} TV shows are now in your Plex library!")

        # List successful deployments
        logger.info(f"\n📋 Successfully deployed TV shows:")
        for tv_show in successful_deployments:
            logger.info(f"   ✅ {tv_show.name} → Shows/ (or tv_shows/)")

    if failed_deployments:
        logger.info(f"\n❌ Failed TV show deployments:")
        for tv_show in failed_deployments:
            logger.info(f"   ❌ {tv_show.name} ({tv_show.size_gb:.2f} GB)")


def display_interactive_menu():
    """
    Display the main interactive menu for content type selection.

    Returns:
        str: Selected content type ('movies', 'tv_shows', 'both', 'quit')
    """
    print(f"\n{'='*60}")
    print(f"🎬📺 PlexMovieAutomator - Interactive Content Selection")
    print(f"{'='*60}")
    print(f"\nWhat type of content would you like to process?")
    print(f"  1. Movies only")
    print(f"  2. TV Shows only")
    print(f"  3. Both Movies and TV Shows")
    print(f"  4. Quit")

    while True:
        try:
            choice = input(f"\nEnter your choice [1-4]: ").strip()

            if choice == '1':
                return 'movies'
            elif choice == '2':
                return 'tv_shows'
            elif choice == '3':
                return 'both'
            elif choice == '4':
                return 'quit'
            else:
                print(f"Please enter a number between 1 and 4")

        except KeyboardInterrupt:
            print(f"\n👋 Exiting...")
            return 'quit'


def main():
    """
    Main function for Plex deployment script supporting both movies and TV shows.
    """
    import argparse

    parser = argparse.ArgumentParser(description='Deploy processed movies and TV shows to Plex drives')
    parser.add_argument('--movies-only', action='store_true',
                       help='Process only movies (command-line mode)')
    parser.add_argument('--tv-only', action='store_true',
                       help='Process only TV shows (command-line mode)')
    parser.add_argument('--all', action='store_true',
                       help='Process both movies and TV shows (command-line mode)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be deployed without actually moving files')
    parser.add_argument('--force', action='store_true',
                       help='Force deployment even if drives are nearly full')

    args = parser.parse_args()

    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - No files will be moved")
    
    logger.info("🚀 Starting Plex Deployment Script (Script 09)")
    logger.info("   Supporting Movies and TV Shows with Smart Drive Organization")
    logger.info("   Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)")

    try:
        # Default to interactive mode unless command-line content arguments are specified
        if args.movies_only or args.tv_only or args.all:
            # Command line mode - user specified content type arguments
            if args.movies_only:
                content_type_choice = 'movies'
            elif args.tv_only:
                content_type_choice = 'tv_shows'
            else:  # args.all
                content_type_choice = 'both'
        else:
            # Interactive mode (default behavior)
            content_type_choice = display_interactive_menu()
            
            if content_type_choice == 'quit':
                logger.info("👋 User chose to quit")
                return
        
        # Process based on selection
        if content_type_choice == 'movies':
            logger.info("🎬 Processing movies only")
            process_movies_plex_deployment()
        elif content_type_choice == 'tv_shows':
            logger.info("📺 Processing TV shows only")
            process_tv_shows_plex_deployment()
        else:
            # Process both movies and TV shows
            logger.info("🎬📺 Processing both movies and TV shows")
            process_plex_deployment()
        
    except KeyboardInterrupt:
        logger.info("👋 Plex deployment interrupted by user")
        print(f"\n👋 Plex deployment interrupted")
        
    except Exception as e:
        logger.error(f"❌ Plex deployment failed: {e}")
        print(f"❌ Plex deployment failed: {e}")


if __name__ == "__main__":
    main()
