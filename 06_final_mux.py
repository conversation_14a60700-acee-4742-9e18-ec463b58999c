#!/usr/bin/env python3
"""
PlexMovieAutomator/06_final_mux.py

Filesystem-First Final Mux Script - Aligned with Scripts 1-5 Architecture

This script:
- Uses filesystem scanning to discover movies ready for final mux
- Uses marker files for state management (.muxed, .subtitle_complete, etc.)
- Takes all files in movie folder and muxes them together
- Names output as "Movie Name (Year).mkv"
- Cleans up intermediate files
- Creates proper status stamps

Architecture matches scripts 1-5:
- Pure filesystem-based state management
- No complex database operations
- Marker files as single source of truth
- Idempotent operations
"""

import sys
import os
import json
import logging
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

# Fix Windows console encoding issues
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Add the _internal directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

try:
    from utils.filesystem_first_state_manager import FilesystemFirstStateManager
    from utils.common_helpers import get_setting
    from utils.mkv_utils import get_mkv_info, get_audio_track_name
except ImportError:
    print("Error: Could not import required modules.")
    print("Please ensure you're running this from the PlexMovieAutomator root directory.")
    sys.exit(1)

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('_internal/logs/final_mux.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def get_audio_name_from_file(audio_file: Path) -> str:
    """
    Get proper audio track name based on file extension and filename.
    Uses similar logic to get_audio_track_name from mkv_utils.py

    Args:
        audio_file: Path to audio file

    Returns:
        str: Proper audio track name (e.g., "TrueHD Atmos", "DTS-HD MA", etc.)
    """
    try:
        file_ext = audio_file.suffix.lower()
        filename = audio_file.name.lower()

        # Map file extensions to codec names and determine channels from filename
        if file_ext == '.thd':
            # TrueHD files - check for Atmos in filename
            if 'atmos' in filename:
                return "TrueHD Atmos"
            elif '7.1' in filename or '8ch' in filename:
                return "TrueHD 7.1ch"
            elif '5.1' in filename or '6ch' in filename:
                return "TrueHD 5.1ch"
            else:
                return "TrueHD"
        elif file_ext == '.dts':
            # DTS files
            if 'hd' in filename or 'ma' in filename:
                return "DTS-HD MA"
            elif '7.1' in filename:
                return "DTS 7.1ch"
            elif '5.1' in filename:
                return "DTS 5.1ch"
            else:
                return "DTS"
        elif file_ext == '.ac3':
            if '5.1' in filename:
                return "AC3 5.1ch"
            elif '7.1' in filename:
                return "AC3 7.1ch"
            else:
                return "AC3"
        elif file_ext == '.eac3':
            if '5.1' in filename:
                return "E-AC3 5.1ch"
            elif '7.1' in filename:
                return "E-AC3 7.1ch"
            else:
                return "E-AC3"
        elif file_ext == '.flac':
            if '5.1' in filename:
                return "FLAC 5.1ch"
            elif '7.1' in filename:
                return "FLAC 7.1ch"
            else:
                return "FLAC"
        elif file_ext == '.aac':
            return "Stereo"  # AAC is typically stereo
        else:
            # Fallback for unknown extensions
            return f"Audio ({file_ext.upper()})"

    except Exception as e:
        logger.warning(f"Failed to determine audio name for {audio_file}: {e}")
        return "Audio"


def discover_movies_ready_for_mux():
    """
    Discover movies that are ready for final mux.
    Supports both new movies/tv_shows structure and legacy structure.
    
    Returns:
        List[Path]: List of movie directories ready for mux
    """
    logger.info("🔍 Discovering movies ready for final mux...")
    
    ready_movies = []
    workspace_path = Path("workspace")
    ready_for_mux_path = workspace_path / "4_ready_for_final_mux"
    
    if not ready_for_mux_path.exists():
        logger.warning(f"Ready for mux directory not found: {ready_for_mux_path}")
        return ready_movies
    
    # Check for new structure (movies/tv_shows subdirectories)
    movies_dir = ready_for_mux_path / "movies"
    tv_shows_dir = ready_for_mux_path / "tv_shows"
    
    content_dirs_to_scan = []
    
    if movies_dir.exists() or tv_shows_dir.exists():
        # New structure: scan movies/ and tv_shows/ subdirectories
        logger.info("📁 Using new movies/tv_shows structure")
        if movies_dir.exists():
            content_dirs_to_scan.append(("movies", movies_dir))
        if tv_shows_dir.exists():
            content_dirs_to_scan.append(("tv_shows", tv_shows_dir))
    else:
        # Legacy structure: scan directly for resolution folders
        logger.info("📁 Using legacy structure")
        content_dirs_to_scan.append(("legacy", ready_for_mux_path))
    
    for content_type, content_dir in content_dirs_to_scan:
        logger.info(f"🔍 Scanning {content_type} content...")
        
        # Check resolution directories within content directory
        for item in content_dir.iterdir():
            if item.is_dir():
                # Check if this is a resolution folder
                if item.name.lower() in ['1080p', '4k', '720p', '2160p']:
                    resolution_dir = item
                    logger.info(f"  📁 Scanning {resolution_dir.name} in {content_type}...")
                    
                    for movie_dir in resolution_dir.iterdir():
                        if movie_dir.is_dir():
                            # Check if movie is ready for mux
                            subtitle_complete = (movie_dir / '.subtitle_complete').exists()
                            encoded_only = (movie_dir / '.encoded').exists()
                            already_muxed = (movie_dir / '.muxed').exists()
                            
                            if (subtitle_complete or encoded_only) and not already_muxed:
                                # Verify we have video files
                                video_files = list(movie_dir.glob("*.mkv"))
                                if video_files:
                                    ready_movies.append(movie_dir)
                                    logger.info(f"    ✅ Ready: {movie_dir.name} ({content_type})")
                                else:
                                    logger.warning(f"    ⚠️ No video files in {movie_dir.name}")
                elif content_type == "legacy":
                    # In legacy mode, this might be a movie directory directly
                    movie_dir = item
                    # Check if movie is ready for mux
                    subtitle_complete = (movie_dir / '.subtitle_complete').exists()
                    encoded_only = (movie_dir / '.encoded').exists()
                    already_muxed = (movie_dir / '.muxed').exists()
                    
                    if (subtitle_complete or encoded_only) and not already_muxed:
                        # Verify we have video files
                        video_files = list(movie_dir.glob("*.mkv"))
                        if video_files:
                            ready_movies.append(movie_dir)
                            logger.info(f"  ✅ Ready: {movie_dir.name} (legacy)")
                        else:
                            logger.warning(f"  ⚠️ No video files in {movie_dir.name}")
    
    logger.info(f"📊 Found {len(ready_movies)} movies ready for final mux")
    return ready_movies


def mux_movie_files(movie_dir: Path, settings: dict) -> bool:
    """
    Mux all files in movie directory into final movie file.
    
    Args:
        movie_dir: Path to movie directory
        settings: Settings dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        movie_name = movie_dir.name
        logger.info(f"🎬 Muxing files for: {movie_name}")
        
        # Scan for files in the movie directory
        video_files = list(movie_dir.glob("*.mkv"))
        srt_files = list(movie_dir.glob("*.srt"))
        sup_files = list(movie_dir.glob("*.sup"))
        audio_files = list(movie_dir.glob("*.ac3")) + list(movie_dir.glob("*.dts")) + list(movie_dir.glob("*.flac")) + list(movie_dir.glob("*.thd")) + list(movie_dir.glob("*.aac"))
        
        logger.info(f"📁 Files found - Video: {len(video_files)}, SRT: {len(srt_files)}, SUP: {len(sup_files)}, Audio: {len(audio_files)}")
        
        if not video_files:
            logger.error(f"❌ No video files found in {movie_dir}")
            return False
        
        # Use the first video file as the base
        main_video = video_files[0]
        
        # Define output path - use movie name only (remove any existing suffixes)
        output_path = movie_dir / f"{movie_name}.mkv"
        
        # If output already exists and is the same as input, create temp name first
        if output_path.exists() and output_path == main_video:
            temp_output = movie_dir / f"{movie_name}_final.mkv"
        else:
            temp_output = output_path
        
        # Get MKVMerge path from settings
        mkvmerge_exe = settings.get('Executables', {}).get('mkvmerge_path', 'C:/Program Files/MKVToolNix/mkvmerge.exe')
        
        # Build MKVMerge command
        command = [
            mkvmerge_exe,
            "-o", str(temp_output),
            "--no-chapters",  # Remove chapters as requested
            "--no-global-tags",  # Remove global tags as requested
            str(main_video)  # Main video/audio file
        ]
        
        # Add SRT subtitles (set first as default)
        for i, srt_file in enumerate(srt_files):
            default_flag = "yes" if i == 0 else "no"
            command.extend([
                "--language", "0:eng",
                "--track-name", "0:English (SRT)",
                "--default-track", f"0:{default_flag}",
                str(srt_file)
            ])
        
        # Add SUP subtitles (not default)
        for sup_file in sup_files:
            command.extend([
                "--language", "0:eng", 
                "--track-name", "0:English (SUP)",
                "--default-track", "0:no",
                str(sup_file)
            ])
        
        # Add extra audio files with proper naming
        for i, audio_file in enumerate(audio_files):
            # Get proper audio track name based on file extension/codec
            audio_track_name = get_audio_name_from_file(audio_file)

            command.extend([
                "--language", "0:eng",
                "--track-name", f"0:{audio_track_name}",
                "--default-track", "0:no",
                str(audio_file)
            ])
        
        logger.info(f"🚀 Running MKVMerge command...")
        logger.debug(f"Command: {' '.join(command)}")
        
        # Execute MKVMerge
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=3600  # 1 hour timeout
        )
        
        if result.returncode == 0:
            # Verify output file was created
            if temp_output.exists() and temp_output.stat().st_size > 0:
                # If we used a temp name, rename to final name
                if temp_output != output_path:
                    if output_path.exists():
                        output_path.unlink()  # Remove old file
                    temp_output.rename(output_path)
                
                file_size_gb = output_path.stat().st_size / (1024**3)
                logger.info(f"✅ Final mux successful!")
                logger.info(f"   Output: {output_path.name} ({file_size_gb:.2f} GB)")
                
                # Clean up intermediate files (keep only the final muxed file)
                files_to_remove = []
                if len(video_files) > 1 or video_files[0] != output_path:
                    files_to_remove.extend(video_files)
                files_to_remove.extend(srt_files + sup_files + audio_files)
                
                for file_to_remove in files_to_remove:
                    if file_to_remove != output_path and file_to_remove.exists():
                        try:
                            file_to_remove.unlink()
                            logger.debug(f"🗑️ Removed: {file_to_remove.name}")
                        except Exception as e:
                            logger.warning(f"⚠️ Failed to remove {file_to_remove.name}: {e}")
                
                return True
            else:
                logger.error("❌ Output file was not created or is empty")
                return False
        else:
            logger.error(f"❌ MKVMerge failed with return code {result.returncode}")
            if result.stderr:
                logger.error(f"Error output: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in mux operation: {e}")
        return False


def create_mux_markers(movie_dir: Path) -> bool:
    """
    Create status markers after successful mux.

    Args:
        movie_dir: Path to movie directory

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create .muxed marker
        muxed_marker = movie_dir / '.muxed'
        muxed_marker.write_text(json.dumps({
            'timestamp': datetime.now().isoformat(),
            'stage': 'muxed',
            'ready_for_poster_selection': True
        }, indent=2))

        # Remove old markers that are no longer needed
        old_markers = ['.encoded', '.subtitle_complete', '.subtitle_processing_pending']
        for marker in old_markers:
            marker_file = movie_dir / marker
            if marker_file.exists():
                marker_file.unlink()
                logger.debug(f"🗑️ Removed old marker: {marker}")

        logger.info(f"✅ Created .muxed status marker")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to create markers: {e}")
        return False


def move_to_folder_5(movie_dir: Path):
    """
    Move the muxed movie and marker to folder 5 (awaiting poster).
    Supports both new movies/tv_shows structure and legacy structure.

    Args:
        movie_dir: Path to current movie directory in folder 4
    """
    try:
        # Determine resolution from current path
        resolution = "1080p"  # Default
        if "4k" in str(movie_dir).lower():
            resolution = "4k"
        elif "720p" in str(movie_dir).lower():
            resolution = "720p"
        elif "2160p" in str(movie_dir).lower():
            resolution = "4k"  # Map 2160p to 4k
        
        # Determine content type from current path
        content_type = None
        movie_dir_str = str(movie_dir)
        if "/movies/" in movie_dir_str or "\\movies\\" in movie_dir_str:
            content_type = "movies"
        elif "/tv_shows/" in movie_dir_str or "\\tv_shows\\" in movie_dir_str:
            content_type = "tv_shows"

        # Create folder 5 destination (awaiting poster)
        folder5_base = Path("workspace") / "5_awaiting_poster"
        
        if content_type:
            # New structure: content_type/resolution/movie_name
            folder5_dest = folder5_base / content_type / resolution / movie_dir.name
        else:
            # Legacy structure: resolution/movie_name
            folder5_dest = folder5_base / resolution / movie_dir.name
            
        folder5_dest.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"📁 Moving to folder 5: {movie_dir.name} ({content_type or 'legacy'}/{resolution})")

        # Move the final MKV file
        mkv_files = list(movie_dir.glob("*.mkv"))
        if mkv_files:
            final_mkv = mkv_files[0]  # Should be only one after mux
            dest_mkv = folder5_dest / final_mkv.name
            shutil.move(str(final_mkv), str(dest_mkv))
            logger.info(f"  📁 Moved MKV: {final_mkv.name} → folder 5")

        # Move the .muxed marker
        muxed_marker = movie_dir / ".muxed"
        if muxed_marker.exists():
            dest_marker = folder5_dest / ".muxed"
            shutil.move(str(muxed_marker), str(dest_marker))
            logger.info(f"  📁 Moved marker: .muxed → folder 5")

        # Remove the now-empty folder 4 directory
        try:
            movie_dir.rmdir()
            logger.info(f"  🗑️ Removed empty folder 4 directory: {movie_dir.name}")
        except OSError:
            # Directory not empty - log what's left
            remaining_files = list(movie_dir.iterdir())
            logger.warning(f"  ⚠️ Folder 4 directory not empty: {[f.name for f in remaining_files]}")

        logger.info(f"✅ Successfully moved {movie_dir.name} to folder 5 ({content_type or 'legacy'}/{resolution})")

    except Exception as e:
        logger.error(f"Failed to move to folder 5: {e}")
        raise


def discover_episodes_ready_for_mux():
    """
    Discover TV episodes that are ready for final mux.
    
    TV Show equivalent of discover_movies_ready_for_mux() with episode-specific handling:
    - Series/season directory structure
    - Episode-based discovery and organization
    - Same marker file logic as movies but episode-aware
    
    Returns:
        List[Path]: List of episode directories ready for mux
    """
    logger.info("🔍 Discovering TV episodes ready for final mux...")
    
    ready_episodes = []
    workspace_path = Path("workspace")
    ready_for_mux_path = workspace_path / "4_ready_for_final_mux"
    
    if not ready_for_mux_path.exists():
        logger.warning(f"Ready for mux directory not found: {ready_for_mux_path}")
        return ready_episodes
    
    # Check for TV shows structure
    tv_shows_dir = ready_for_mux_path / "tv_shows"
    
    if not tv_shows_dir.exists():
        logger.warning(f"TV shows directory not found: {tv_shows_dir}")
        return ready_episodes
    
    logger.info("📁 Scanning TV shows for episodes ready for mux...")
    
    # Scan series directories
    for series_dir in tv_shows_dir.iterdir():
        if series_dir.is_dir():
            series_name = series_dir.name
            logger.info(f"  📺 Scanning series: {series_name}")
            
            # Scan season directories within series
            for season_dir in series_dir.iterdir():
                if season_dir.is_dir() and season_dir.name.startswith("Season"):
                    season_name = season_dir.name
                    logger.info(f"    📁 Scanning {season_name}...")
                    
                    # Scan for individual episodes within season
                    for episode_dir in season_dir.iterdir():
                        if episode_dir.is_dir():
                            # Check if episode is ready for mux
                            subtitle_complete = (episode_dir / '.subtitle_complete').exists()
                            encoded_only = (episode_dir / '.encoded').exists()
                            already_muxed = (episode_dir / '.muxed').exists()
                            
                            if (subtitle_complete or encoded_only) and not already_muxed:
                                # Verify we have video files for episode
                                video_files = list(episode_dir.glob("*.mkv"))
                                if video_files:
                                    ready_episodes.append(episode_dir)
                                    logger.info(f"      ✅ Ready: {episode_dir.name}")
                                else:
                                    logger.warning(f"      ⚠️ No video files in {episode_dir.name}")
    
    logger.info(f"📊 Found {len(ready_episodes)} episodes ready for final mux")
    return ready_episodes


def mux_episode_files(episode_dir: Path, settings: dict) -> bool:
    """
    Mux all files in episode directory into final episode file.
    
    TV Show equivalent of mux_movie_files() with episode-specific handling:
    - Episode-based naming conventions  
    - Series/season/episode organization
    - Same MKVMerge sophistication as movies
    - Episode-specific file management
    
    Args:
        episode_dir: Path to episode directory
        settings: Settings dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        episode_name = episode_dir.name
        series_name = episode_dir.parent.parent.name  # Series is 2 levels up
        season_name = episode_dir.parent.name  # Season is 1 level up
        
        logger.info(f"📺 Muxing files for episode: {series_name} - {season_name} - {episode_name}")
        
        # Scan for files in the episode directory (same file types as movies)
        video_files = list(episode_dir.glob("*.mkv"))
        srt_files = list(episode_dir.glob("*.srt"))
        sup_files = list(episode_dir.glob("*.sup"))
        audio_files = (list(episode_dir.glob("*.ac3")) + list(episode_dir.glob("*.dts")) + 
                      list(episode_dir.glob("*.flac")) + list(episode_dir.glob("*.thd")) + 
                      list(episode_dir.glob("*.aac")))
        
        logger.info(f"📁 Episode files found - Video: {len(video_files)}, SRT: {len(srt_files)}, SUP: {len(sup_files)}, Audio: {len(audio_files)}")
        
        if not video_files:
            logger.error(f"❌ No video files found in episode {episode_dir}")
            return False
        
        # Use the first video file as the base for episode
        main_video = video_files[0]
        
        # Define episode output path - use episode name only (remove any existing suffixes)
        output_path = episode_dir / f"{episode_name}.mkv"
        
        # If output already exists and is the same as input, create temp name first
        if output_path.exists() and output_path == main_video:
            temp_output = episode_dir / f"{episode_name}_final.mkv"
        else:
            temp_output = output_path
        
        # Get MKVMerge path from settings (same as movies)
        mkvmerge_exe = settings.get('Executables', {}).get('mkvmerge_path', 'C:/Program Files/MKVToolNix/mkvmerge.exe')
        
        # Build MKVMerge command for episode (same sophistication as movies)
        command = [
            mkvmerge_exe,
            "-o", str(temp_output),
            "--no-chapters",  # Remove chapters as requested
            "--no-global-tags",  # Remove global tags as requested
            str(main_video)  # Main video/audio file for episode
        ]
        
        # Add SRT subtitles for episode (set first as default, same as movies)
        for i, srt_file in enumerate(srt_files):
            default_flag = "yes" if i == 0 else "no"
            command.extend([
                "--language", "0:eng",
                "--track-name", "0:English (SRT)",
                "--default-track", f"0:{default_flag}",
                str(srt_file)
            ])
        
        # Add SUP subtitles for episode (not default, same as movies)
        for sup_file in sup_files:
            command.extend([
                "--language", "0:eng", 
                "--track-name", "0:English (SUP)",
                "--default-track", "0:no",
                str(sup_file)
            ])
        
        # Add extra audio files for episode with proper naming (same logic as movies)
        for i, audio_file in enumerate(audio_files):
            # Get proper audio track name based on file extension/codec (same function as movies)
            audio_track_name = get_audio_name_from_file(audio_file)

            command.extend([
                "--language", "0:eng",
                "--track-name", f"0:{audio_track_name}",
                "--default-track", "0:no",
                str(audio_file)
            ])
        
        logger.info(f"🚀 Running MKVMerge command for episode...")
        logger.debug(f"Episode command: {' '.join(command)}")
        
        # Execute MKVMerge for episode (same timeout and handling as movies)
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=3600  # 1 hour timeout (same as movies)
        )
        
        if result.returncode == 0:
            # Verify episode output file was created
            if temp_output.exists() and temp_output.stat().st_size > 0:
                # If we used a temp name, rename to final name
                if temp_output != output_path:
                    if output_path.exists():
                        output_path.unlink()  # Remove old file
                    temp_output.rename(output_path)
                
                file_size_gb = output_path.stat().st_size / (1024**3)
                logger.info(f"✅ Episode final mux successful!")
                logger.info(f"   Output: {output_path.name} ({file_size_gb:.2f} GB)")
                
                # Clean up intermediate episode files (keep only the final muxed file, same as movies)
                files_to_remove = []
                if len(video_files) > 1 or video_files[0] != output_path:
                    files_to_remove.extend(video_files)
                files_to_remove.extend(srt_files + sup_files + audio_files)
                
                for file_to_remove in files_to_remove:
                    if file_to_remove != output_path and file_to_remove.exists():
                        try:
                            file_to_remove.unlink()
                            logger.debug(f"🗑️ Removed episode file: {file_to_remove.name}")
                        except Exception as e:
                            logger.warning(f"⚠️ Failed to remove episode file {file_to_remove.name}: {e}")
                
                return True
            else:
                logger.error("❌ Episode output file was not created or is empty")
                return False
        else:
            logger.error(f"❌ Episode MKVMerge failed with return code {result.returncode}")
            if result.stderr:
                logger.error(f"Episode error output: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in episode mux operation: {e}")
        return False


def create_episode_mux_markers(episode_dir: Path) -> bool:
    """
    Create status markers after successful episode mux.

    TV Show equivalent of create_mux_markers() with episode-specific handling:
    - Episode-based marker management
    - Same marker logic as movies but episode-aware
    - Episode completion tracking

    Args:
        episode_dir: Path to episode directory

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create .muxed marker for episode (same format as movies)
        muxed_marker = episode_dir / '.muxed'
        muxed_marker.write_text(json.dumps({
            'timestamp': datetime.now().isoformat(),
            'stage': 'muxed',
            'ready_for_poster_selection': True,
            'episode_info': {
                'series_name': episode_dir.parent.parent.name,
                'season_name': episode_dir.parent.name,
                'episode_name': episode_dir.name
            }
        }, indent=2))

        # Remove old episode markers that are no longer needed (same logic as movies)
        old_markers = ['.encoded', '.subtitle_complete', '.subtitle_processing_pending']
        for marker in old_markers:
            marker_file = episode_dir / marker
            if marker_file.exists():
                marker_file.unlink()
                logger.debug(f"🗑️ Removed old episode marker: {marker}")

        logger.info(f"✅ Created .muxed status marker for episode")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to create episode markers: {e}")
        return False


def move_episode_to_folder_5(episode_dir: Path):
    """
    Move the muxed episode and marker to folder 5 (awaiting poster).
    
    TV Show equivalent of move_to_folder_5() with episode-specific handling:
    - Series/season/episode organization in folder 5
    - Episode-specific file management
    - Same folder structure as movies but episode-aware

    Args:
        episode_dir: Path to current episode directory in folder 4
    """
    try:
        # Get episode organization info
        series_name = episode_dir.parent.parent.name  # Series is 2 levels up
        season_name = episode_dir.parent.name  # Season is 1 level up
        episode_name = episode_dir.name
        
        # Create folder 5 destination for TV shows (awaiting poster)
        folder5_base = Path("workspace") / "5_awaiting_poster"
        folder5_dest = folder5_base / "tv_shows" / series_name / season_name / episode_name
        folder5_dest.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"📁 Moving episode to folder 5: {series_name} - {season_name} - {episode_name}")

        # Move the final episode MKV file
        mkv_files = list(episode_dir.glob("*.mkv"))
        if mkv_files:
            final_mkv = mkv_files[0]  # Should be only one after mux
            dest_mkv = folder5_dest / final_mkv.name
            shutil.move(str(final_mkv), str(dest_mkv))
            logger.info(f"  📁 Moved episode MKV: {final_mkv.name} → folder 5")

        # Move the .muxed marker for episode
        muxed_marker = episode_dir / ".muxed"
        if muxed_marker.exists():
            dest_marker = folder5_dest / ".muxed"
            shutil.move(str(muxed_marker), str(dest_marker))
            logger.info(f"  📁 Moved episode marker: .muxed → folder 5")

        # Remove the now-empty folder 4 episode directory
        try:
            episode_dir.rmdir()
            logger.info(f"  🗑️ Removed empty folder 4 episode directory: {episode_name}")
        except OSError:
            # Directory not empty - log what's left
            remaining_files = list(episode_dir.iterdir())
            logger.warning(f"  ⚠️ Folder 4 episode directory not empty: {[f.name for f in remaining_files]}")

        logger.info(f"✅ Successfully moved episode {episode_name} to folder 5 (tv_shows/{series_name}/{season_name})")

    except Exception as e:
        logger.error(f"Failed to move episode to folder 5: {e}")
        raise


async def run_tv_final_mux_stage(episodes_data_list: list, settings: dict, main_logger: logging.Logger, mcp_manager=None) -> bool:
    """
    TV Show equivalent of main final mux stage with episode-specific processing.
    
    Filesystem-First Stage 06: TV Episode Final Mux with sophisticated track management

    Same advanced MKVMerge capabilities as movies but for TV episodes:
    - Episode-based discovery and processing
    - Series/season directory organization  
    - Same audio/subtitle/video mux sophistication as movies
    - Episode-specific naming and file management
    - Same marker-based state management as movies

    Args:
        episodes_data_list: Ignored - stage works with filesystem scanning
        settings: Pipeline settings
        main_logger: Logger instance
        mcp_manager: MCP manager instance (optional)

    Returns:
        bool: True if stage completed successfully, False otherwise
    """
    global logger
    logger = main_logger

    logger.info("===== Starting TV Episode Final Mux Stage =====")
    logger.info(f"🔍 DEBUG: Received {len(episodes_data_list)} episodes for final mux")

    try:
        # Initialize filesystem state manager
        workspace_root = Path.cwd()
        from utils.filesystem_first_state_manager import FilesystemFirstStateManager
        state_manager = FilesystemFirstStateManager(workspace_root)

        # Discover episodes ready for final mux using filesystem scanning
        logger.info("🔍 Discovering episodes ready for final mux...")
        ready_episodes = discover_episodes_ready_for_mux()

        if not ready_episodes:
            logger.info("No TV episodes currently need final mux.")
            logger.info("All episodes are either completed or don't have processed files.")
            return True

        logger.info(f"Found {len(ready_episodes)} episode(s) for final mux:")
        for episode_dir in ready_episodes:
            series_name = episode_dir.parent.parent.name
            season_name = episode_dir.parent.name
            episode_name = episode_dir.name
            logger.info(f"  📺 {series_name} - {season_name} - {episode_name}")

        # Process each episode's final mux
        successful_count = 0
        failed_count = 0

        for episode_dir in ready_episodes:
            series_name = episode_dir.parent.parent.name
            season_name = episode_dir.parent.name
            episode_name = episode_dir.name
            
            logger.info(f"\n{'='*40}")
            logger.info(f"Processing episode: {series_name} - {season_name} - {episode_name}")
            logger.info(f"{'='*40}")

            try:
                # Create .mux_processing marker for episode
                processing_marker = episode_dir / '.mux_processing'
                processing_marker.write_text(json.dumps({
                    'started': datetime.now().isoformat(),
                    'stage': 'mux_processing',
                    'series_name': series_name,
                    'season_name': season_name,
                    'episode_name': episode_name
                }, indent=2))
                logger.debug(f"Created .mux_processing marker for episode")

                # Mux the episode files (same sophistication as movies)
                mux_success = mux_episode_files(episode_dir, settings)

                if mux_success:
                    # Create episode status markers
                    marker_success = create_episode_mux_markers(episode_dir)

                    if marker_success:
                        # Move episode to folder 5 for poster selection
                        try:
                            move_episode_to_folder_5(episode_dir)
                            logger.info(f"✅ Successfully processed and moved episode to folder 5: {episode_name}")
                            successful_count += 1
                        except Exception as e:
                            logger.error(f"❌ Failed to move episode to folder 5: {episode_name} - {e}")
                            failed_count += 1
                    else:
                        logger.error(f"❌ Episode mux succeeded but marker creation failed: {episode_name}")
                        failed_count += 1
                else:
                    logger.error(f"❌ Episode mux failed: {episode_name}")
                    failed_count += 1

                # Remove processing marker
                if processing_marker.exists():
                    processing_marker.unlink()

            except Exception as e:
                logger.error(f"❌ Error processing episode {episode_name}: {e}")
                failed_count += 1

                # Remove processing marker on error
                processing_marker = episode_dir / '.mux_processing'
                if processing_marker.exists():
                    processing_marker.unlink()

        # Final logging
        logger.info(f"\n{'='*60}")
        logger.info(f"📊 TV Episode Final Mux Summary:")
        logger.info(f"   ✅ Successful: {successful_count}")
        logger.info(f"   ❌ Failed: {failed_count}")
        logger.info(f"   📁 Total processed: {successful_count + failed_count}")

        if successful_count > 0:
            logger.info(f"\n🎉 {successful_count} episodes are now ready for poster selection!")
            logger.info(f"📝 Episodes moved to folder 5 (awaiting poster)")

        return failed_count == 0

    except Exception as e:
        logger.error(f"Critical error in TV episode final mux stage: {e}")
        return False


def display_interactive_menu():
    """
    Display the main interactive menu for content type selection.

    Returns:
        str: Selected content type ('movies', 'tv_shows', 'both', 'quit')
    """
    print(f"\n{'='*60}")
    print(f"🎬📺 PlexMovieAutomator - Interactive Content Selection")
    print(f"{'='*60}")
    print(f"\nWhat type of content would you like to process?")
    print(f"  1. Movies only")
    print(f"  2. TV Shows only")
    print(f"  3. Both Movies and TV Shows")
    print(f"  4. Quit")

    while True:
        try:
            choice = input(f"\nEnter your choice [1-4]: ").strip()

            if choice == '1':
                return 'movies'
            elif choice == '2':
                return 'tv_shows'
            elif choice == '3':
                return 'both'
            elif choice == '4':
                return 'quit'
            else:
                print(f"Please enter a number between 1 and 4")

        except KeyboardInterrupt:
            print(f"\n👋 Exiting...")
            return 'quit'


def main():
    """Main function for filesystem-first final mux."""
    logger.info("🎬 Starting Filesystem-First Final Mux (Script 6)")
    logger.info("=" * 60)

    # Check for command-line arguments or use interactive selection
    import sys
    args = getattr(sys, '_final_mux_args', None)
    
    if args and (args.movies_only or args.tv_only or args.all):
        # Command line mode
        if args.movies_only:
            content_type_choice = 'movies'
        elif args.tv_only:
            content_type_choice = 'tv_shows'
        else:  # args.all
            content_type_choice = 'both'
        logger.info(f"Command-line mode: Processing {content_type_choice}")
    else:
        # Interactive content type selection (default)
        content_type_choice = display_interactive_menu()
        
        if content_type_choice == 'quit':
            logger.info("👋 User chose to quit")
            return

    # Load settings from settings.ini
    try:
        import sys
        sys.path.append(str(Path(__file__).parent / "_internal"))
        from utils.common_helpers import load_settings

        settings = load_settings("_internal/config/settings.ini")
        logger.info("📋 Settings loaded from settings.ini")
    except Exception as e:
        logger.warning(f"⚠️ Failed to load settings: {e}, using defaults")
        settings = {
            'Executables': {
                'mkvmerge_path': 'C:/Program Files/MKVToolNix/mkvmerge.exe'
            }
        }

    # Process based on user selection
    successful = 0
    failed = 0

    if content_type_choice in ['movies', 'both']:
        # Discover movies ready for mux
        ready_movies = discover_movies_ready_for_mux()
        
        if ready_movies:
            logger.info(f"🎬 Processing {len(ready_movies)} movies for final mux")
            for movie_dir in ready_movies:
                logger.info(f"\n{'='*40}")
                logger.info(f"Processing: {movie_dir.name}")
                logger.info(f"{'='*40}")

                try:
                    # Mux the movie files
                    mux_success = mux_movie_files(movie_dir, settings)

                    if mux_success:
                        # Create status markers
                        marker_success = create_mux_markers(movie_dir)

                        if marker_success:
                            # Move to folder 5 for poster selection
                            try:
                                move_to_folder_5(movie_dir)
                                logger.info(f"✅ Successfully processed and moved to folder 5: {movie_dir.name}")
                                successful += 1
                            except Exception as e:
                                logger.error(f"❌ Failed to move to folder 5: {movie_dir.name} - {e}")
                                failed += 1
                        else:
                            logger.error(f"❌ Mux succeeded but marker creation failed: {movie_dir.name}")
                            failed += 1
                    else:
                        logger.error(f"❌ Mux failed: {movie_dir.name}")
                        failed += 1

                except Exception as e:
                    logger.error(f"❌ Error processing {movie_dir.name}: {e}")
                    failed += 1
        else:
            logger.info("✅ No movies ready for final mux")

    if content_type_choice in ['tv_shows', 'both']:
        # Discover TV shows ready for mux
        ready_tv_shows = discover_tv_shows_ready_for_mux()
        
        if ready_tv_shows:
            logger.info(f"📺 Processing {len(ready_tv_shows)} TV episodes for final mux")
            for episode_dir in ready_tv_shows:
                logger.info(f"\n{'='*40}")
                logger.info(f"Processing: {episode_dir.name}")
                logger.info(f"{'='*40}")

                try:
                    # Mux the TV episode files (using similar logic as movies)
                    mux_success = mux_tv_episode_files(episode_dir, settings)

                    if mux_success:
                        # Create status markers
                        marker_success = create_tv_mux_markers(episode_dir)

                        if marker_success:
                            # Move to next stage for TV episodes
                            try:
                                move_tv_to_next_stage(episode_dir)
                                logger.info(f"✅ Successfully processed TV episode: {episode_dir.name}")
                                successful += 1
                            except Exception as e:
                                logger.error(f"❌ Failed to move TV episode: {episode_dir.name} - {e}")
                                failed += 1
                        else:
                            logger.error(f"❌ TV Mux succeeded but marker creation failed: {episode_dir.name}")
                            failed += 1
                    else:
                        logger.error(f"❌ TV Mux failed: {episode_dir.name}")
                        failed += 1

                except Exception as e:
                    logger.error(f"❌ Error processing TV episode {episode_dir.name}: {e}")
                    failed += 1
        else:
            logger.info("✅ No TV episodes ready for final mux")

    # Summary
    content_type_str = {
        'movies': 'Movies',
        'tv_shows': 'TV Shows', 
        'both': 'Movies and TV Shows'
    }.get(content_type_choice, 'Content')
    
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 Final Mux Summary for {content_type_str}:")
    logger.info(f"   ✅ Successful: {successful}")
    logger.info(f"   ❌ Failed: {failed}")
    logger.info(f"   📁 Total processed: {successful + failed}")

    if successful > 0:
        if content_type_choice in ['movies', 'both']:
            logger.info(f"\n🎉 Movies are now ready for poster selection!")
            logger.info(f"📝 Run 07_poster_handler.py to select posters")
        if content_type_choice in ['tv_shows', 'both']:
            logger.info(f"\n📺 TV episodes are now ready for next stage!")
            logger.info(f"📝 Run 07_poster_handler.py for TV show poster processing")


if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Final Mux Script - Pipeline 06')
    parser.add_argument('--movies-only', action='store_true',
                       help='Process only movies (command-line mode)')
    parser.add_argument('--tv-only', action='store_true',
                       help='Process only TV shows (command-line mode)')
    parser.add_argument('--all', action='store_true',
                       help='Process both movies and TV shows (command-line mode)')
    
    args = parser.parse_args()
    
    print("🎬 Final Mux - Pipeline 06")
    print("   Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)")
    
    # Store args globally so main() can access them
    import sys
    sys._final_mux_args = args
    
    main()
