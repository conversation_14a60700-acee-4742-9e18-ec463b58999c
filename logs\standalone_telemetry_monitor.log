
=== STANDALONE TELEMETRY MONITOR STARTED ===
Started by Script 1 when first download was detected
Uses the EXACT SAME telemetry system as Script 1
==================================================

2025-09-15 02:45:55,278 - standalone_telemetry_monitor - INFO - ===== Starting Standalone Telemetry Monitor =====
2025-09-15 02:45:55,278 - standalone_telemetry_monitor - INFO - This uses the EXACT SAME telemetry system as Script 1
2025-09-15 02:45:55,280 - standalone_telemetry_monitor - INFO - Settings loaded successfully
2025-09-15 02:45:55,280 - standalone_telemetry_monitor - INFO - Telemetry verbose mode: False
2025-09-15 02:45:55,280 - standalone_telemetry_monitor - INFO - \U0001f504 Real-time telemetry system initialized
2025-09-15 02:45:55,281 - standalone_telemetry_monitor - INFO - \U0001f52c Enhanced telemetry integration initialized
2025-09-15 02:45:55,281 - standalone_telemetry_monitor - INFO -    \U0001f4ca Loaded 78 existing movie records
2025-09-15 02:45:55,281 - standalone_telemetry_monitor - INFO - \U0001f52c Real-time telemetry initialized - ready for monitoring
2025-09-15 02:45:55,281 - standalone_telemetry_monitor - WARNING - \u26a0\ufe0f Telemetry initialization failed: 'charmap' codec can't encode character '\U0001f52c' in position 0: character maps to <undefined>
2025-09-15 02:45:55,281 - standalone_telemetry_monitor - ERROR - \u274c Fatal error in standalone telemetry monitor: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>
INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
Traceback (most recent call last):
  File "C:\Users\<USER>\Videos\PlexAutomator\standalone_telemetry_monitor.py", line 57, in run_standalone_telemetry_monitor
    print("\U0001f52c Real-time download monitoring enabled (dashboard mode)")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f52c' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Videos\PlexAutomator\standalone_telemetry_monitor.py", line 60, in run_standalone_telemetry_monitor
    print("\u26a0\ufe0f Telemetry initialization failed")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Videos\PlexAutomator\standalone_telemetry_monitor.py", line 105, in <module>
    success = asyncio.run(run_standalone_telemetry_monitor())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Videos\PlexAutomator\standalone_telemetry_monitor.py", line 96, in run_standalone_telemetry_monitor
    print(f"\u274c Fatal error: {e}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Videos\PlexAutomator\standalone_telemetry_monitor.py", line 111, in <module>
    print(f"\u274c Unexpected error: {e}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
2025-09-15 02:45:55,309 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000022EDA0C9160>
