
=== STANDALONE TELEMETRY MONITOR STARTED ===
Started by Script 1 when first download was detected
Uses the EXACT SAME telemetry system as Script 1
==================================================

2025-09-15 02:48:05,517 - standalone_telemetry_monitor - INFO - ===== Starting Standalone Telemetry Monitor =====
2025-09-15 02:48:05,517 - standalone_telemetry_monitor - INFO - This uses the EXACT SAME telemetry system as Script 1
2025-09-15 02:48:05,518 - standalone_telemetry_monitor - INFO - Settings loaded successfully
2025-09-15 02:48:05,518 - standalone_telemetry_monitor - INFO - Telemetry verbose mode: False
2025-09-15 02:48:05,519 - standalone_telemetry_monitor - INFO - \U0001f504 Real-time telemetry system initialized
2025-09-15 02:48:05,519 - standalone_telemetry_monitor - INFO - \U0001f52c Enhanced telemetry integration initialized
2025-09-15 02:48:05,519 - standalone_telemetry_monitor - INFO -    \U0001f4ca Loaded 80 existing movie records
2025-09-15 02:48:05,519 - standalone_telemetry_monitor - INFO - \U0001f52c Real-time telemetry initialized - ready for monitoring
2025-09-15 02:48:05,519 - standalone_telemetry_monitor - INFO - \U0001f680 Starting telemetry monitoring (same system as Script 1)
2025-09-15 02:48:05,519 - standalone_telemetry_monitor - INFO - \U0001f504 Starting real-time download monitoring (interval: 5s)
2025-09-15 02:48:05,519 - standalone_telemetry_monitor - ERROR - Error monitoring downloads: 'charmap' codec can't encode character '\U0001f389' in position 0: character maps to <undefined>
2025-09-15 02:48:05,519 - standalone_telemetry_monitor - WARNING - \u26a0\ufe0f Telemetry monitoring ended with issues
2025-09-15 02:48:05,521 - standalone_telemetry_monitor - INFO - \U0001f52c Telemetry session completed
2025-09-15 02:48:05,521 - standalone_telemetry_monitor - INFO - ===== Standalone Telemetry Monitor Complete =====
INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
Real-time download monitoring enabled (dashboard mode)
Telemetry monitoring started - dashboard will update in real-time
Telemetry monitoring ended
