#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PlexMovieAutomator/02_download_and_organize.py

Auto-activates virtual environment if not already active.
"""

import sys
import os
from pathlib import Path

# Set environment variable for UTF-8 encoding
os.environ['PYTHONIOENCODING'] = 'utf-8'

# Auto-activate virtual environment
def ensure_venv():
    """Ensure we're running in the virtual environment"""
    # Get the current script directory
    root_dir = Path(__file__).parent
    venv_python = root_dir / "_internal" / "venv" / "Scripts" / "python.exe"

    # Check if we're already in venv or if venv doesn't exist
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # Already in virtual environment
        return

    if venv_python.exists():
        # Re-run this script with venv python
        import subprocess
        print(f"     Activating virtual environment: {venv_python}")
        result = subprocess.run([str(venv_python)] + sys.argv, cwd=str(root_dir))
        sys.exit(result.returncode)
    else:
        print("       Virtual environment not found, running with system Python")

# Activate venv before any other imports
ensure_venv()

"""
PlexMovieAutomator/src/02_download_and_organize.py

UNIFIED STAGE 02: Modern download monitoring using Radarr API integration.

This unified script replaces multiple O2 files with one clean, working implementation:
- Monitors Radarr for download progress and completion
- Updates pipeline state when movies complete
- Detects completed movies in Plex directories
- Transitions completed movies to MKV processing stage
- Handles download errors and failures

Consolidates: 02_modern_download_monitor.py + 02_radarr_download_monitor.py
"""

# Setup paths for clean imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

# Import terminal logger
from utils.terminal_logger import start_terminal_logging

import sys
import json
import re
import logging
import asyncio
import aiohttp
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any
import hashlib
import json
import time

import re

# === METADATA CLEANING HELPERS ===

def _title_looks_messy(title: str) -> bool:
    """Check if a title looks like a release name instead of a clean title"""
    if not title:
        return False

    # Indicators that this is a release name, not a clean title
    release_indicators = [
        '.', 'bluray', 'web-dl', '1080p', '2160p', '720p', 'h264', 'h265', 'x264', 'x265',
        'remux', 'truehd', 'atmos', 'dts', 'avc', 'hevc', 'remaster', 'extended', 'directors',
        'S01E', 'S02E', 'S1E', 'S2E'  # Episode info in series titles
    ]

    lower_title = title.lower()
    return any(indicator in lower_title for indicator in release_indicators)

def _clean_messy_title(title: str) -> str:
    """Clean a messy release title to extract the core title"""
    if not title:
        return title

    import re

    # For TV shows: Remove episode info from series names
    # "The.Office.US.S01E01....AVC.DD5.1-NOGRP" -> "The Office US"
    if re.search(r'S\d{1,2}E\d{1,2}', title, re.IGNORECASE):
        # Extract everything before the first season/episode marker
        match = re.search(r'^(.+?)\.S\d{1,2}', title, re.IGNORECASE)
        if match:
            clean_title = match.group(1)
            # Clean up the extracted part
            clean_title = re.sub(r'\.', ' ', clean_title)
            clean_title = re.sub(r'\s+', ' ', clean_title).strip()
            return clean_title

    # For movies: Extract title before year and tech info
    # "The.Matrix.1999.Remaster.BluRay..." -> "The Matrix"
    year_match = re.search(r'(\d{4})', title)
    if year_match:
        year_pos = year_match.start()
        clean_title = title[:year_pos].strip()
        clean_title = re.sub(r'\.+$', '', clean_title)  # Remove trailing dots
        clean_title = re.sub(r'\.', ' ', clean_title)   # Replace dots with spaces
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()  # Clean up spaces
        if clean_title and len(clean_title) > 2:
            # Fix unmatched parentheses and remove common release artifacts
            clean_title = _fix_unmatched_parentheses(clean_title)
            # Remove patterns like " ()" or " ( )" 
            clean_title = re.sub(r'\s*\(\s*\)\s*', ' ', clean_title)
            clean_title = re.sub(r'\s+', ' ', clean_title).strip()
            return clean_title

    # Fallback: Just clean up dots and common issues
    clean_title = re.sub(r'\.', ' ', title)
    clean_title = re.sub(r'\s+', ' ', clean_title).strip()
    # Fix unmatched parentheses and clean up empty parentheses
    clean_title = _fix_unmatched_parentheses(clean_title)
    clean_title = re.sub(r'\s*\(\s*\)\s*', ' ', clean_title)
    clean_title = re.sub(r'\s+', ' ', clean_title).strip()
    return clean_title


def _fix_unmatched_parentheses(title: str) -> str:
    """Fix unmatched parentheses in titles"""
    if not title:
        return title
    
    # Count opening and closing parentheses
    open_count = title.count('(')
    close_count = title.count(')')
    
    # If unmatched, fix them
    if open_count > close_count:
        # Add missing closing parentheses
        title += ')' * (open_count - close_count)
    elif close_count > open_count:
        # Remove extra closing parentheses from the end
        extra_closes = close_count - open_count
        title = title.rstrip(')')
        # Add back the correct number of closing parentheses
        title += ')' * (close_count - extra_closes)
    
    return title

async def _query_sonarr_for_show(show_name: str, settings_dict: dict, logger_instance) -> dict | None:
    """
    Query Sonarr API to get authoritative show title and year using integration wrapper with retries.
    """
    try:
        from _internal.src.sonarr_integration import SonarrClient
        sonarr_url = settings_dict.get('Sonarr', {}).get('url', 'http://localhost:8989')
        sonarr_api_key = settings_dict.get('Sonarr', {}).get('api_key')
        if not sonarr_api_key:
            logger_instance.warning("No Sonarr API key configured - cannot query for show data")
            return None
        client = SonarrClient(sonarr_url, sonarr_api_key)
        import aiohttp
        async with aiohttp.ClientSession() as session:
            results = await client.series_lookup(session, show_name)
            if results:
                show = results[0]
                return {
                    'title': show.get('title', show_name),
                    'year': show.get('year'),
                    'tvdb_id': show.get('tvdbId'),
                    'imdb_id': show.get('imdbId')
                }
            else:
                logger_instance.warning(f"Sonarr found no matches for: {show_name}")
                return None
    except Exception as e:
        logger_instance.warning(f"Error querying Sonarr for '{show_name}': {e}")
        return None

async def _get_clean_title_from_api(messy_title: str, original_analysis: dict, settings_dict: dict, logger_instance) -> str:
    """Try to get clean title by querying Sonarr/Radarr API directly"""
    try:
        import aiohttp

        content_type = original_analysis.get('content_type', 'unknown')

        if content_type == 'movie':
            # Query Radarr API
            radarr_config = settings_dict.get('Radarr', {})
            radarr_url = radarr_config.get('url', 'http://localhost:7878')
            radarr_key = radarr_config.get('api_key')

            if radarr_key:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{radarr_url}/api/v3/movie",
                                         headers={'X-Api-Key': radarr_key},
                                         timeout=aiohttp.ClientTimeout(total=5)) as response:
                        if response.status == 200:
                            movies = await response.json()
                            # Look for movie with file that matches our messy title
                            for movie in movies:
                                if movie.get('hasFile') and movie.get('movieFile'):
                                    file_path = movie['movieFile'].get('path', '')
                                    if messy_title in file_path or any(word in file_path.lower() for word in messy_title.lower().split('.')[:3]):
                                        clean_title = movie.get('title', '')
                                        if clean_title and not _title_looks_messy(clean_title):
                                            return clean_title

        elif content_type == 'tv_show':
            # Query Sonarr API
            sonarr_config = settings_dict.get('Sonarr', {})
            sonarr_url = sonarr_config.get('url', 'http://localhost:8989')
            sonarr_key = sonarr_config.get('api_key')

            if sonarr_key:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{sonarr_url}/api/v3/series",
                                         headers={'X-Api-Key': sonarr_key},
                                         timeout=aiohttp.ClientTimeout(total=5)) as response:
                        if response.status == 200:
                            series = await response.json()
                            # Look for series that matches our messy title
                            for show in series:
                                show_title = show.get('title', '').lower()
                                # Extract series name from messy title for matching
                                series_part = messy_title.split('.S')[0] if '.S' in messy_title else messy_title
                                series_words = [w for w in series_part.lower().replace('.', ' ').split() if len(w) > 2]

                                # Check if this series matches
                                if series_words and any(word in show_title for word in series_words[:2]):
                                    clean_title = show.get('title', '')
                                    if clean_title and not _title_looks_messy(clean_title):
                                        return clean_title

    except Exception as e:
        logger_instance.warning(f"Failed to query API for clean title: {e}")

    return None

# === LONG PATH HANDLING CONSTANTS AND FUNCTIONS ===

LONG_PATH_THRESHOLD = 235   # headroom before 260 (gives space for later Season/Show dirs)
MIN_DOWNLOAD_AGE_SEC = 90   # avoid renaming while SABnzbd still writing
MAPPING_FILENAME = "long_path_renames.json"  # stored under _internal/data

def _windows_path_len(p: Path) -> int:
    return len(str(p))

def _is_still_writing(p: Path) -> bool:
    try:
        latest = max(f.stat().st_mtime for f in p.rglob("*") if f.is_file())
    except ValueError:
        return False
    age = datetime.now(timezone.utc).timestamp() - latest
    return age < MIN_DOWNLOAD_AGE_SEC

_folder_rx = re.compile(
    r'^(?P<title>.+?)\.S(?P<season>\d{1,2})E(?P<ep>\d{2})(?:[-E](?P<end_ep>\d{2}))?',
    re.IGNORECASE
)

def create_short_name_from_original(original: str) -> str:
    """
    Create a shorter name that preserves enough info for the pipeline to work correctly.
    Goal: Create something like "Detective.Conan.S01E01.1080p.WEB-DL" that's short enough
    but still parseable by the normal TV show pipeline to end up as:
    "Detective Conan/Season 01/S01E01.mkv"

    Idempotent: if pattern already applied (ends with _[0-9a-f]{6}), return original.
    """
    if re.search(r'_[0-9a-f]{6}$', original):
        return original  # already shortened

    m = _folder_rx.match(original)
    if not m:
        # Fallback: take first 25 alnum chars + hash
        base = re.sub(r'[^A-Za-z0-9]+', '_', original)[:25].strip("_")
        h = hashlib.sha1(original.encode()).hexdigest()[:6]
        return f"{base}_{h}" if base else h

    title = m.group("title")
    season = m.group("season").zfill(2)
    ep = m.group("ep")
    end_ep = m.group("end_ep")

    # Create a generic short but parseable format for all shows
    clean_title = re.sub(r'[^A-Za-z0-9]+', '.', title)[:20].strip(".")
    ep_part = f"S{season}E{ep}"
    if end_ep:
        ep_part += f"-E{end_ep}"

    return f"{clean_title}.{ep_part}.1080p.WEB-DL"

def _shorten_video_filename(file: Path, short_base: str) -> Path:
    """
    Rename video file to match the shortened folder name format.
    For a folder like "Detective.Conan.S01E01.1080p.WEB-DL",
    create "Detective.Conan.S01E01.1080p.WEB-DL.mkv" file.
    """
    ext = file.suffix
    return file.with_name(f"{short_base}{ext}")

def _load_rename_mapping(mapping_path: Path) -> dict:
    if mapping_path.exists():
        try:
            return json.loads(mapping_path.read_text(encoding="utf-8"))
        except Exception:
            return {}
    return {}

def _save_rename_mapping(mapping_path: Path, mapping: dict):
    try:
        mapping_path.write_text(json.dumps(mapping, indent=2), encoding="utf-8")
    except Exception:
        pass

def handle_long_paths_in_complete_raw(complete_dir: Path):
    """
    Scan top-level completed items. If any folder or contained file path length
    exceeds threshold, rename folder + primary video file to a compact form.
    Writes mapping to _internal/data/long_path_renames.json for traceability.
    Safe:
      - Skips active downloads (recent mtime).
      - Idempotent (detects prior shortening).
      - Hash avoids collisions.
    """
    # New location under _internal/data; migrate from old path if present
    data_dir = Path(__file__).parent / "_internal" / "data"
    data_dir.mkdir(parents=True, exist_ok=True)
    new_mapping_path = data_dir / MAPPING_FILENAME

    old_mapping_path = Path(__file__).parent / MAPPING_FILENAME
    if old_mapping_path.exists() and not new_mapping_path.exists():
        try:
            old_mapping_path.replace(new_mapping_path)
        except Exception:
            # Fallback to copy then remove
            try:
                new_mapping_path.write_text(old_mapping_path.read_text(encoding="utf-8"), encoding="utf-8")
                old_mapping_path.unlink(missing_ok=True)
            except Exception:
                pass

    mapping_path = new_mapping_path
    rename_map = _load_rename_mapping(mapping_path)

    for item in complete_dir.iterdir():
        if not item.is_dir():
            continue

        # Debug: Show absolute folder path for debugging
        folder_absolute_path = str(item.resolve())
        print(f"  DEBUG: Checking folder: {item.name}")
        print(f"  DEBUG: Absolute path: {folder_absolute_path} ({len(folder_absolute_path)} chars)")

        # Check if the full paths (including contained files) are problematic
        long_candidate = False

        # Check the folder path itself
        folder_path_len = len(str(item.resolve()))
        if folder_path_len > LONG_PATH_THRESHOLD:
            long_candidate = True
            logger.info(f"    LONG PATH DETECTED: Folder itself is {folder_path_len} chars")

        # More importantly: check if any files inside would exceed Windows limit
        try:
            for vid in item.glob("*.mkv"):
                # Use absolute path to get the real Windows path length
                vid_absolute_path = str(vid.resolve())
                full_vid_path_len = len(vid_absolute_path)
                print(f"    DEBUG: MKV file: {vid.name}")
                print(f"    DEBUG: Absolute MKV path: {vid_absolute_path} ({full_vid_path_len} chars)")
                if full_vid_path_len > 250:  # Close to Windows 260 limit
                    long_candidate = True
                    print(f"    *** LONG PATH DETECTED: {vid.name} has full path {full_vid_path_len} chars ***")
                    logger.info(f"          LONG PATH DETECTED: {vid.name} has full path {full_vid_path_len} chars")
                    break
        except (OSError, PermissionError):
            # If we can't even scan the directory, it's probably a path issue
            long_candidate = True

        if not long_candidate:
            continue
        if _is_still_writing(item):
            continue

        original_name = item.name
        short_name = create_short_name_from_original(original_name)
        if short_name == original_name:
            # Already shortened
            continue

        target = item.parent / short_name
        if target.exists():
            # Extremely unlikely due to hash; append different hash if needed
            alt = f"{short_name[:-6]}{hashlib.sha1((original_name+'x').encode()).hexdigest()[:6]}"
            target = item.parent / alt

        try:
            item.rename(target)
            print(f"📁 RENAMED LONG PATH: {original_name} → {short_name}")
        except OSError:
            continue  # Cannot rename; leave as-is

        # Rename a primary video file (largest) to align with folder for cleaner later parsing
        try:
            from _internal.src.fs_helpers import find_video_files
            videos = find_video_files(target)
            if videos:
                largest = max(videos, key=lambda p: p.stat().st_size)
                new_vid = _shorten_video_filename(largest, short_name)
                if new_vid.name != largest.name and not new_vid.exists():
                    largest.rename(new_vid)
                    print(f"🎬 RENAMED LONG FILE: {largest.name} → {new_vid.name}")
        except Exception:
            pass

        rename_map[str(target)] = {
            "original_folder": original_name,
            "short_folder": short_name,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }

    _save_rename_mapping(mapping_path, rename_map)

# === END LONG PATH HANDLING ===

# Import SQLite state manager
try:
    from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
except ImportError:
    # Fallback import path
    sys.path.insert(0, str(Path(__file__).parent / "_internal"))
    from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase

try:
    from _internal.utils.common_helpers import (
        get_path_setting,
        get_setting,
        get_video_resolution,
        generate_plex_movie_name,
        safe_move_file,
        ensure_dir_exists,
        safe_delete_folder
    )
except ImportError:
    try:
        # Try alternative import path
        from utils.common_helpers import (
            get_path_setting,
            get_setting,
            get_video_resolution,
            generate_plex_movie_name,
            safe_move_file,
            ensure_dir_exists,
            safe_delete_folder
        )
    except ImportError:
        # Fallback for testing - create basic implementations
        def get_path_setting(section, key, settings_dict=None, default=None):
            return settings_dict.get(section, {}).get(key, default) if settings_dict else default

        def get_setting(section, key, settings_dict=None, default=None, expected_type=str):
            return settings_dict.get(section, {}).get(key, default) if settings_dict else default

# Import new robust state management
try:
    from _internal.utils.filesystem_first_state_manager import create_filesystem_first_manager
    from _internal.utils.idempotent_helpers import create_idempotent_operations
except ImportError:
    try:
        from _internal.utils.filesystem_first_state_manager import create_filesystem_first_manager
        from utils.idempotent_helpers import create_idempotent_operations
    except ImportError:
        # Fallback - will use legacy system
        create_robust_state_manager = None
        create_idempotent_operations = None

# Import content type detection and TV show naming utilities
try:
    from _internal.utils.content_type_detector import ContentTypeDetector, ContentInfo
    from _internal.utils.tv_show_naming import TVShowNamingHelper, TVShowInfo
except ImportError:
    # Fallback - create basic implementations
    class ContentInfo:
        def __init__(self, content_type, confidence, title=None, year=None):
            self.content_type = content_type
            self.confidence = confidence
            self.title = title
            self.year = year

    class ContentTypeDetector:
        def detect_content_type(self, filename):
            """Detect if content is movie or TV show from folder name"""
            import re
            folder_lower = filename.lower()

            # TV show indicators - comprehensive list including season patterns
            tv_indicators = ['s0', 'season', 'episode', 'e0', 'e1', 'e2', 'e3', 'e4', 'e5', 'e6', 'e7', 'e8', 'e9']
            tv_patterns = [r's\d+e\d+', r'season\s*\d+', r'episode\s*\d+', r's\d+\D', r'\.s\d+\.']

            # Check for TV patterns first (higher priority)
            if any(indicator in folder_lower for indicator in tv_indicators):
                return ContentInfo("tv_show", 0.8)

            for pattern in tv_patterns:
                if re.search(pattern, folder_lower):
                    return ContentInfo("tv_show", 0.8)

            # Movie indicators (year patterns, quality indicators)
            movie_indicators = ['bluray', 'bdrip', 'webrip', 'dvdrip', 'remux']
            quality_indicators = ['1080p', '720p', '4k', '2160p']
            year_pattern = r'\b(19|20)\d{2}\b'

            # Strong movie indicators
            if re.search(year_pattern, folder_lower) and any(indicator in folder_lower for indicator in movie_indicators):
                return ContentInfo("movie", 0.8)

            # Check for year + quality without clear TV indicators (weaker signal)
            if re.search(year_pattern, folder_lower) and any(indicator in folder_lower for indicator in quality_indicators):
                return ContentInfo("movie", 0.6)

            # Default assumption - prefer TV show for ambiguous cases since TV content is more common
            return ContentInfo("tv_show", 0.3)

    class TVShowInfo:
        def __init__(self, series_name, year, season, episode, is_multi_season):
            self.series_name = series_name
            self.year = year
            self.season = season
            self.episode = episode
            self.is_multi_season = is_multi_season

    class TVShowNamingHelper:
        def generate_plex_tv_structure(self, filename):
            return TVShowInfo("Unknown Show", None, 1, 1, False)

# --- Global Logger ---
logger = None

# Original cleanup_sabnzbd_history function has been replaced with enhanced version at line 482


async def cleanup_completed_downloads_from_radarr(movie, radarr_session, radarr_url, headers, logger):
    """
    ENHANCED: Comprehensive cleanup that works with title matching when IDs are missing.

    After successful organization, clean up Radarr queue AND movie record to enable re-downloads.
    Uses multiple matching strategies to find the movie in Radarr.
    """
    try:
        # Get movie identification data
        radarr_id = movie.get("paths", {}).get("radarr_id") if isinstance(movie.get("paths"), dict) else None
        movie_title = movie.get("title") or movie.get("cleaned_title", "Unknown")
        tmdb_id = movie.get("tmdb_id")
        year = movie.get("year")

        logger.info(f"🧹 Starting enhanced Radarr cleanup for: {movie_title}")
        logger.info(f"   Available IDs: radarr_id={radarr_id}, tmdb_id={tmdb_id}, year={year}")

        # STEP 1: Get all Radarr movies and queue items first (with retries via wrapper)
        logger.info(f"🔍 Step 1: Getting Radarr movies and queue...")
        from _internal.src.radarr_integration import RadarrClient
        client = RadarrClient(radarr_url, headers.get('X-Api-Key', ''))
        radarr_movies = await client.get_movies(radarr_session)
        if not isinstance(radarr_movies, list):
            logger.error("   Failed to get Radarr movies")
            return False
        logger.info(f"   Retrieved {len(radarr_movies)} movies from Radarr")
        queue_data = await client.get_queue(radarr_session)
        queue_records = queue_data.get("records", []) if isinstance(queue_data, dict) else []
        logger.info(f"   Retrieved {len(queue_records)} queue items from Radarr")

        # STEP 2: Find matching movie using multiple strategies
        logger.info(f"🎯 Step 2: Finding movie using multiple matching strategies...")

        target_movie = None
        matching_strategy = "none"

        # Strategy 1: Match by Radarr ID (most reliable)
        if radarr_id:
            target_movie = next((m for m in radarr_movies if m.get('id') == int(radarr_id)), None)
            if target_movie:
                matching_strategy = "radarr_id"
                logger.info(f"   ✅ Found by Radarr ID: {radarr_id}")

        # Strategy 2: Match by TMDB ID
        if not target_movie and tmdb_id:
            target_movie = next((m for m in radarr_movies if m.get('tmdbId') == int(tmdb_id)), None)
            if target_movie:
                matching_strategy = "tmdb_id"
                logger.info(f"   ✅ Found by TMDB ID: {tmdb_id}")

        # Strategy 3: Match by title and year (fuzzy matching)
        if not target_movie and movie_title and year:
            movie_title_lower = movie_title.lower().strip()
            for radarr_movie in radarr_movies:
                radarr_title = radarr_movie.get('title', '').lower().strip()
                radarr_year = radarr_movie.get('year')

                # Exact title + year match
                if radarr_title == movie_title_lower and radarr_year == year:
                    target_movie = radarr_movie
                    matching_strategy = "title_year_exact"
                    logger.info(f"   ✅ Found by exact title+year: {radarr_movie.get('title')} ({radarr_year})")
                    break

                # Fuzzy title match (contains) + year match
                elif movie_title_lower in radarr_title and radarr_year == year:
                    target_movie = radarr_movie
                    matching_strategy = "title_year_fuzzy"
                    logger.info(f"   ✅ Found by fuzzy title+year: {radarr_movie.get('title')} ({radarr_year})")
                    break

        # STEP 3: Clean up queue items (even if movie not found in library)
        logger.info(f"🗑️ Step 3: Cleaning up queue items...")

        # Find queue items by movie ID (if we found the movie)
        queue_items_to_remove = []
        if target_movie:
            movie_id = target_movie.get('id')
            queue_items_to_remove = [
                item for item in queue_records
                if item.get("movieId") == movie_id
            ]
            logger.info(f"   Found {len(queue_items_to_remove)} queue items by movie ID")

        # Also find queue items by title matching (backup method)
        title_queue_matches = []
        if movie_title:
            movie_words = set(movie_title.lower().replace('.', ' ').replace('-', ' ').split())
            for item in queue_records:
                item_title = item.get('title', '').lower()
                # If 70% of movie title words appear in queue item title
                item_words = set(item_title.replace('.', ' ').replace('-', ' ').split())
                if len(movie_words & item_words) >= len(movie_words) * 0.7:
                    title_queue_matches.append(item)

            logger.info(f"   Found {len(title_queue_matches)} additional queue items by title matching")

            # Combine and deduplicate
            all_queue_items = queue_items_to_remove + title_queue_matches
            seen_ids = set()
            queue_items_to_remove = []
            for item in all_queue_items:
                item_id = item.get('id')
                if item_id not in seen_ids:
                    queue_items_to_remove.append(item)
                    seen_ids.add(item_id)

        # Remove queue items
        if queue_items_to_remove:
            logger.info(f"   Removing {len(queue_items_to_remove)} queue items...")
            for queue_item in queue_items_to_remove:
                queue_id = queue_item.get("id")
                item_title = queue_item.get("title", "Unknown")
                download_id = queue_item.get("downloadId", "")

                # Delete with removeFromClient=true to also clean SABnzbd
                from _internal.src.radarr_integration import RadarrClient
                client = RadarrClient(radarr_url, headers.get('X-Api-Key', ''))
                ok = await client.delete_queue_item(radarr_session, queue_id, remove_from_client=True, blocklist=False)
                if ok:
                    logger.info(f"   ✅ Removed queue item: {item_title} (ID: {queue_id})")
                else:
                    logger.warning(f"   ❌ Failed to remove queue item {queue_id}")
        else:
            logger.info(f"   No queue items found to remove")

        # STEP 4: Remove movie from library (if found)
        if target_movie:
            movie_id = target_movie.get('id')
            movie_radarr_title = target_movie.get('title')

            logger.info(f"🗑️ Step 4: Removing movie from Radarr library...")
            logger.info(f"   Movie: {movie_radarr_title} (ID: {movie_id}, Strategy: {matching_strategy})")

            # Remove movie file reference first (if exists)
            if target_movie.get('hasFile', False):
                movie_file_id = target_movie.get('movieFile', {}).get('id')
                if movie_file_id:
                    logger.info(f"   Removing movie file reference (ID: {movie_file_id})")
                    delete_file_url = f"{radarr_url}/api/v3/moviefile/{movie_file_id}"

                    from _internal.src.radarr_integration import RadarrClient
                    client = RadarrClient(radarr_url, headers.get('X-Api-Key', ''))
                    ok = await client.delete_movie_file(radarr_session, movie_file_id)
                    if ok:
                        logger.info(f"   ✅ Removed movie file reference")
                    else:
                        logger.warning(f"   ⚠️ Failed to remove movie file reference")

            # Remove movie completely
            from _internal.src.radarr_integration import RadarrClient
            client = RadarrClient(radarr_url, headers.get('X-Api-Key', ''))
            ok = await client.delete_movie(radarr_session, movie_id, delete_files=False, add_import_exclusion=False)
            if ok:
                logger.info(f"   ✅ Completely removed movie from Radarr library")
                logger.info(f"🎉 Radarr cleanup complete for {movie_title}")
                return True
            else:
                logger.warning(f"   ❌ Failed to remove movie from library")
                return False
        else:
            logger.warning(f"   Could not find movie in Radarr library - queue cleanup only")
            return len(queue_items_to_remove) > 0  # Success if we cleaned up queue items

    except Exception as e:
        logger.error(f"Error in enhanced Radarr cleanup for {movie_title}: {e}")
        return False


async def cleanup_sabnzbd_history(movie, settings_dict, logger):
    """
    ENHANCED: Remove movie download history from SABnzbd using multiple matching strategies.

    After successful organization, clean up SABnzbd history to prevent confusion
    and allow clean re-downloads of the same movie.
    """
    try:
        # Get SABnzbd settings
        # Support both flat and nested settings_dict keys
        sabnzbd_url = settings_dict.get("sabnzbd_url") or settings_dict.get("sabnzbd", {}).get("url", "")
        sabnzbd_api_key = settings_dict.get("sabnzbd_api_key") or settings_dict.get("sabnzbd", {}).get("api_key", "")
        sabnzbd_url = (sabnzbd_url or "").rstrip("/")

        if not sabnzbd_url or not sabnzbd_api_key:
            logger.warning(f"SABnzbd URL or API key not configured (sabnzbd_url={sabnzbd_url}, sabnzbd_api_key={sabnzbd_api_key}), skipping history cleanup")
            return False

        movie_title = movie.get("title") or movie.get("cleaned_title", "Unknown")
        nzb_name = movie.get("nzb_name", "")

        logger.info(f"🧹 Starting enhanced SABnzbd history cleanup for: {movie_title}")
        logger.info(f"   NZB name: {nzb_name}")

        import aiohttp

        async with aiohttp.ClientSession() as session:
            # Get SABnzbd history
            history_url = f"{sabnzbd_url}/api"
            params = {
                "mode": "history",
                "output": "json",
                "apikey": sabnzbd_api_key,
                "limit": 100  # Get recent history
            }

            logger.info(f"🔍 Getting SABnzbd history...")
            async with session.get(history_url, params=params) as response:
                if response.status != 200:
                    logger.error(f"   Failed to get SABnzbd history: HTTP {response.status}")
                    return False

                data = await response.json()
                history_slots = data.get("history", {}).get("slots", [])
                logger.info(f"   Retrieved {len(history_slots)} history entries")

                # Find matching history entries using multiple strategies
                matching_entries = []

                # Strategy 1: Exact NZB name match
                if nzb_name:
                    exact_matches = [
                        slot for slot in history_slots
                        if slot.get("name", "").lower() == nzb_name.lower()
                    ]
                    matching_entries.extend(exact_matches)
                    if exact_matches:
                        logger.info(f"   ✅ Found {len(exact_matches)} entries by exact NZB name")

                # Strategy 2: Title-based fuzzy matching
                if movie_title:
                    movie_words = set(movie_title.lower().replace('.', ' ').replace('-', ' ').split())
                    title_matches = []

                    for slot in history_slots:
                        slot_name = slot.get("name", "").lower()
                        slot_words = set(slot_name.replace('.', ' ').replace('-', ' ').split())

                        # If 70% of movie title words appear in slot name
                        if len(movie_words & slot_words) >= len(movie_words) * 0.7:
                            # Avoid duplicates
                            if not any(slot.get("nzo_id") == existing.get("nzo_id") for existing in matching_entries):
                                title_matches.append(slot)

                    matching_entries.extend(title_matches)
                    if title_matches:
                        logger.info(f"   ✅ Found {len(title_matches)} additional entries by title fuzzy match")

                # Remove history entries
                if matching_entries:
                    logger.info(f"🗑️ Removing {len(matching_entries)} SABnzbd history entries...")

                    removed_count = 0
                    for entry in matching_entries:
                        nzo_id = entry.get("nzo_id")
                        entry_name = entry.get("name", "Unknown")

                        if nzo_id:
                            # Delete history entry
                            delete_params = {
                                "mode": "history",
                                "name": "delete",
                                "apikey": sabnzbd_api_key,
                                "value": nzo_id
                            }

                            async with session.get(history_url, params=delete_params) as delete_response:
                                if delete_response.status == 200:
                                    logger.info(f"   ✅ Removed history entry: {entry_name} (ID: {nzo_id})")
                                    removed_count += 1
                                else:
                                    logger.warning(f"   ❌ Failed to remove history entry {nzo_id}: HTTP {delete_response.status}")
                        else:
                            logger.warning(f"   ❌ No NZO ID found for entry: {entry_name}")

                    if removed_count > 0:
                        logger.info(f"🎉 SABnzbd cleanup complete: removed {removed_count} history entries for {movie_title}")
                        return True
                    else:
                        logger.warning(f"Failed to remove any SABnzbd history entries for {movie_title}")
                        return False
                else:
                    logger.info(f"   No matching SABnzbd history entries found for {movie_title}")
                    return True  # No entries to remove is considered success

    except Exception as e:
        logger.error(f"Error in enhanced SABnzbd cleanup for {movie_title}: {e}")
        return False


async def check_sabnzbd_failures(settings_dict, logger):
    """
    Check SABNZBD history for failed downloads and report reasons.
    """
    try:
        from _internal.utils.common_helpers import get_setting

        sabnzbd_url = get_setting("SABnzbd", "url", settings_dict=settings_dict, default="http://localhost:8080")
        sabnzbd_api_key = get_setting("SABnzbd", "api_key", settings_dict=settings_dict)

        if not sabnzbd_api_key:
            logger.warning("SABNZBD API key not configured, cannot check failure history")
            return []

        import aiohttp
        async with aiohttp.ClientSession() as session:
            # Get history with failed downloads
            params = {
                'output': 'json',
                'apikey': sabnzbd_api_key,
                'mode': 'history',
                'limit': 50,  # Check last 50 downloads
                'failed_only': 1  # Only failed downloads
            }

            async with session.get(f"{sabnzbd_url}/api", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    history = data.get('history', {})
                    slots = history.get('slots', [])

                    failed_episodes = []
                    for slot in slots:
                        name = slot.get('name', '')
                        status = slot.get('status', '')
                        fail_message = slot.get('fail_message', '')
                        completed = slot.get('completed', 0)

                        # Check if this looks like a TV episode and failed
                        if status == 'Failed' and ('S0' in name and 'E0' in name):
                            failed_episodes.append({
                                'name': name,
                                'status': status,
                                'fail_message': fail_message,
                                'completed_time': completed
                            })

                            # Log the failure with clear messaging
                            if 'missing' in fail_message.lower() and 'article' in fail_message.lower():
                                logger.warning(f"❌ EPISODE FAILED (Missing Articles): {name}")
                                logger.warning(f"   Reason: {fail_message}")
                            else:
                                logger.warning(f"❌ EPISODE FAILED: {name}")
                                logger.warning(f"   Reason: {fail_message}")

                    return failed_episodes
                else:
                    logger.warning(f"Failed to get SABNZBD history: HTTP {response.status}")
                    return []

    except Exception as e:
        logger.error(f"Error checking SABNZBD failures: {e}")
        return []


async def handle_failed_extractions(download_dir: Path, logger):
    """
    Detect failed extractions and move them to recycling bin.
    Failed extractions are identified by:
    1. Presence of RAR archive parts (.rar, .r00, .r01, etc.) without corresponding video files
    2. Empty directories after presumed extraction
    """
    try:
        recycling_bin = download_dir.parent / "recycling_bin"
        recycling_bin.mkdir(exist_ok=True)
        
        failed_extractions = []
        
        for item in download_dir.iterdir():
            if not item.is_dir():
                continue
                
            # Check for RAR archive files (including multi-part: .rar, .r00, .r01, .r02, etc.)
            rar_patterns = ["*.rar", "*.r[0-9][0-9]"]
            rar_files = []
            for pattern in rar_patterns:
                rar_files.extend(list(item.glob(pattern)))
            
            if not rar_files:
                continue
                
            # Check if there are any video files in the same directory
            from _internal.src.fs_helpers import find_video_files
            video_files = find_video_files(item)
            
            if not video_files:
                # Found RAR archive parts but no video files - extraction likely failed
                logger.warning(f"⚠️ EXTRACTION FAILURE DETECTED: {item.name}")
                
                # Categorize the RAR files for better logging
                main_rar = [f for f in rar_files if f.suffix.lower() == '.rar']
                part_rars = [f for f in rar_files if f.suffix.lower().startswith('.r') and f.suffix.lower() != '.rar']
                
                if main_rar and part_rars:
                    logger.warning(f"   Found multi-part RAR: {len(main_rar)} .rar + {len(part_rars)} parts (.r00, .r01, etc.)")
                elif main_rar:
                    logger.warning(f"   Found {len(main_rar)} .rar files but no video files")
                elif part_rars:
                    logger.warning(f"   Found {len(part_rars)} RAR parts (.r00, .r01, etc.) but no video files")
                
                try:
                    # Move to recycling bin
                    recycling_dest = recycling_bin / item.name
                    if recycling_dest.exists():
                        # Add timestamp to avoid conflicts
                        from datetime import datetime
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        recycling_dest = recycling_bin / f"{item.name}_{timestamp}"
                    
                    import shutil
                    shutil.move(str(item), str(recycling_dest))
                    logger.info(f"🗑️ Moved failed extraction to recycling: {item.name} → {recycling_dest.name}")
                    failed_extractions.append(item.name)
                    
                except Exception as move_error:
                    logger.error(f"Failed to move {item.name} to recycling: {move_error}")
        
        if failed_extractions:
            logger.info(f"🧹 Cleaned up {len(failed_extractions)} failed extractions")
        
        return failed_extractions
        
    except Exception as e:
        logger.error(f"Error handling failed extractions: {e}")
        return []


async def remove_season_packs_from_queue(session, sonarr_url: str, headers: dict, logger):
    """
    Remove any season packs from Sonarr download queue to force individual episode downloads.
    """
    try:
        # Get current queue
        async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers) as response:
            if response.status != 200:
                logger.warning(f"Failed to get Sonarr queue: HTTP {response.status}")
                return

            queue_data = await response.json()
            queue_records = queue_data.get("records", [])

            season_packs_removed = 0
            for item in queue_records:
                title = item.get("title", "")
                queue_id = item.get("id")

                # Check if this is a season pack using the same detection logic
                if is_season_pack(title):
                    logger.warning(f"🚨 REMOVING SEASON PACK from queue: {title}")

                    # Remove from queue
                    async with session.delete(f"{sonarr_url}/api/v3/queue/{queue_id}?removeFromClient=true&blocklist=true",
                                            headers=headers) as del_resp:
                        if del_resp.status in [200, 204]:
                            season_packs_removed += 1
                            logger.info(f"   ✅ Removed and blocklisted: {title}")
                        else:
                            logger.error(f"   ❌ Failed to remove {title}: HTTP {del_resp.status}")

            if season_packs_removed > 0:
                logger.info(f"🧹 Removed {season_packs_removed} season packs from download queue")
            else:
                logger.debug("✅ No season packs found in download queue")

    except Exception as e:
        logger.error(f"Failed to remove season packs from queue: {e}")


def is_season_pack(title: str) -> bool:
    """
    Enhanced season pack detection using comprehensive patterns.
    """
    if not title:
        return False

    import re

    # Enhanced patterns to catch season packs
    season_pack_patterns = [
        r'\bS\d{1,2}(?!\d|\s*[Ee]\d)',           # S01, S02 without episode (main pattern for FLCL.S01)
        r'\bS\d{1,2}\.(?![Ee]\d)',               # S01., S02. (period after season)
        r'\bS\d{1,2}\s+\d{3,4}p',                # S01 1080p, S01 720p
        r'\bSeason[\s\._-]*\d{1,2}',             # Season 1, Season.01
        r'\bComplete[\s\._-]*Season',            # Complete Season
        r'\bFull[\s\._-]*Season',                # Full Season
        r'\bEntire[\s\._-]*Season',              # Entire Season
        r'\bSeries[\s\._-]*Complete',            # Series Complete
        r'\bMulti[\s\._-]*Episode',              # Multi Episode
        r'\bAll[\s\._-]*Episodes',               # All Episodes
        r'\bSeason[\s\._-]*Pack',                # Season Pack
        r'\bS\d{1,2}[\s\._-]*Pack',              # S01 Pack
        r'\bS\d{1,2}[\s\._-]*Complete',          # S01 Complete
        r'\b\d{1,2}x\d{1,2}-\d{1,2}x\d{1,2}',   # 1x01-1x06 ranges
        r'\bE\d{1,2}-E\d{1,2}',                  # E01-E06 ranges
        r'\bE\d{1,2}\s*-\s*\d{1,2}',            # E01-06 ranges
        # ABSOLUTE BLOCKS - These should NEVER be downloaded
        r'FLCL\.S\d{1,2}\.',                     # FLCL.S01., FLCL.S02. etc.
        r'\bS\d{1,2}\.\d{3,4}p',                 # S01.1080p format
        # Patterns for shows like "Ben 10" that include year and season
        r'Ben[\s\._-]*10.*S\d{1,2}(?![Ee]\d)',   # Ben 10 (2005) S01, Ben.10.S01, etc.
        r'(?:.*\(\d{4}\).*)?S\d{1,2}(?![Ee]\d)', # Any show with year and S01 format
    ]

    for pattern in season_pack_patterns:
        if re.search(pattern, title, re.IGNORECASE):
            return True

    return False


async def prevent_season_pack_fallback(session, sonarr_url: str, headers: dict, series_id: int, logger):
    """
    CRITICAL: Ensure no season pack searches are triggered for this series.
    """
    try:
        # Get series details
        async with session.get(f"{sonarr_url}/api/v3/series/{series_id}", headers=headers) as r:
            if r.status != 200:
                return
            series_obj = await r.json()

        # Disable any automatic searches
        changed = False
        if series_obj.get("automaticSearch", True):
            series_obj["automaticSearch"] = False
            changed = True

        # Update series with no automatic searches
        if changed:
            async with session.put(f"{sonarr_url}/api/v3/series/{series_id}",
                                 headers=headers, json=series_obj) as pr:
                if pr.status == 202:
                    title = series_obj.get("title", "Unknown")
                    logger.info(f"[NO-SEASON-PACKS] Disabled automatic searches for {title}")

    except Exception as e:
        logger.warning(f"Failed to prevent season pack fallback: {e}")


async def should_cleanup_sonarr_series(tv_show_info, settings_dict, logger):
    """
    Determine if we should run Sonarr cleanup for a TV series.
    Only cleanup if no other episodes for this series are still downloading/pending.

    Args:
        tv_show_info: TV show information dictionary
        settings_dict: Configuration settings
        logger: Logger instance

    Returns:
        bool: True if safe to cleanup, False if other episodes still pending
    """
    try:
        # Get Sonarr configuration
        sonarr_url = get_setting("Sonarr", "url", settings_dict=settings_dict, default="http://localhost:8989")
        sonarr_api_key = get_setting("Sonarr", "api_key", settings_dict=settings_dict)

        if not sonarr_api_key:
            logger.warning("Sonarr API key not configured - allowing cleanup")
            return True

        # Get series information
        sonarr_id = tv_show_info.get("paths", {}).get("sonarr_id") if isinstance(tv_show_info.get("paths"), dict) else None
        series_title = tv_show_info.get("title") or tv_show_info.get("cleaned_title", "Unknown")
        tvdb_id = tv_show_info.get("tvdb_id")

        headers = {'X-Api-Key': sonarr_api_key}
        timeout = aiohttp.ClientTimeout(total=15)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Get current queue to check for pending downloads
            async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers) as response:
                if response.status == 200:
                    queue_data = await response.json()
                    queue_records = queue_data.get("records", [])
                    logger.info(f"   Checking {len(queue_records)} queue items for pending episodes...")
                else:
                    logger.warning(f"   Failed to get Sonarr queue: HTTP {response.status} - allowing cleanup")
                    return True

            # Find any downloading/pending items for this series
            pending_episodes = []

            # Method 1: Match by series ID if available
            if sonarr_id:
                for item in queue_records:
                    if item.get("seriesId") == int(sonarr_id):
                        status = item.get("status", "").lower()
                        if status in ["downloading", "queued", "pending", "delay"]:
                            pending_episodes.append(item)

            # Method 2: Match by title if series ID not available
            if not pending_episodes and series_title:
                series_words = set(series_title.lower().replace('.', ' ').replace('-', ' ').split())
                for item in queue_records:
                    item_title = item.get('title', '').lower()
                    item_words = set(item_title.replace('.', ' ').replace('-', ' ').split())
                    # If 70% of series title words appear in queue item title
                    if len(series_words & item_words) >= len(series_words) * 0.7:
                        status = item.get("status", "").lower()
                        if status in ["downloading", "queued", "pending", "delay"]:
                            pending_episodes.append(item)

            if pending_episodes:
                logger.info(f"   Found {len(pending_episodes)} episodes still downloading/pending:")
                for ep in pending_episodes:
                    logger.info(f"      - {ep.get('title', 'Unknown')} (Status: {ep.get('status', 'Unknown')})")
                return False
            else:
                logger.info(f"   No pending episodes found - safe to cleanup")
                return True

    except Exception as e:
        logger.warning(f"Error checking series download status: {e} - allowing cleanup")
        return True


async def cleanup_completed_downloads_from_sonarr(tv_show_info, settings_dict, logger):
    """
    ENHANCED: Comprehensive Sonarr cleanup for TV shows equivalent to Radarr cleanup.

    After successful TV show organization, clean up Sonarr queue AND series record to enable re-downloads.
    Uses multiple matching strategies to find the TV show in Sonarr.

    Args:
        tv_show_info: TV show information dictionary
        settings_dict: Configuration settings
        logger: Logger instance

    Returns:
        bool: True if cleanup completed successfully, False otherwise
    """
    try:
        # Get Sonarr configuration
        sonarr_url = get_setting("Sonarr", "url", settings_dict=settings_dict, default="http://localhost:8989")
        sonarr_api_key = get_setting("Sonarr", "api_key", settings_dict=settings_dict)

        if not sonarr_api_key:
            logger.warning("Sonarr API key not configured - skipping Sonarr cleanup")
            return True

        # Get TV show identification data
        sonarr_id = tv_show_info.get("paths", {}).get("sonarr_id") if isinstance(tv_show_info.get("paths"), dict) else None
        series_title = tv_show_info.get("title") or tv_show_info.get("cleaned_title", "Unknown")
        tvdb_id = tv_show_info.get("tvdb_id")
        year = tv_show_info.get("year")

        logger.info(f"🧹 Starting enhanced Sonarr cleanup for: {series_title}")
        logger.info(f"   Available IDs: sonarr_id={sonarr_id}, tvdb_id={tvdb_id}, year={year}")

        headers = {'X-Api-Key': sonarr_api_key}
        timeout = aiohttp.ClientTimeout(total=30)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            # STEP 1: Get all Sonarr series and queue items
            logger.info(f"🔍 Step 1: Getting Sonarr series and queue...")

            # Get current series
            async with session.get(f"{sonarr_url}/api/v3/series", headers=headers) as response:
                if response.status == 200:
                    sonarr_series = await response.json()
                    logger.info(f"   Retrieved {len(sonarr_series)} series from Sonarr")
                else:
                    logger.error(f"   Failed to get Sonarr series: HTTP {response.status}")
                    return False

            # Get current queue
            async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers) as response:
                if response.status == 200:
                    queue_data = await response.json()
                    queue_records = queue_data.get("records", [])
                    logger.info(f"   Retrieved {len(queue_records)} queue items from Sonarr")
                else:
                    logger.error(f"   Failed to get Sonarr queue: HTTP {response.status}")
                    queue_records = []

            # STEP 2: Find matching series using multiple strategies
            logger.info(f"🎯 Step 2: Finding TV series using multiple matching strategies...")

            target_series = None
            matching_strategy = "none"

            # Strategy 1: Match by Sonarr ID (most reliable)
            if sonarr_id:
                target_series = next((s for s in sonarr_series if s.get('id') == int(sonarr_id)), None)
                if target_series:
                    matching_strategy = "sonarr_id"
                    logger.info(f"   ✅ Found by Sonarr ID: {sonarr_id}")

            # Strategy 2: Match by TVDB ID
            if not target_series and tvdb_id:
                target_series = next((s for s in sonarr_series if s.get('tvdbId') == int(tvdb_id)), None)
                if target_series:
                    matching_strategy = "tvdb_id"
                    logger.info(f"   ✅ Found by TVDB ID: {tvdb_id}")

            # Strategy 3: Match by title and year (fuzzy matching)
            if not target_series and series_title and year:
                series_title_lower = series_title.lower().strip()
                for sonarr_series_item in sonarr_series:
                    sonarr_title = sonarr_series_item.get('title', '').lower().strip()
                    sonarr_year = sonarr_series_item.get('year')

                    # Exact title + year match
                    if sonarr_title == series_title_lower and sonarr_year == year:
                        target_series = sonarr_series_item
                        matching_strategy = "title_year_exact"
                        logger.info(f"   ✅ Found by exact title+year: {sonarr_series_item.get('title')} ({sonarr_year})")
                        break

                    # Fuzzy title match (contains) + year match
                    elif series_title_lower in sonarr_title and sonarr_year == year:
                        target_series = sonarr_series_item
                        matching_strategy = "title_year_fuzzy"
                        logger.info(f"   ✅ Found by fuzzy title+year: {sonarr_series_item.get('title')} ({sonarr_year})")
                        break

            # STEP 3: Clean up queue items (even if series not found in library)
            logger.info(f"🗑️ Step 3: Cleaning up queue items...")

            # Find queue items by series ID (if we found the series)
            queue_items_to_remove = []
            if target_series:
                series_id = target_series.get('id')
                # IMPORTANT: For specific episode downloads, only remove COMPLETED items
                # Don't remove items that are still downloading/in progress
                for item in queue_records:
                    if item.get("seriesId") == series_id:
                        # Only remove completed items or failed items, not downloading items
                        status = item.get("status", "").lower()
                        if status in ["completed", "failed", "warning"]:
                            queue_items_to_remove.append(item)
                        else:
                            logger.info(f"   🔄 Keeping in-progress item: {item.get('title', 'Unknown')} (Status: {status})")

                logger.info(f"   Found {len(queue_items_to_remove)} completed/failed queue items to remove by series ID")

            # Also find queue items by title matching (backup method)
            title_queue_matches = []
            if series_title:
                series_words = set(series_title.lower().replace('.', ' ').replace('-', ' ').split())
                for item in queue_records:
                    item_title = item.get('title', '').lower()
                    # If 70% of series title words appear in queue item title
                    item_words = set(item_title.replace('.', ' ').replace('-', ' ').split())
                    if len(series_words & item_words) >= len(series_words) * 0.7:
                        title_queue_matches.append(item)

                logger.info(f"   Found {len(title_queue_matches)} additional queue items by title matching")

                # Combine and deduplicate
                all_queue_items = queue_items_to_remove + title_queue_matches
                seen_ids = set()
                queue_items_to_remove = []
                for item in all_queue_items:
                    item_id = item.get('id')
                    if item_id not in seen_ids:
                        queue_items_to_remove.append(item)
                        seen_ids.add(item_id)

            # Remove queue items
            if queue_items_to_remove:
                logger.info(f"   Removing {len(queue_items_to_remove)} queue items...")
                for queue_item in queue_items_to_remove:
                    queue_id = queue_item.get("id")
                    item_title = queue_item.get("title", "Unknown")
                    download_id = queue_item.get("downloadId", "")

                    # Delete with removeFromClient=true to also clean SABnzbd
                    delete_url = f"{sonarr_url}/api/v3/queue/{queue_id}?removeFromClient=true&blocklist=false"

                    async with session.delete(delete_url, headers=headers) as delete_response:
                        if delete_response.status == 200:
                            logger.info(f"   ✅ Removed queue item: {item_title} (ID: {queue_id})")
                        else:
                            logger.warning(f"   ❌ Failed to remove queue item {queue_id}: HTTP {delete_response.status}")
            else:
                logger.info(f"   No queue items found to remove")

            # STEP 4: Ensure consistent series monitoring status after downloads complete
            # Fix for Priority 5: "Most shows show as unmonitored even after successful downloads"
            if target_series:
                series_id = target_series.get('id')
                series_sonarr_title = target_series.get('title')

                logger.info(f"📺 Step 4: Series found in Sonarr library...")
                logger.info(f"   Series: {series_sonarr_title} (ID: {series_id}, Strategy: {matching_strategy})")

                # CRITICAL FIX: Explicitly ensure series monitoring is enabled for future episodes/seasons
                logger.info(f"   🔧 Verifying and restoring series monitoring status...")

                # Get current series data to check monitoring status
                async with session.get(f"{sonarr_url}/api/v3/series/{series_id}", headers=headers) as series_response:
                    if series_response.status == 200:
                        series_data = await series_response.json()
                        current_monitored = series_data.get("monitored", False)

                        if not current_monitored:
                            logger.info(f"   ⚠️ Series is currently unmonitored - fixing this now")

                            # Enable series monitoring for future episodes/seasons
                            series_data["monitored"] = True

                            async with session.put(f"{sonarr_url}/api/v3/series/{series_id}",
                                                 headers=headers, json=series_data) as update_response:
                                if update_response.status in [200, 202]:
                                    logger.info(f"   ✅ Restored series monitoring - will detect future episodes/seasons")
                                else:
                                    logger.warning(f"   ❌ Failed to restore monitoring: HTTP {update_response.status}")
                        else:
                            logger.info(f"   ✅ Series monitoring already enabled - future episodes/seasons will be detected")
                    else:
                        logger.warning(f"   ⚠️ Could not verify monitoring status: HTTP {series_response.status}")

                logger.info(f"   ✅ Sonarr cleanup completed - series monitoring verified/restored")
            else:
                logger.info(f"   Series not found in Sonarr library - cleanup complete")

        logger.info(f"🎉 Enhanced Sonarr cleanup completed successfully for: {series_title}")
        return True

    except Exception as e:
        logger.error(f"Error in enhanced Sonarr cleanup for {series_title}: {e}", exc_info=True)
        return False


async def monitor_downloads_unified(settings_dict: Dict[str, Any], logger_instance: logging.Logger, mcp_manager=None) -> bool:
    """
    UNIFIED DOWNLOAD MONITORING: Handles both movies (Radarr) and TV shows (Sonarr) with same sophistication.

    This function provides the sophisticated TV show support matching movie features:
    - Dual content processing (movies + TV shows)
    - Resolution-based organization (4K/1080p/720p)
    - Smart state validation & cleanup
    - Comprehensive API monitoring
    - SQLite integration and filesystem scanning

    Replaces the need to call separate monitor functions - handles everything automatically.

    Args:
        settings_dict: Configuration settings
        logger_instance: Logger for output
        mcp_manager: Optional MCP manager for enhanced features

    Returns:
        bool: True if both movie and TV show monitoring completed successfully
    """
    logger_instance.info("===== Starting Unified Download Monitoring (Movies + TV Shows) =====")
    logger_instance.info("     SOPHISTICATED: Matching movie complexity for TV shows")

    success_count = 0
    total_monitors = 2

    try:
        # Monitor Movies (Radarr)
        logger_instance.info("🎬 Phase 1: Monitoring movie downloads via Radarr...")
        movie_success = await monitor_radarr_downloads(settings_dict, logger_instance, mcp_manager)
        if movie_success:
            success_count += 1
            logger_instance.info("✅ Movie monitoring completed successfully")
        else:
            logger_instance.error("❌ Movie monitoring failed")

        # Monitor TV Shows (Sonarr)
        logger_instance.info("📺 Phase 2: Monitoring TV show downloads via Sonarr...")
        tv_success = await monitor_sonarr_downloads(settings_dict, logger_instance, mcp_manager)
        if tv_success:
            success_count += 1
            logger_instance.info("✅ TV show monitoring completed successfully")
        else:
            logger_instance.error("❌ TV show monitoring failed")

        # Phase 3: Check for and clean up failed extractions
        logger_instance.info("🧹 Phase 3: Checking for failed extractions...")
        try:
            from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager
            from pathlib import Path
            
            # Get workspace root from settings or use default
            workspace_root = Path(__file__).parent / "workspace"
            if 'File' in settings_dict and 'workspace_root' in settings_dict['File']:
                workspace_root = Path(settings_dict['File']['workspace_root'])
            
            state_manager = FilesystemFirstStateManager(workspace_root)
            download_raw_dir = state_manager.stage_directories.get('download_raw')
            
            if download_raw_dir and download_raw_dir.exists():
                failed_extractions = await handle_failed_extractions(download_raw_dir, logger_instance)
                if failed_extractions:
                    logger_instance.info(f"✅ Cleaned up {len(failed_extractions)} failed extractions")
                else:
                    logger_instance.info("✅ No failed extractions found")
            else:
                logger_instance.info("ℹ️ Download directory not found, skipping extraction check")
        except Exception as e:
            logger_instance.error(f"❌ Failed extraction cleanup error: {e}")

        # Event inbox: consume any SAB post-process events and organize
        try:
            from _internal.src.sab_event_inbox import EventInboxReader
            from _internal.src.fs_helpers import find_video_files
            inbox = EventInboxReader()
            processed_events = 0
            for evt in inbox.iter_new_events():
                data = evt.get('data', {})
                ddir = data.get('final_folder') or data.get('download_dir')
                status = data.get('status')
                ok = (str(status).lower() == 'success') or (str(status) == '0')
                if not ddir or not ok:
                    continue
                p = Path(ddir)
                if not p.exists():
                    continue
                # Find a primary video file (largest) - now handles ISO files too
                from _internal.src.fs_helpers import get_main_video_file
                main_file = get_main_video_file(p, logger_instance)
                if not main_file:
                    continue
                # Build minimal content info using folder analysis
                content_info = {
                    'title': p.name,
                    'year': None,
                }
                # Idempotency: skip if movie likely already organized
                try:
                    from _internal.src.idempotency import IdempotencyIndex, compute_movie_key
                    idx = IdempotencyIndex()
                    key = compute_movie_key(content_info)
                    if idx.is_already_processed(key, 'organized'):
                        logger_instance.info(f"   ⏭️ Skipping already-processed event for {p.name}")
                        continue
                except Exception:
                    pass
                # Publish DownloadCompleted event for observability
                try:
                    from _internal.src.event_queue import get_event_queue
                    import asyncio
                    eq = get_event_queue({'EventQueue': {'enabled': True}})
                    await eq.publish('download.completed', {
                        'path': str(p),
                        'main_file': str(main_file),
                        'status': 'success'
                    })
                except Exception:
                    pass
                # Backoff: ensure SAB has fully flushed files
                try:
                    from datetime import datetime, timezone
                    ts_str = evt.get('ts')
                    min_age = MIN_DOWNLOAD_AGE_SEC if isinstance(MIN_DOWNLOAD_AGE_SEC, int) else 60
                    if ts_str:
                        try:
                            # Try stdlib fromisoformat first
                            evt_time = datetime.fromisoformat(ts_str.replace('Z', '+00:00'))
                        except Exception:
                            try:
                                evt_time = _dtparser.parse(ts_str)  # type: ignore
                            except Exception:
                                evt_time = None
                        if evt_time is not None:
                            age = (datetime.now(timezone.utc) - evt_time).total_seconds()
                            if age < min_age:
                                await asyncio.sleep(min_age - age)
                except Exception:
                    pass
                try:
                    ok = await _organize_completed_content(content_info, str(main_file), p, settings_dict, logger_instance)
                    if ok:
                        processed_events += 1
                except Exception:
                    continue
            if processed_events:
                logger_instance.info(f"📥 Event inbox processed {processed_events} completed download(s)")
        except Exception as inbox_err:
            logger_instance.warning(f"Event inbox processing failed (non-fatal): {inbox_err}")

        # Evaluate RequestGroups and mark organized when all children terminal
        # Requeue fallback for FAILED episodes (simple strategy): emit event and call Stage 1 search
        try:
            from _internal.src import request_state as _rstate
            from _internal.src.event_queue import get_event_queue as _get_eq
            eq = _get_eq({'EventQueue': {'enabled': True}})
            # Iterate over groups and detect any FAILED children
            pending = _rstate.pending_groups()
            for grp in pending:
                children = _rstate.list_children(grp.id)
                for ch in children:
                    if ch.type == 'episode' and ch.status == 'FAILED':
                        try:
                            await eq.publish('download.failed', {
                                'type': 'episode',
                                'title': grp.title,
                                'year': grp.year,
                                'season': ch.season,
                                'episode': ch.episode
                            })
                        except Exception:
                            pass
                        # Simple fallback: call Stage 01 requeue/search if available
                        try:
                            import importlib.util
                            stage01_path = Path(__file__).parent / "01_intake_and_nzb_search.py"
                            spec = importlib.util.spec_from_file_location("stage01", stage01_path)
                            if spec and spec.loader:
                                stage01 = importlib.util.module_from_spec(spec)
                                spec.loader.exec_module(stage01)
                                # Use a generic helper if present, otherwise attempt targeted Sonarr search
                                if hasattr(stage01, 'requeue_episode_search'):
                                    await stage01.requeue_episode_search({
                                        'title': grp.title,
                                        'year': grp.year,
                                        'season': ch.season,
                                        'episode': ch.episode
                                    }, settings_dict, logger_instance)
                                else:
                                    # Fallback to Sonarr manual search for episode
                                    from _internal.src.sonarr_integration import SonarrClient
                                    sonarr_url = get_setting("Sonarr", "url", settings_dict=settings_dict, default="http://localhost:8989")
                                    sonarr_api_key = get_setting("Sonarr", "api_key", settings_dict=settings_dict)
                                    if sonarr_api_key:
                                        client = SonarrClient(sonarr_url, sonarr_api_key)
                                        import aiohttp
                                        async with aiohttp.ClientSession() as session:
                                            # Trigger MissingEpisodeSearch only for this series
                                            series_id = (grp.metadata or {}).get('sonarr_series_id')
                                            if series_id:
                                                await client.issue_command(session, {"name": "MissingEpisodeSearch", "seriesId": int(series_id)})
                                                logger_instance.info(f"🔁 Triggered Sonarr MissingEpisodeSearch for seriesId={series_id}")
                        except Exception as e:
                            logger_instance.warning(f"FAILED fallback requeue error: {e}")
        except Exception as fb_err:
            logger_instance.warning(f"FAILED fallback orchestrator encountered an issue: {fb_err}")

        try:
            from _internal.src import request_state as _rstate
            from _internal.src.event_queue import get_event_queue as _get_eq
            eq = _get_eq({'EventQueue': {'enabled': True}})
            pending = _rstate.pending_groups()
            for grp in pending:
                children = _rstate.list_children(grp.id)
                if _rstate.all_children_terminal(grp.id) and not grp.organized:
                    # Organizer for seasons is already called as part of completed content handling.
                    # Here we only mark group organized and emit event for audit.
                    _rstate.mark_group_organized(grp.id)
                    try:
                        import asyncio
                        await eq.publish('group.organized', {
                            'group_id': grp.id,
                            'type': grp.type,
                            'title': grp.title,
                            'year': grp.year
                        })
                    except Exception:
                        pass
        except Exception as grp_err:
            logger_instance.warning(f"RequestGroup evaluation failed (non-fatal): {grp_err}")

        # Overall result
        if success_count == total_monitors:
            logger_instance.info(f"🎉 UNIFIED SUCCESS: Both movie and TV show monitoring completed ({success_count}/{total_monitors})")
            logger_instance.info("📋 FEATURE PARITY: TV shows now have same sophistication as movies")
            return True
        elif success_count > 0:
            logger_instance.warning(f"⚠️ PARTIAL SUCCESS: {success_count}/{total_monitors} monitors completed")
            logger_instance.info("📋 Continuing with partial functionality...")
            return True  # Partial success is acceptable
        else:
            logger_instance.error("💥 COMPLETE FAILURE: Both movie and TV show monitoring failed")
            return False

    except Exception as e:
        logger_instance.error(f"Critical error in unified download monitoring: {e}", exc_info=True)
        return False
    finally:
        logger_instance.info("===== Finished Unified Download Monitoring =====")


async def remove_season_packs_from_queue(sonarr_url: str, api_key: str, series_id: int, logger_instance):
    """
    Remove season pack downloads from Sonarr queue for a specific series.
    This ensures only individual episodes are downloaded, not entire seasons.
    """
    try:
        headers = {'X-Api-Key': api_key}
        timeout = aiohttp.ClientTimeout(total=30)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Get current queue
            async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers) as response:
                if response.status != 200:
                    logger_instance.warning(f"Failed to get queue for season pack removal: HTTP {response.status}")
                    return

                queue_data = await response.json()
                records = queue_data.get('records', [])

                # Find season packs for this series
                season_packs_to_remove = []
                for record in records:
                    if record.get('seriesId') == series_id:
                        title = record.get('title', '')
                        # Detect season pack patterns
                        if any(pattern in title.lower() for pattern in [
                            'season pack', 'complete season', 'full season',
                            f'.s{1:02d}.', f'.s{2:02d}.', f'.s{3:02d}.', f'.s{4:02d}.', f'.s{5:02d}.',
                            f'.s{6:02d}.', f'.s{7:02d}.', f'.s{8:02d}.', f'.s{9:02d}.', f'.s{10:02d}.',
                            'season.1', 'season.2', 'season.3', 'season.4', 'season.5'
                        ]):
                            season_packs_to_remove.append({
                                'id': record.get('id'),
                                'title': title
                            })

                # Remove detected season packs
                for pack in season_packs_to_remove:
                    try:
                        remove_url = f"{sonarr_url}/api/v3/queue/{pack['id']}?removeFromClient=true&blocklist=true"
                        async with session.delete(remove_url, headers=headers) as del_resp:
                            if del_resp.status in [200, 204]:
                                logger_instance.info(f"[SEQUENTIAL] Removed season pack: {pack['title']}")
                            else:
                                logger_instance.warning(f"Failed to remove season pack: {pack['title']} (HTTP {del_resp.status})")
                    except Exception as e:
                        logger_instance.warning(f"Error removing season pack {pack['title']}: {e}")

    except Exception as e:
        logger_instance.warning(f"Failed to remove season packs from queue: {e}")


async def monitor_sonarr_downloads(settings_dict: Dict[str, Any], logger_instance: logging.Logger, mcp_manager=None) -> bool:
    """
    Modern TV show download monitoring using Sonarr API with SQLite state management.

    TV show equivalent of monitor_radarr_downloads with same sophistication level.
    Monitors Sonarr downloads, organizes completed TV shows, handles cleanup.

    Features matching movie monitoring:
    - Smart state validation & cleanup
    - Resolution-based organization (4K/1080p/720p)
    - Plex-compatible naming structure
    - Comprehensive cleanup operations
    - Auto-discovery via filesystem scanning
    - SQLite integration with metadata database

    Args:
        settings_dict: Configuration settings
        logger_instance: Logger for output
        mcp_manager: Optional MCP manager for enhanced features

    Returns:
        bool: True if monitoring completed successfully, False otherwise
    """
    global logger
    logger = logger_instance

    # Import Path locally to avoid scope issues
    from pathlib import Path
    from datetime import datetime, timezone

    logger.info("===== Starting Modern Sonarr TV Show Download Monitoring with SQLite =====")
    logger.info("     ENHANCED: Dual-detection system (Filesystem + Sonarr API) + SQLite state")

    # Initialize filesystem-first state manager
    workspace_root = Path.cwd()
    filesystem_manager = FilesystemFirstStateManager(workspace_root)
    metadata_db = MetadataOnlyDatabase(workspace_root)

    # Discover current TV shows by scanning filesystem
    logger.info("Discovering TV shows by scanning filesystem...")
    movies_by_stage = filesystem_manager.discover_movies_by_stage()  # Using same discovery for TV shows
    total_content = sum(len(movies) for movies in movies_by_stage.values())
    logger.info(f"Found {total_content} content items across {len(movies_by_stage)} stages")

    # MCP Enhancement: Initialize services
    sequential_service = mcp_manager.services.get('sequential_thinking') if mcp_manager else None
    memory_service = mcp_manager.services.get('memory_manager') if mcp_manager else None
    github_service = mcp_manager.services.get('github_integration') if mcp_manager else None

    # Get Sonarr configuration
    sonarr_url = get_setting("Sonarr", "url", settings_dict=settings_dict, default="http://localhost:8989")
    sonarr_api_key = get_setting("Sonarr", "api_key", settings_dict=settings_dict)

    if not sonarr_api_key:
        logger.error("Sonarr API key not configured - cannot monitor TV show downloads")
        return False

    # ENHANCED: Get SABnzbd complete_raw directory for filesystem scanning
    sabnzbd_complete_dir = None
    if "Paths" in settings_dict and "download_complete_raw_dir" in settings_dict["Paths"]:
        raw_path = settings_dict["Paths"]["download_complete_raw_dir"]
        if "%(download_client_active_dir)s" in raw_path:
            base_dir = settings_dict["Paths"].get("download_client_active_dir", "workspace/1_downloading")
            sabnzbd_complete_dir = Path(raw_path.replace("%(download_client_active_dir)s", base_dir))
        else:
            sabnzbd_complete_dir = Path(raw_path)
    else:
        sabnzbd_complete_dir = Path("workspace/1_downloading/complete_raw")

    logger.info(f"     SABnzbd complete directory: {sabnzbd_complete_dir}")
    logger.info(f"     Sonarr API endpoint: {sonarr_url}")

    # SMART STATE VALIDATION: Fix broken pipeline states automatically
    logger.info("     SMART STATE VALIDATION: Checking for inconsistent TV show states...")

    # Get current Sonarr status to detect active downloads
    headers = {'X-Api-Key': sonarr_api_key}
    timeout = aiohttp.ClientTimeout(total=30)

    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Get current Sonarr series list and queue
            async with session.get(f"{sonarr_url}/api/v3/series", headers=headers) as response:
                if response.status == 200:
                    sonarr_series = await response.json()
                    logger.info(f"Retrieved {len(sonarr_series)} TV series from Sonarr")
                else:
                    logger.error(f"Failed to get Sonarr series: HTTP {response.status}")
                    sonarr_series = []

            # Get queue status for logging purposes
            async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers) as response:
                if response.status == 200:
                    queue_data = await response.json()
                    active_downloads = len(queue_data.get('records', []))
                    if active_downloads > 0:
                        logger.info(f"     Active TV show downloads in Sonarr queue: {active_downloads}")
                    else:
                        logger.info("No active TV show downloads in Sonarr queue")
                else:
                    logger.warning(f"Failed to get Sonarr queue: HTTP {response.status}")

    except Exception as e:
        logger.error(f"Error in Sonarr connection check: {e}")

    # Helper: Load requested TV titles and years from request file for authoritative overrides
    def load_tv_requests_map() -> Dict[str, Dict[str, Any]]:
        """Return a map of normalized_title -> {title, year} from user request files."""
        candidates = [
            Path("new_tv_requests.txt"),
            Path("example_tv_requests.txt"),
            Path("config/tv_requests.txt"),
        ]
        req_path = next((p for p in candidates if p.exists()), None)
        req_map: Dict[str, Dict[str, Any]] = {}
        if not req_path:
            return req_map
        try:
            lines = req_path.read_text(encoding="utf-8", errors="ignore").splitlines()
            for raw in lines:
                line = raw.strip()
                if not line or line.startswith("#"):
                    continue
                title, year = None, None
                # Patterns: "Title (1999)" or "Title,1999" or just "Title"
                m = re.match(r"^(.*?)\s*\((\d{4})\)\s*$", line)
                if m:
                    title = m.group(1).strip()
                    year = int(m.group(2))
                else:
                    parts = [p.strip() for p in line.split(",")]
                    if len(parts) == 2 and re.fullmatch(r"\d{4}", parts[1] or ""):
                        title, year = parts[0], int(parts[1])
                    else:
                        title = line
                if title:
                    key = re.sub(r"[^a-z0-9]+", "", title.lower())
                    req_map[key] = {"title": title, "year": year}
        except Exception as _:
            pass
        return req_map

    tv_requests_map = load_tv_requests_map()

    # ENHANCED: Check filesystem for completed TV show downloads
    logger.info("     ENHANCED: Checking both Sonarr API and filesystem for completed TV shows")
    logger.info(f"     Will scan SABnzbd directory: {sabnzbd_complete_dir}")

    # FOLDER-FIRST DETECTION: one entry per download folder (prevents multi-processing per file)
    completed_tv_folders = []
    if Path(sabnzbd_complete_dir).exists():
        for item in Path(sabnzbd_complete_dir).iterdir():
            if not item.is_dir():
                continue

            # WINDOWS PATH LENGTH VALIDATION - TV SHOWS
            if len(str(item)) > 200:  # Conservative limit (Windows limit is 260)
                logger.warning(f"⚠️  SKIPPING TV SHOW: Path too long ({len(str(item))} chars): {item.name}")
                logger.warning(f"     Moving to issues folder to prevent cascade failures")
                try:
                    issues_path_too_long_dir = Path(sabnzbd_complete_dir).parent / "issues_path_too_long"
                    issues_path_too_long_dir.mkdir(exist_ok=True)
                    safe_move_file(str(item), str(issues_path_too_long_dir / item.name), logger_instance=logger)
                    logger.warning(f"     Moved problematic TV download to: {issues_path_too_long_dir / item.name}")
                except Exception as e:
                    logger.error(f"     Failed to move problematic TV download: {e}")
                continue

            # Look for video files in the directory (now handles ISO files too)
            from _internal.src.fs_helpers import get_main_video_file, find_video_files
            try:
                # First try to get the main video file (handles ISO extraction)
                main_video_file = get_main_video_file(item, logger)
                if main_video_file:
                    mkv_files = [main_video_file]
                else:
                    # Fall back to finding regular video files
                    mkv_files = [f for f in find_video_files(item) if f.exists()]
            except Exception as _e:
                logger.warning(f"     Skipping folder due to access error: {item} - {_e}")
                continue

            # Filter out tiny/sample files; only consider legit episode-sized files
            filtered_files = []
            for f in mkv_files:
                try:
                    if f.stat().st_size > 50 * 1024 * 1024:  # > 50MB (episode minimum)
                        filtered_files.append(f)
                except OSError as e:
                    logger.warning(f"     Skipping TV file due to path/access error: {f} - {e}")
                    continue

            if not filtered_files:
                continue

            # Choose largest file as representative main file (used by organizer for naming)
            try:
                main_file = max(filtered_files, key=lambda f: f.stat().st_size)
            except Exception:
                main_file = filtered_files[0]

            total_size_gb = 0.0
            try:
                total_size_gb = round(sum(f.stat().st_size for f in filtered_files) / (1024**3), 2)
            except Exception:
                pass

            completed_tv_folders.append({
                "folder_path": item,
                "folder_name": item.name,
                "main_file": main_file,
                "video_files": filtered_files,
                "size_gb": total_size_gb,
            })
            logger.info(f"     Found completed TV folder: {item.name} ({len(filtered_files)} video files, ~{total_size_gb} GB)")

    if completed_tv_folders:
        logger.info(f"     FILESYSTEM DETECTION: Found {len(completed_tv_folders)} completed TV folders ready for organization")
    else:
        logger.info("     No completed TV show folders found in filesystem")

    # Convert folder-first detections into match structures for processing
    completed_tv_matches = []
    for info in completed_tv_folders:
        completed_tv_matches.append({
            "download_dir": info["folder_path"],
            "main_file_path": info["main_file"],
            "content_info": {
                "title": info["folder_name"],
                "type": "tv_show"
            }
        })

    if completed_tv_matches:
        logger.info(f"     Processing {len(completed_tv_matches)} completed TV folders")

        # Process each completed TV folder (season pack or single episode) exactly once
        for match_info in completed_tv_matches:
            try:
                download_dir = match_info["download_dir"]
                main_file_path = match_info["main_file_path"]
                content_info = match_info.get("content_info", {})

                # Acquire a per-folder processing lock to prevent duplicate execution
                lock_files = list(download_dir.glob(".processing_lock*"))
                if lock_files:
                    # Remove stale locks older than 30 minutes
                    removed_stale = False
                    for lf in lock_files:
                        try:
                            age_sec = (time.time() - lf.stat().st_mtime)
                            if age_sec > 1800:  # 30 minutes
                                lf.unlink(missing_ok=True)
                                logger.info(f"     🧹 Removed stale lock for {download_dir.name}")
                                removed_stale = True
                        except Exception:
                            continue
                    # If any lock remains after stale cleanup, skip this folder
                    if list(download_dir.glob(".processing_lock*")):
                        logger.info(f"     ⚠️ Skipping {download_dir.name} - processing lock present")
                        continue
                    elif removed_stale:
                        logger.info(f"     ⚠️ Proceeding with {download_dir.name} after clearing stale lock")

                # Create a new lock
                ts = datetime.now().strftime("%Y%m%dT%H%M%S")
                lock_path = download_dir / f".processing_lock_{ts}"
                try:
                    lock_path.touch(exist_ok=False)
                    logger.info(f"     🔒 Acquired lock for {download_dir.name}, processing...")
                except Exception as e:
                    logger.info(f"     ⚠️ Could not create lock for {download_dir.name} ({e}); skipping to avoid duplicate processing")
                    continue

                try:
                    logger.info(f"     Organizing completed TV folder: {download_dir.name}")
                    logger.info(f"     📁 Using Sonarr metadata directly (no year resolver needed)")

                    success = await _organize_completed_content(
                        content_info=content_info,
                        main_file_path=str(main_file_path),
                        download_dir=download_dir,
                        settings_dict=settings_dict,
                        logger_instance=logger
                    )
                finally:
                    # Always remove the lock (success or failure)
                    try:
                        for lf in download_dir.glob(".processing_lock*"):
                            lf.unlink(missing_ok=True)
                        logger.info(f"     🔓 Released lock for {download_dir.name}")
                    except Exception:
                        pass

                if success:
                    logger.info(f"     ✅ Successfully organized TV show: {download_dir.name}")

                    # EPISODE-AWARE CLEANUP: Only cleanup Sonarr if this was the last pending episode
                    # Check if there are any other episodes for this series still in progress
                    should_cleanup = await should_cleanup_sonarr_series(content_info, settings_dict, logger)

                    if should_cleanup:
                        logger.info(f"     🧹 All episodes complete - running Sonarr cleanup")
                        await cleanup_completed_downloads_from_sonarr(
                            content_info, settings_dict, logger
                        )
                    else:
                        logger.info(f"     ⏳ Other episodes still pending - skipping Sonarr cleanup")
                else:
                    logger.error(f"     ❌ Failed to organize TV show: {download_dir.name}")

            except Exception as e:
                logger.error(f"Error processing TV show {download_dir.name}: {e}", exc_info=True)
    else:
        logger.info("     No completed TV shows found for organization")

    # DYNAMIC SEASON PROGRESSION: Check for series advancement after processing downloads
    try:
        logger.info("🔄 Checking for dynamic season progression opportunities...")

        # Load opt-in file
        sequential_file = Path("sequential_series.txt")
        opt_in_titles = set()
        if sequential_file.exists():
            try:
                lines = sequential_file.read_text(encoding='utf-8').strip().split('\n')
                opt_in_titles = {line.strip() for line in lines if line.strip() and not line.startswith('#')}
                logger.info(f"     Sequential progression enabled for {len(opt_in_titles)} series")
            except Exception as e:
                logger.warning(f"Failed to load sequential_series.txt: {e}")

        if opt_in_titles:
            # Initialize dynamic progression manager
            from _internal.utils.tv_season_progression_manager import TVSeasonProgressionManager
            progression_mgr = TVSeasonProgressionManager(logger)

            # Get all series from Sonarr and check for progression opportunities
            headers = {'X-Api-Key': sonarr_api_key}
            timeout = aiohttp.ClientTimeout(total=30)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                # CRITICAL: First remove any season packs from the queue
                await remove_season_packs_from_queue(session, sonarr_url, headers, logger)

                # Check SABNZBD for recent episode failures and report them
                failed_episodes = await check_sabnzbd_failures(settings_dict, logger)
                if failed_episodes:
                    logger.info(f"📊 Found {len(failed_episodes)} recent episode failures in SABNZBD")
                    for failure in failed_episodes[:5]:  # Show last 5 failures
                        logger.info(f"   • {failure['name']} - {failure['fail_message']}")

                # Get all series
                async with session.get(f"{sonarr_url}/api/v3/series", headers=headers) as response:
                    if response.status == 200:
                        all_series = await response.json()

                        # Fetch episodes concurrently with a semaphore to avoid overloading Sonarr
                        import asyncio as _asyncio
                        sem = _asyncio.Semaphore(5)

                        async def fetch_and_process(series_obj):
                            try:
                                series_id = series_obj.get('id')
                                async with sem:
                                    async with session.get(f"{sonarr_url}/api/v3/episode?seriesId={series_id}", headers=headers) as ep_resp:
                                        if ep_resp.status == 200:
                                            episodes = await ep_resp.json()
                                        else:
                                            logger.warning(f"Failed to get episodes for {series_obj.get('title')}: HTTP {ep_resp.status}")
                                            return

                                # Try to initialize or get existing progression state
                                key = await progression_mgr.init_if_needed(series_obj, episodes, opt_in_titles)
                                if key:
                                    # Enforce current season monitoring
                                    await progression_mgr.enforce_monitoring(key, sonarr_url, sonarr_api_key)
                                    # Check if current season is complete
                                    if await progression_mgr.is_current_season_complete(key, sonarr_url, sonarr_api_key):
                                        logger.info(f"[SEQUENTIAL] Season complete detected for {series_obj.get('title')}")
                                        await progression_mgr.advance(key, sonarr_url, sonarr_api_key)
                                        # Remove any season packs from queue after advancement
                                        await remove_season_packs_from_queue(sonarr_url, sonarr_api_key, series_id, logger)
                            except Exception as series_err:
                                logger.warning(f"Failed to process series {series_obj.get('title', 'Unknown')}: {series_err}")

                        await _asyncio.gather(*(fetch_and_process(so) for so in all_series))
                    else:
                        logger.warning(f"Failed to get series list for progression check: HTTP {response.status}")

            # Log progression status summary
            summary = progression_mgr.get_status_summary()
            if summary:
                logger.info("📊 Sequential Progression Status:")
                for item in summary:
                    status = "COMPLETE" if item["series_complete"] else f"Season {item['current_season']}/{item['total_seasons']}"
                    completed_seasons = ", ".join(map(str, item["seasons_completed"])) if item["seasons_completed"] else "None"
                    logger.info(f"     {item['title']}: {status} (Completed: {completed_seasons})")
        else:
            logger.info("     No series opted-in for sequential progression")

    except Exception as e:
        logger.warning(f"Dynamic season progression check failed: {e}")

    logger.info("===== Finished Modern Sonarr TV Show Download Monitoring =====")
    return True


def _detect_and_handle_path_corruption(sabnzbd_complete_dir: str, logger_instance) -> list:
    """
    Detect Windows 8.3 short name corruption caused by paths that are too long.

    Examples of 8.3 corruption:
    - Detective.Conan.S01E01... becomes DETECT~1.264
    - Very.Long.Movie.Title... becomes VERYLO~1.MKV

    Returns list of corrupted items with their original and short names.
    """
    corrupted_items = []

    try:
        if not Path(sabnzbd_complete_dir).exists():
            return corrupted_items

        logger_instance.info("🔍 Checking for Windows 8.3 short name corruption...")

        for item in Path(sabnzbd_complete_dir).iterdir():
            if item.is_dir():
                folder_name = item.name

                # Detect 8.3 short name patterns:
                # - Contains ~ followed by digit(s)
                # - Shortened to 8 characters before ~
                # - File extension shortened to 3 chars or number
                is_8_3_pattern = (
                    '~' in folder_name and
                    any(char.isdigit() for char in folder_name.split('~')[-1]) and
                    len(folder_name.split('.')[0]) <= 8
                )

                if is_8_3_pattern:
                    # Try to get the original long filename using dir command
                    try:
                        import subprocess
                        result = subprocess.run(
                            ['dir', '/x', str(item.parent)],
                            capture_output=True, text=True, shell=True
                        )

                        # Parse the dir output to find the long filename
                        original_name = None
                        for line in result.stdout.split('\n'):
                            if folder_name in line and '<DIR>' in line:
                                # Extract the long filename from dir output
                                parts = line.strip().split()
                                if len(parts) >= 4:
                                    original_name = ' '.join(parts[3:])  # Long filename
                                break

                        corrupted_items.append({
                            'short_name': folder_name,
                            'original_name': original_name or 'Unknown',
                            'path': str(item),
                            'corruption_type': 'windows_8_3_shortname'
                        })

                        logger_instance.warning(f"🚨 8.3 Corruption detected: {folder_name}")
                        if original_name:
                            logger_instance.warning(f"     Original name: {original_name}")

                    except Exception as e:
                        logger_instance.warning(f"Could not determine original name for {folder_name}: {e}")
                        corrupted_items.append({
                            'short_name': folder_name,
                            'original_name': 'Unable to determine',
                            'path': str(item),
                            'corruption_type': 'windows_8_3_shortname'
                        })

    except Exception as e:
        logger_instance.error(f"Error detecting path corruption: {e}")

    return corrupted_items


async def monitor_radarr_downloads(settings_dict: Dict[str, Any], logger_instance: logging.Logger, mcp_manager=None) -> bool:
    """
    Modern download monitoring using Radarr API with SQLite state management.

    Replaces the complex NZB handling workflow with simple Radarr API monitoring.
    Radarr automatically handles: NZB sending, download monitoring, extraction, file organization.

    Our job: Monitor progress, update pipeline state, handle completion.
    Uses SQLite for reliable state management and filesystem scanning for truth.

    Args:
        settings_dict: Configuration settings
        logger_instance: Logger for output
        mcp_manager: Optional MCP manager for enhanced features

    Returns:
        bool: True if monitoring completed successfully, False otherwise
    """
    global logger
    logger = logger_instance

    # --- Normalize SABnzbd credentials for robust access ---
    def normalize_sabnzbd_settings(settings_dict):
        # If flat keys missing, but section exists, copy them
        sab_section = settings_dict.get("sabnzbd", {})
        if "sabnzbd_url" not in settings_dict and "url" in sab_section:
            settings_dict["sabnzbd_url"] = sab_section["url"]
        if "sabnzbd_api_key" not in settings_dict and "api_key" in sab_section:
            settings_dict["sabnzbd_api_key"] = sab_section["api_key"]
        return settings_dict

    settings_dict = normalize_sabnzbd_settings(settings_dict)

    # Import Path locally to avoid scope issues
    from pathlib import Path
    from datetime import datetime, timezone

    logger.info("===== Starting Modern Radarr Download Monitoring with SQLite =====")
    logger.info("     ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state")

    # Check for season progression opportunities before main monitoring
    try:
        logger.info("🔄 Checking for season progression opportunities...")

        # Load opt-in file for sequential series
        sequential_file = Path("sequential_series.txt")
        opt_in_titles = set()
        if sequential_file.exists():
            try:
                lines = sequential_file.read_text(encoding='utf-8').strip().split('\n')
                opt_in_titles = {line.strip() for line in lines if line.strip() and not line.startswith('#')}
                logger.info(f"     Sequential progression enabled for {len(opt_in_titles)} series")
            except Exception as e:
                logger.warning(f"Failed to load sequential_series.txt: {e}")

        if opt_in_titles:
            # Initialize progression manager with new API
            from _internal.utils.tv_season_progression_manager import TVSeasonProgressionManager
            progression_mgr = TVSeasonProgressionManager(logger)

            # Get Sonarr configuration
            config = configparser.ConfigParser()
            config.read("_internal/config/settings.ini")
            sonarr_url = config.get('sonarr', 'url')
            sonarr_api_key = config.get('sonarr', 'api_key')

            # Get all series from Sonarr and check for progression opportunities
            headers = {'X-Api-Key': sonarr_api_key}
            timeout = aiohttp.ClientTimeout(total=30)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Get all series
                async with session.get(f"{sonarr_url}/api/v3/series", headers=headers) as response:
                    if response.status == 200:
                        all_series = await response.json()

                        for series_obj in all_series:
                            try:
                                # Get episodes for this series
                                series_id = series_obj.get('id')
                                async with session.get(f"{sonarr_url}/api/v3/episode?seriesId={series_id}", headers=headers) as ep_resp:
                                    if ep_resp.status == 200:
                                        episodes = await ep_resp.json()

                                        # Try to initialize or get existing progression state
                                        key = await progression_mgr.init_if_needed(series_obj, episodes, opt_in_titles)

                                        if key:
                                            # Check if current season is complete and advance if needed
                                            if await progression_mgr.is_current_season_complete(key, sonarr_url, sonarr_api_key):
                                                logger.info(f"[SEQUENTIAL] Season complete detected for {series_obj.get('title')}")
                                                await progression_mgr.advance(key, sonarr_url, sonarr_api_key)

                            except Exception as series_err:
                                logger.warning(f"Failed to process series {series_obj.get('title', 'Unknown')}: {series_err}")
        else:
            logger.info("     No series opted-in for sequential progression")

    except Exception as e:
        logger.warning(f"Season progression check failed: {e}")

    # Initialize filesystem-first state manager
    workspace_root = Path.cwd()
    filesystem_manager = FilesystemFirstStateManager(workspace_root)
    metadata_db = MetadataOnlyDatabase(workspace_root)

    # ========== INTELLIGENT FALLBACK INTEGRATION ==========
    # Initialize real-time telemetry system for download monitoring and fallback
    logger.info("🔄 Initializing real-time telemetry for download monitoring...")
    telemetry_system = None
    try:
        from _internal.utils.real_time_telemetry import RealTimeTelemetry
        
        # Create telemetry config from settings
        telemetry_config = {
            "Radarr": {
                "enabled": True,
                "url": get_setting("Radarr", "url", settings_dict=settings_dict, default="http://localhost:7878"),
                "api_key": get_setting("Radarr", "api_key", settings_dict=settings_dict)
            },
            "SABnzbd": {
                "enabled": True,
                "base_url": get_setting("SABnzbd", "url", settings_dict=settings_dict, default="http://localhost:8080"),
                "api_key": get_setting("SABnzbd", "api_key", settings_dict=settings_dict)
            },
            "telemetry_state_path": "_internal/state/telemetry_state.json",
            # 🔧 FIX: Preserve state file across runs for intelligent fallback
            "clear_on_start": False
        }
        
        telemetry_system = RealTimeTelemetry(telemetry_config, logger)
        await telemetry_system.__aenter__()  # Initialize async context
        logger.info("✅ Real-time telemetry system initialized for download monitoring")
        logger.info("   🛡️ Intelligent fallback protection: ENABLED")
        
    except Exception as e:
        logger.warning(f"⚠️ Could not initialize telemetry system: {e}")
        logger.warning("   Download monitoring will continue without intelligent fallback")
    # ========================================================

    # Discover current movies by scanning filesystem
    logger.info("Discovering movies by scanning filesystem...")
    movies_by_stage = filesystem_manager.discover_movies_by_stage()
    total_movies = sum(len(movies) for movies in movies_by_stage.values())
    logger.info(f"Found {total_movies} movies across {len(movies_by_stage)} stages")

    # MCP Enhancement: Initialize services
    sequential_service = mcp_manager.services.get('sequential_thinking') if mcp_manager else None
    memory_service = mcp_manager.services.get('memory_manager') if mcp_manager else None
    github_service = mcp_manager.services.get('github_integration') if mcp_manager else None

    # Get Radarr configuration
    radarr_url = get_setting("Radarr", "url", settings_dict=settings_dict, default="http://localhost:7878")
    radarr_api_key = get_setting("Radarr", "api_key", settings_dict=settings_dict)

    if not radarr_api_key:
        logger.error("Radarr API key not configured - cannot monitor downloads")
        return False

    # ENHANCED: Get SABnzbd complete_raw directory for filesystem scanning
    sabnzbd_complete_dir = None
    if "Paths" in settings_dict and "download_complete_raw_dir" in settings_dict["Paths"]:
        raw_path = settings_dict["Paths"]["download_complete_raw_dir"]
        if "%(download_client_active_dir)s" in raw_path:
            base_dir = settings_dict["Paths"].get("download_client_active_dir", "workspace/1_downloading")
            sabnzbd_complete_dir = Path(raw_path.replace("%(download_client_active_dir)s", base_dir))
        else:
            sabnzbd_complete_dir = Path(raw_path)
    else:
        sabnzbd_complete_dir = Path("workspace/1_downloading/complete_raw")

    logger.info(f"     SABnzbd complete directory: {sabnzbd_complete_dir}")
    logger.info(f"     Radarr API endpoint: {radarr_url}")

    # SMART STATE VALIDATION: Fix broken pipeline states automatically
    logger.info("     SMART STATE VALIDATION: Checking for inconsistent states...")

    # First, get current Radarr status to detect active downloads
    headers = {'X-Api-Key': radarr_api_key}
    timeout = aiohttp.ClientTimeout(total=30)

    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Get current Radarr movie list and queue
            async with session.get(f"{radarr_url}/api/v3/movie", headers=headers) as response:
                if response.status == 200:
                    radarr_movies = await response.json()
                    logger.info(f"Retrieved {len(radarr_movies)} movies from Radarr")
                else:
                    logger.error(f"Failed to get Radarr movies: HTTP {response.status}")
                    radarr_movies = []

            # Get queue status for logging purposes
            async with session.get(f"{radarr_url}/api/v3/queue", headers=headers) as response:
                if response.status == 200:
                    queue_data = await response.json()
                    active_downloads = len(queue_data.get('records', []))
                    if active_downloads > 0:
                        logger.info(f"     Active downloads in Radarr queue: {active_downloads}")
                    else:
                        logger.info("No active downloads in Radarr queue")
                else:
                    logger.warning(f"Failed to get Radarr queue: HTTP {response.status}")

    except Exception as e:
        logger.error(f"Error in Radarr connection check: {e}")
    # Get movies from filesystem that are in download states
    download_states = ["radarr_added_searching", "radarr_downloading", "download_initiated_client", "downloading", "download_completed"]
    movies_by_stage = filesystem_manager.discover_movies_by_stage()
    movies_to_monitor = movies_by_stage.get('downloading', []) + movies_by_stage.get('queued', [])

    logger.info(f"Found {len(movies_to_monitor)} movies in download states to monitor")

    # Perform smart validation on SQLite data
    logger.info("     SMART STATE VALIDATION: Checking for inconsistent states...")
    for movie in movies_to_monitor:
        unique_id = movie.get("unique_id")
        title = movie.get("title", "Unknown")
        year = movie.get("year", "")
        status = movie.get("status", "unknown")
        paths = movie.get("paths", {})
        organized_path = paths.get("organized_mkv_path") if isinstance(paths, dict) else None

        # Check for broken "mkv_processing_pending" states where file doesn't exist
        if status == "mkv_processing_pending" and organized_path and not Path(organized_path).exists():
            logger.warning(f"     SMART VALIDATION: Fixing broken state for {title} ({year})")
            logger.warning(f"   Claims to be organized at: {organized_path}")
            logger.warning(f"   But file doesn't exist!")

            # Update status in SQLite to trigger re-processing
            update_movie_status_filesystem(
                unique_id=unique_id,
                new_status="error_organizing_movie",
                error_message="Smart validation: Movie missing from organized location"
            )
            logger.info(f"       Reset {title} to error state for re-processing")

    # Re-fetch movies after validation updates
    movies_by_stage = filesystem_manager.discover_movies_by_stage()
    movies_to_monitor = movies_by_stage.get('downloading', []) + movies_by_stage.get('queued', [])

    logger.info(f"Found {len(movies_to_monitor)} movies in download states to monitor")

    # Helper function to sync pipeline state (filesystem-first approach)
    def sync_pipeline_state():
        try:
            # Refresh movies discovery from filesystem
            movies_by_stage = filesystem_manager.discover_movies_by_stage()
            total_movies = sum(len(movies) for movies in movies_by_stage.values())
            logger.info(f"Pipeline state refreshed - found {total_movies} movies")
        except Exception as e:
            logger.error(f"Failed to sync pipeline state: {e}")

    # Helper function to update movie status with filesystem-first approach
    def update_movie_status_filesystem(unique_id: str, new_status: str, error_message: str = None, **additional_data):
        try:
            # Find the movie directory to set stage marker
            movies_by_stage = filesystem_manager.discover_movies_by_stage()
            movie_dir = None

            # Search through all stages to find the movie
            for stage_movies in movies_by_stage.values():
                for movie in stage_movies:
                    if movie.get('unique_id') == unique_id or movie.get('title') == unique_id:
                        movie_dir = Path(movie.get('movie_directory', ''))
                        break
                if movie_dir:
                    break

            if movie_dir and movie_dir.exists():
                # Map status to appropriate stage marker
                stage_mapping = {
                    'downloading': 'downloading',
                    'download_completed': 'organized',
                    'organized': 'organized',
                    'mkv_processing_pending': 'organized'
                }
                stage = stage_mapping.get(new_status, new_status)

                success = filesystem_manager.set_stage_marker(movie_dir, stage, {
                    'status': new_status,
                    'error_message': error_message,
                    **additional_data
                })

                if success:
                    logger.debug(f"Updated movie {unique_id} status to {new_status}")
                else:
                    logger.error(f"Failed to update movie {unique_id} status")
            else:
                logger.warning(f"Could not find movie directory for {unique_id}")
        except Exception as e:
            logger.error(f"Error updating movie status: {e}")

    # Filter movies that are actively downloading (not completed)
    active_download_movies = [m for m in movies_to_monitor if m.get("status") != "download_completed"]

    if not active_download_movies:
        logger.info("No movies currently in download states")
    else:
        logger.info(f"Monitoring {len(active_download_movies)} movie(s) for download progress")
        
        # ========== INTELLIGENT FALLBACK INTEGRATION ==========
        # Check for failures and trigger fallbacks for active downloads
        if telemetry_system:
            try:
                logger.info("🔍 Checking for download failures and triggering intelligent fallbacks...")
                
                # Update telemetry with current download status
                for movie in active_download_movies:
                    title = movie.get("title", "Unknown")
                    year = movie.get("year", "")
                    status = movie.get("status", "unknown")
                    radarr_id = movie.get("radarr_id")
                    
                    if radarr_id and status in ["failed", "error", "cancelled"]:
                        logger.warning(f"🚨 Detected failed download: {title} ({year}) - Status: {status}")
                        
                        # Create a mock download job for the failed movie
                        from _internal.utils.real_time_telemetry import DownloadJob
                        failed_job = DownloadJob(
                            id=f"mock_{radarr_id}",
                            title=f"{title} ({year})" if year else title,
                            status=status,
                            progress=0.0,
                            radarr_id=radarr_id
                        )
                        
                        # Trigger fallback handling
                        await telemetry_system._handle_detected_failure(
                            failed_job=failed_job,
                            failure_reason=f"detected_in_pipeline_status_{status}",
                            radarr_id=radarr_id
                        )
                
                # Also check for any jobs that disappeared from queue but failed
                await telemetry_system._check_for_failures_and_trigger_fallbacks()
                
                logger.info("✅ Intelligent fallback check completed")
                
            except Exception as e:
                logger.warning(f"⚠️ Error during intelligent fallback check: {e}")
        # ========================================================

    try:
        # MCP Enhancement: Initialize services
        sequential_service = mcp_manager.services.get('sequential_thinking') if mcp_manager else None
        memory_service = mcp_manager.services.get('memory_manager') if mcp_manager else None
        github_service = mcp_manager.services.get('github_integration') if mcp_manager else None

        # Get Radarr configuration
        radarr_url = get_setting("Radarr", "url", settings_dict=settings_dict, default="http://localhost:7878")
        radarr_api_key = get_setting("Radarr", "api_key", settings_dict=settings_dict)

        if not radarr_api_key:
            logger.error("Radarr API key not configured - cannot monitor downloads")
            return False

        headers = {'X-Api-Key': radarr_api_key}

        # ENHANCED: Also check filesystem for completed downloads (addresses detection issue)
        logger.info("     ENHANCED: Checking both Radarr API and filesystem for completed downloads")
        logger.info(f"     Will scan SABnzbd directory: {sabnzbd_complete_dir}")

        # STEP 0: Handle long paths BEFORE any other processing
        try:
            complete_raw_dir = Path(sabnzbd_complete_dir)
            if complete_raw_dir.exists():
                logger.info("🛡️  LONG PATH PRE-PROCESSING: Checking for Windows path length issues...")

                # Debug: Show what we're scanning
                items_found = list(complete_raw_dir.iterdir())
                logger.info(f"     Found {len(items_found)} items to check for long paths")
                for item in items_found:
                    if item.is_dir():
                        logger.info(f"     Checking folder: {item.name} ({len(str(item))} chars)")
                        try:
                            mkv_files = list(item.glob("*.mkv"))
                            for mkv in mkv_files:
                                mkv_path_len = len(str(mkv))
                                logger.info(f"          MKV file: {mkv.name} ({mkv_path_len} chars)")
                                logger.info(f"          Full MKV path: {str(mkv)}")
                        except Exception as e:
                            logger.warning(f"          Error checking MKV files in {item.name}: {e}")

                handle_long_paths_in_complete_raw(complete_raw_dir)
                logger.info("     Long path handling completed - safe to proceed with detection")
        except Exception as e:
            logger.warning(f"     Long path handling failed (non-critical): {e}")

        # STEP 1: Check for 8.3 short name corruption issues
        corrupted_items = _detect_and_handle_path_corruption(sabnzbd_complete_dir, logger)
        if corrupted_items:
            logger.warning(f"🚨 DETECTED {len(corrupted_items)} items with path corruption (8.3 short names)")
            for item_info in corrupted_items:
                logger.warning(f"     Corrupted: {item_info['short_name']} → Original: {item_info['original_name']}")
                logger.warning(f"     This indicates Windows path length issues causing filesystem corruption")

        # FILESYSTEM-FIRST DETECTION: Check for actual completed files
        completed_files_found = []
        if Path(sabnzbd_complete_dir).exists():
            for item in Path(sabnzbd_complete_dir).iterdir():
                if item.is_dir():
                    # Check path length before processing
                    folder_path_len = len(str(item))
                    if folder_path_len > 200:
                        logger.warning(f"🛡️  PATH TOO LONG: Skipping folder with {folder_path_len} chars: {item.name}")
                        logger.warning(f"     This item should be moved to issues_path_too_long folder")
                        continue

                    # Look for video files in the directory
                    try:
                        from _internal.src.fs_helpers import find_video_files
                        mkv_files = find_video_files(item)
                        for mkv_file in mkv_files:
                            # Check MKV file path length before accessing
                            mkv_path_len = len(str(mkv_file))
                            if mkv_path_len > 200:
                                logger.warning(f"🛡️  PATH TOO LONG: Detected MKV with {mkv_path_len} chars: {mkv_file.name}")
                                logger.warning(f"     Will attempt to process using short path handling...")

                                # Instead of skipping, try to process with path length handling
                                logger.info(f"     SMART HANDLING: Long paths detected - will process as TV/Movie content")

                                # For long paths, assume it's valid content and process it
                                # We'll handle the path issues during the actual organization step
                                completed_files_found.append({
                                    "folder_name": item.name,
                                    "mkv_path": mkv_file,  # Keep original path for now
                                    "size_gb": 1.4,  # Reasonable estimate - will be corrected during processing
                                    "path_length_issue": True,  # Flag for special handling
                                    "original_mkv_name": mkv_file.name
                                })
                                logger.info(f"     Found long-path content: {item.name} (1.4 GB estimate) - WILL PROCESS")
                                continue

                            try:
                                # Check if file exists and get its size safely
                                if mkv_file.exists() and mkv_file.is_file():
                                    file_size = mkv_file.stat().st_size
                                    if file_size > 7 * 1024 * 1024 * 1024:  # > 7GB
                                        completed_files_found.append({
                                            "folder_name": item.name,
                                            "mkv_path": mkv_file,
                                            "size_gb": round(file_size / (1024**3), 2)
                                        })
                                        logger.info(f"     Found completed movie: {item.name} ({round(file_size / (1024**3), 2)} GB)")
                                else:
                                    logger.warning(f"     Skipping non-existent or invalid file: {mkv_file}")
                            except (OSError, FileNotFoundError) as file_error:
                                logger.warning(f"     Error accessing file {mkv_file}: {file_error}")
                                continue
                    except (OSError, PermissionError) as dir_error:
                        logger.warning(f"     Error scanning directory {item}: {dir_error}")
                        continue

        if completed_files_found:
            logger.info(f"     FILESYSTEM DETECTION: Found {len(completed_files_found)} completed movies ready for organization")
        else:
            logger.info("     No completed movies found in filesystem")
        # ENHANCED: Simplified and robust detection logic
        # Priority 1: Filesystem scan for completed files (MOST RELIABLE)
        # Priority 2: Radarr API status (secondary confirmation)

        logger.info("     ROBUST DETECTION: Scanning filesystem for completed downloads...")
        logger.info(f"     Scanning for completed downloads in: {sabnzbd_complete_dir}")
        logger.info(f"     Content type filter: movie")

        # Use the async detection function with filesystem manager - BOTH MOVIES AND TV SHOWS
        logger.info("     ENHANCED DETECTION: Scanning for both movies and TV shows...")

        # First scan for movies
        # Use existing robust scanning path below (_discover_completed_downloads) for both content types
        movie_matches = []
        tv_matches = []

        # Combine both types
        completed_matches = movie_matches + tv_matches

        logger.info(f"     FILESYSTEM SCAN: Found {len(movie_matches)} movies and {len(tv_matches)} TV shows")
        logger.info(f"     TOTAL CONTENT: {len(completed_matches)} items ready for processing")

        # SAFETY FALLBACK: If database detection found nothing, use filesystem detection
        if len(completed_matches) == 0 and len(completed_files_found) > 0:
            logger.warning("🛡️  DATABASE DETECTION EMPTY - Activating filesystem fallback for edge cases")
            logger.warning(f"     Converting {len(completed_files_found)} filesystem items to processing format...")

            # Convert filesystem detection results to match the expected format
            for file_info in completed_files_found:
                folder_name = file_info["folder_name"]
                mkv_path = file_info["mkv_path"]

                # Handle long path files by trying to access them with safer methods
                if file_info.get("path_length_issue", False):
                    # For long paths, try to find an accessible file in the directory
                    try:
                        download_dir = mkv_path.parent
                        # Try to find any accessible MKV file in the directory
                        accessible_mkv = None
                        for potential_mkv in download_dir.iterdir():
                            if potential_mkv.suffix.lower() == ".mkv":
                                try:
                                    if potential_mkv.exists() and potential_mkv.stat().st_size > 100 * 1024 * 1024:  # > 100MB
                                        accessible_mkv = potential_mkv
                                        break
                                except (OSError, FileNotFoundError):
                                    continue

                        if accessible_mkv:
                            mkv_path = accessible_mkv
                            logger.info(f"     LONG PATH FIX: Found accessible file for {folder_name}")
                        else:
                            logger.warning(f"     LONG PATH WARNING: No accessible file found for {folder_name}")
                            # Continue anyway - we'll handle it in the organization step
                    except Exception as e:
                        logger.warning(f"     LONG PATH ERROR: Could not find accessible file for {folder_name}: {e}")

                # Detect if it's a TV show based on folder name
                is_tv_show = ("S0" in folder_name and "E0" in folder_name)

                # Create a minimal content info object
                fallback_content = {
                    "unique_id": f"fallback_{folder_name}",
                    "title": folder_name.replace(".", " ").split("S0")[0].strip(),  # Extract show name
                    "cleaned_title": folder_name.replace(".", " ").split("S0")[0].strip(),
                    "year": "",
                    "content_type": "tv_show" if is_tv_show else "movie"
                }

                # Create match format
                fallback_match = {
                    "content_info": fallback_content,
                    "main_file_path": mkv_path,
                    "download_dir": mkv_path.parent,
                    "content_type": "tv_show" if is_tv_show else "movie"
                }

                completed_matches.append(fallback_match)
                logger.info(f"     FALLBACK: Added {fallback_content['title']} as {'TV show' if is_tv_show else 'movie'}")

            logger.info(f"     FALLBACK COMPLETE: Now processing {len(completed_matches)} items total")

        # Process matched completed downloads - BOTH MOVIES AND TV SHOWS
        organized_count = 0
        for match in completed_matches:
            content = match["content_info"]
            main_file = match["main_file_path"]
            download_dir = match["download_dir"]
            folder_name = download_dir.name

            # Detect content type from the match or folder structure
            is_tv_show = ("S0" in folder_name and "E0" in folder_name) or match.get("content_type") == "tv_show"
            content_type = "TV Show" if is_tv_show else "Movie"

            # Handle both regular and auto-generated entries
            content_id = content.get("unique_id") or content.get("id") or f"auto_{folder_name}"
            title = content.get("cleaned_title") or content.get("title", "Unknown")
            year = content.get("year", "")

            logger.info(f"     Organizing completed {content_type.lower()}: {title} ({year}) from {folder_name}")

            # Process based on content type
            if is_tv_show:
                logger.info(f"     🎬 TV SHOW DETECTED: Processing {title} as TV show content")
                # For TV shows, we need to use the TV show organization logic
                success = await _organize_completed_content(content, str(main_file), download_dir, settings_dict, logger)
            else:
                logger.info(f"     🎬 MOVIE DETECTED: Processing {title} as movie content")
                # Update status to download_completed in SQLite
                update_movie_status_filesystem(
                    unique_id=content_id,
                    new_status="download_completed",
                    completed_movie_file=str(main_file),
                    file_size_bytes=main_file.stat().st_size if main_file.exists() else 0
                )

                # Organize the movie file
                success = await _organize_completed_movie(content, str(main_file), download_dir, settings_dict, logger)

            if success:
                organized_count += 1
                logger.info(f"✅ Successfully organized: {title} ({year})")

                # FILESYSTEM-FIRST: No need to update status - .organized marker is sufficient
                # The marker file created in _organize_completed_movie indicates readiness for next stage

                # 🧹 CLEANUP: Remove from Radarr and SABnzbd to enable re-downloads
                logger.info(f"🧹 Starting comprehensive cleanup for {title} ({year})")
                try:
                    # Create session for cleanup operations
                    timeout = aiohttp.ClientTimeout(total=30)
                    async with aiohttp.ClientSession(timeout=timeout) as cleanup_session:
                        headers = {'X-Api-Key': get_setting("Radarr", "api_key", settings_dict=settings_dict)}
                        radarr_url = get_setting("Radarr", "url", settings_dict=settings_dict, default="http://localhost:7878")

                        # Create movie object for cleanup functions
                        movie = {
                            "title": title,
                            "cleaned_title": title,
                            "year": year,
                            "tmdb_id": content.get("tmdb_id"),
                            "paths": {
                                "radarr_id": content.get("id") or content.get("radarr_id")
                            }
                        }

                        # Clean up Radarr (queue, movie file reference, monitoring status)
                        radarr_cleanup_success = await cleanup_completed_downloads_from_radarr(
                            movie, cleanup_session, radarr_url, headers, logger
                        )

                        # Clean up SABnzbd history
                        sabnzbd_cleanup_success = await cleanup_sabnzbd_history(movie, settings_dict, logger)

                        if radarr_cleanup_success and sabnzbd_cleanup_success:
                            logger.info(f"✅ Complete cleanup successful for {title} - ready for re-download if needed")
                        elif radarr_cleanup_success:
                            logger.info(f"✅ Radarr cleanup successful, SABnzbd cleanup had issues")
                        elif sabnzbd_cleanup_success:
                            logger.info(f"✅ SABnzbd cleanup successful, Radarr cleanup had issues")
                        else:
                            logger.warning(f"⚠️ Both cleanup operations had issues for {title}")

                except Exception as e:
                    logger.error(f"Error during cleanup for {title}: {e}")
                    # Don't fail the whole process if cleanup fails
            else:
                logger.error(f"    Failed to organize: {title} ({year})")
                # Only update movie status if this is actually a movie
                if not is_tv_show:
                    update_movie_status_filesystem(
                        unique_id=content_id,
                        new_status="error_organizing_movie",
                        error_message="Failed to organize completed download"
                    )

        logger.info(f"     ORGANIZATION SUMMARY: {organized_count} movies organized successfully")

        # Optional: Still check Radarr API for additional status updates
        # But filesystem detection takes priority
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.get(f"{radarr_url}/api/v3/movie", headers=headers) as response:
                    if response.status == 200:
                        radarr_movies = await response.json()
                        logger.info(f"     Radarr API: Retrieved {len(radarr_movies)} movies for status sync")

                        # Sync Radarr status for remaining movies (not yet filesystem-detected)
                        remaining_movies = [m for m in active_download_movies if m.get("status") != "download_completed"]

                        for movie in remaining_movies:
                            movie_id = movie["unique_id"]
                            cleaned_title = movie.get("cleaned_title", "Unknown")
                            year = movie.get("year", "")
                            tmdb_id = movie.get("tmdb_id")
                            radarr_id = movie.get("paths", {}).get("radarr_id")

                            # Find corresponding Radarr movie
                            radarr_movie = None
                            if radarr_id:
                                radarr_movie = next((m for m in radarr_movies if m.get('id') == radarr_id), None)

                            if not radarr_movie and tmdb_id:
                                radarr_movie = next((m for m in radarr_movies if m.get('tmdbId') == tmdb_id), None)

                            if radarr_movie:
                                downloaded = radarr_movie.get('hasFile', False)
                                if downloaded:
                                    movie_file = radarr_movie.get('movieFile', {})
                                    if movie_file:
                                        logger.info(f"     Radarr confirms download: {cleaned_title} ({year})")
                                        update_movie_status_filesystem(
                                            unique_id=movie_id,
                                            new_status="download_completed",
                                            radarr_file_path=movie_file.get('path', '')
                                        )
                    else:
                        logger.warning(f"Radarr API returned HTTP {response.status} - using filesystem detection only")
            except Exception as e:
                logger.warning(f"Error checking Radarr API: {e}")

        # Final sync and cleanup
        sync_pipeline_state()

        # Close database connection
        metadata_db.close()

        # ========== INTELLIGENT FALLBACK CLEANUP ==========
        # Clean up telemetry system
        if telemetry_system:
            try:
                await telemetry_system.__aexit__(None, None, None)
                logger.info("✅ Real-time telemetry system cleaned up")
            except Exception as e:
                logger.warning(f"⚠️ Error cleaning up telemetry system: {e}")
        # ===================================================

        logger.info("===== Finished Modern Radarr Download Monitoring =====")
        if organized_count > 0:
            logger.info(f"     SUCCESS: {organized_count} movies organized and ready for MKV processing")
        else:
            logger.info("    No new completed downloads found this run")

        return True

    except Exception as e:
        logger.error(f"Critical error in download monitoring: {e}", exc_info=True)

        # ========== INTELLIGENT FALLBACK CLEANUP ON ERROR ==========
        # Clean up telemetry system on error
        if 'telemetry_system' in locals() and telemetry_system:
            try:
                await telemetry_system.__aexit__(None, None, None)
                logger.info("✅ Real-time telemetry system cleaned up (error path)")
            except Exception as cleanup_error:
                logger.warning(f"⚠️ Error cleaning up telemetry system: {cleanup_error}")
        # ===========================================================

        # Close SQLite connection on error
        try:
            if 'metadata_db' in locals():
                metadata_db.close()
        except:
            pass

        return False


async def find_completed_downloads_simple(sabnzbd_complete_dir: Path, filesystem_manager, logger, content_type: str = "auto", settings_dict: Dict[str, Any] | None = None) -> List[Dict[str, Any]]:
    """
    Simple filesystem-first detection for completed downloads.
    Uses recursive search to handle nested folder structures.
    Enhanced with movie_import_helper-style detection and stage analysis.
    NOW SUPPORTS BOTH MOVIES AND TV SHOWS.

    Args:
        sabnzbd_complete_dir: Path to SABnzbd complete directory
        filesystem_manager: SQLite state manager for database operations
        logger: Logger instance
        content_type: "movie", "tv_show", or "auto" for automatic detection
        settings_dict: Configuration settings dictionary for API access

    Returns:
        List of completed content (movies or TV shows) found in filesystem
    """

    def analyze_processing_stage(movie_dir: Path) -> Dict:
        """Analyze what processing stage a movie is at based on its content"""
        issues = []

        # Find video files
        from _internal.src.fs_helpers import find_video_files
        video_files = find_video_files(movie_dir)

        # Find subtitle files
        subtitle_files = []
        for ext in ['.srt', '.sup']:
            subtitle_files.extend(list(movie_dir.rglob(f"*{ext}")))

        # Find processed directories
        audio_dirs = [d for d in movie_dir.iterdir() if d.is_dir() and 'audio' in d.name.lower()]
        subtitle_dirs = [d for d in movie_dir.iterdir() if d.is_dir() and 'subtitle' in d.name.lower()]

        # Analyze video processing
        has_processed_video = any('processed' in vf.name.lower() for vf in video_files)
        has_raw_video = any('processed' not in vf.name.lower() for vf in video_files)

        # Analyze subtitle processing
        has_srt = any(sf.suffix.lower() == '.srt' for sf in subtitle_files)
        has_sup = any(sf.suffix.lower() == '.sup' for sf in subtitle_files)
        has_subtitle_dirs = len(subtitle_dirs) > 0
        has_audio_dirs = len(audio_dirs) > 0

        # Determine processing stage and target
        if has_processed_video and has_subtitle_dirs and has_audio_dirs:
            return {
                'stage': 'mkv_complete',
                'markers': ['.mkv_complete'],
                'issues': issues
            }
        elif has_processed_video and (has_srt or has_subtitle_dirs):
            return {
                'stage': 'mkv_complete',
                'markers': ['.mkv_complete'],
                'issues': issues
            }
        elif has_processed_video:
            if has_sup and not has_srt:
                issues.append("Has .sup files that may need conversion to .srt")
            return {
                'stage': 'organized',
                'markers': ['.organized'],
                'issues': issues
            }
        elif has_raw_video and (has_audio_dirs or has_subtitle_dirs):
            return {
                'stage': 'organized',
                'markers': ['.organized'],
                'issues': issues
            }
        elif has_raw_video:
            return {
                'stage': 'organized',
                'markers': ['.organized'],
                'issues': issues
            }
        else:
            issues.append("No clear video files found")
            return {
                'stage': 'manual_review',
                'markers': [],
                'issues': issues
            }

    completed_content = []

    if not sabnzbd_complete_dir.exists():
        logger.warning(f"Complete directory does not exist: {sabnzbd_complete_dir}")
        return completed_content

    logger.info(f"     Scanning for completed downloads in: {sabnzbd_complete_dir}")
    logger.info(f"     Content type filter: {content_type}")

    # Content type detection helper
    def detect_content_type_from_name(folder_name: str) -> str:
        """Detect if content is movie or TV show from folder name"""
        import re  # Import needed for regex operations in nested function
        folder_lower = folder_name.lower()

        # TV show indicators - comprehensive list including season patterns
        tv_indicators = ['s0', 'season', 'episode', 'e0', 'e1', 'e2', 'e3', 'e4', 'e5', 'e6', 'e7', 'e8', 'e9']
        tv_patterns = [r's\d+e\d+', r'season\s*\d+', r'episode\s*\d+', r's\d+\D', r'\.s\d+\.']

        # Check for TV patterns first (higher priority)
        if any(indicator in folder_lower for indicator in tv_indicators):
            return "tv_show"

        for pattern in tv_patterns:
            if re.search(pattern, folder_lower):
                return "tv_show"

        # Movie indicators (year patterns, quality indicators)
        movie_indicators = ['bluray', 'bdrip', 'webrip', 'dvdrip', 'remux']
        quality_indicators = ['1080p', '720p', '4k', '2160p']
        year_pattern = r'\b(19|20)\d{2}\b'

        # Strong movie indicators
        if re.search(year_pattern, folder_lower) and any(indicator in folder_lower for indicator in movie_indicators):
            return "movie"

        # Check for year + quality without clear TV indicators (weaker signal for movies)
        if re.search(year_pattern, folder_lower) and any(indicator in folder_lower for indicator in quality_indicators):
            return "movie"

        # Default assumption - prefer TV show for ambiguous cases since TV content is more common
        return "tv_show"

    # FIXED: Process by top-level folders first to avoid duplicate season pack processing
    processed_folders = set()

    # First pass: Group by top-level folders using a threaded rglob to avoid blocking the event loop
    from itertools import chain
    import asyncio as _asyncio

    from _internal.src.fs_helpers import find_video_files
    def _scan_videos(base: Path):
        return find_video_files(base)

    mkv_files_all = await _asyncio.to_thread(_scan_videos, sabnzbd_complete_dir)

    for mkv_file in mkv_files_all:
        try:
            # Get the top-level download folder
            relative_path = mkv_file.relative_to(sabnzbd_complete_dir)
            top_level_folder = relative_path.parts[0]
            folder_path = sabnzbd_complete_dir / top_level_folder

            # Skip if we've already processed this folder (prevents season pack duplicates)
            if str(folder_path) in processed_folders:
                continue
            processed_folders.add(str(folder_path))

            folder_name = folder_path.name

            # Count video files in this folder to detect season packs (offloaded to thread)
            from _internal.src.fs_helpers import find_video_files
            def _scan_folder_videos(folder: Path):
                return find_video_files(folder)
            mkv_files_in_folder = await _asyncio.to_thread(_scan_folder_videos, folder_path)
            if len(mkv_files_in_folder) > 1:
                logger.info(f"     Season pack detected: {folder_name} contains {len(mkv_files_in_folder)} episodes")
                mkv_file = mkv_files_in_folder[0]  # representative for matching

            # WINDOWS PATH LENGTH VALIDATION
            if len(str(folder_path)) > 200:  # Conservative limit (Windows limit is 260)
                logger.warning(f"⚠️  SKIPPING: Path too long ({len(str(folder_path))} chars): {folder_name}")
                logger.warning(f"     Moving to issues folder to prevent cascade failures")
                try:
                    issues_path_too_long_dir = sabnzbd_complete_dir.parent / "issues_path_too_long"
                    issues_path_too_long_dir.mkdir(exist_ok=True)
                    safe_move_file(str(folder_path), str(issues_path_too_long_dir / folder_name), logger_instance=logger)
                    logger.warning(f"     Moved problematic download to: {issues_path_too_long_dir / folder_name}")
                except Exception as e:
                    logger.error(f"     Failed to move problematic download: {e}")
                continue

            # Detect content type
            detected_type = detect_content_type_from_name(folder_name)

            # Apply content type filter
            if content_type != "auto" and detected_type != content_type:
                logger.debug(f"     Skipping {folder_name}: detected as {detected_type}, filtering for {content_type}")
                continue

            logger.info(f"     Processing {detected_type}: {folder_name}")

            # Try to match with database entries - different search for movies vs TV shows
            if detected_type == "tv_show":
                # For TV shows, search the MetadataOnlyDatabase
                from _internal.utils.filesystem_first_state_manager import MetadataOnlyDatabase
                metadata_db = MetadataOnlyDatabase(Path(__file__).parent)
                all_tv_shows = metadata_db.get_all_tv()
                all_content = all_tv_shows
                logger.info(f"     Trying to match TV show: {folder_name}")
                logger.info(f"     Against {len(all_content)} tracked TV shows")
            else:
                # For movies, use the filesystem manager
                movies_by_stage = filesystem_manager.discover_movies_by_stage()
                all_content = []
                for stage_content in movies_by_stage.values():
                    all_content.extend(stage_content)
                logger.info(f"     Trying to match movie: {folder_name}")
                logger.info(f"     Against {len(all_content)} tracked movies")

            matched_content = None

            # Enhanced matching with multiple strategies for TV shows and movies
            for db_content in all_content:
                title = db_content.get('title', '').lower().strip()
                cleaned_title = db_content.get('cleaned_title', '').lower().strip()
                year = str(db_content.get('year', ''))

                # Normalize names for matching - replace dots with spaces
                normalized_folder = folder_name.lower().replace('.', ' ').replace('-', ' ')
                normalized_title = title.replace('.', ' ').replace('-', ' ')
                normalized_cleaned = cleaned_title.replace('.', ' ').replace('-', ' ')

                logger.debug(f"       Checking: '{title}' ({year}) vs '{folder_name.lower()}'")
                logger.debug(f"       Normalized: '{normalized_title}' vs '{normalized_folder}'")

                # Strategy 1: Direct title match (with normalization)
                if title and normalized_title in normalized_folder:
                    matched_content = db_content
                    logger.info(f"    ✅ MATCHED (normalized title): {title} -> {folder_name}")
                    break

                # Strategy 2: Cleaned title match (with normalization)
                elif cleaned_title and normalized_cleaned in normalized_folder:
                    matched_content = db_content
                    logger.info(f"    ✅ MATCHED (normalized cleaned): {cleaned_title} -> {folder_name}")
                    break

                # Strategy 3: Original title + year match (for movies)
                elif title and title in folder_name.lower() and year in folder_name:
                    matched_content = db_content
                    logger.info(f"    ✅ MATCHED (title+year): {title} ({year}) -> {folder_name}")
                    break

                # Strategy 4: Fuzzy word matching
                elif title:
                    title_words = set(normalized_title.split())
                    folder_words = set(normalized_folder.split())

                    # If 70% of title words appear in folder name
                    if len(title_words & folder_words) >= len(title_words) * 0.7:
                        matched_content = db_content
                        logger.info(f"    ✅ MATCHED (fuzzy normalized): {title} -> {folder_name}")
                        break

            if matched_content:
                size_gb = mkv_file.stat().st_size / (1024**3)

                # Create appropriate result structure based on content type
                if detected_type == "tv_show":
                    completed_content.append({
                        'content_info': matched_content,
                        'content_type': 'tv_show',
                        'main_file_path': mkv_file,
                        'download_dir': folder_path,
                        'size_gb': round(size_gb, 2)
                    })
                    logger.info(f"    ✅ Found completed TV show: {matched_content.get('title')} ({matched_content.get('year')}) - {size_gb:.2f} GB")
                else:
                    completed_content.append({
                        'content_info': matched_content,
                        'content_type': 'movie',
                        'main_file_path': mkv_file,
                        'download_dir': folder_path,
                        'size_gb': round(size_gb, 2)
                    })
                    logger.info(f"    ✅ Found completed movie: {matched_content.get('title')} ({matched_content.get('year')}) - {size_gb:.2f} GB")
            else:
                # REDUNDANCY FEATURE: Auto-add unmatched content to tracking
                logger.warning(f"     ❌ No database match for: {folder_name}")
                logger.info(f"     🔄 REDUNDANCY: Auto-adding to tracking system...")

                # ENHANCED: Extract title and year using Sonarr API for TV shows
                import re
                extracted_title = ""
                extracted_year = None

                # Detect if this is a TV show or movie based on folder content
                tv_indicators = ['s01', 's02', 's03', 's04', 's05', 'season', 'episode', 'e01', 'e02']
                is_tv_show = any(indicator in folder_name.lower() for indicator in tv_indicators)

                if is_tv_show:
                    # TV SHOW: Extract show name and query Sonarr API for authoritative data
                    clean_name = folder_name

                    # Extract show name before season/episode markers (ignore any years in folder)
                    season_match = re.search(r'[._-]?S\d+', clean_name, re.IGNORECASE)
                    if season_match:
                        extracted_title = clean_name[:season_match.start()].strip(' .-_')
                    else:
                        # Fallback: extract before any episode pattern like E01, E02
                        episode_match = re.search(r'[._-]?E\d+', clean_name, re.IGNORECASE)
                        if episode_match:
                            extracted_title = clean_name[:episode_match.start()].strip(' .-_')
                        else:
                            extracted_title = clean_name.strip(' .-_')

                    # Clean up extracted title - remove dots, underscores, quality tags
                    extracted_title = re.sub(r'[._-]', ' ', extracted_title)
                    extracted_title = re.sub(r'\s+', ' ', extracted_title).strip()

                    # Query Sonarr API for authoritative title and year
                    try:
                        if settings_dict:
                            # We're already in an async context, so use the async version directly
                            sonarr_data = await _query_sonarr_for_show(extracted_title, settings_dict, logger)
                            if sonarr_data:
                                extracted_title = sonarr_data['title']
                                extracted_year = sonarr_data['year']
                                logger.info(f"    📡 Sonarr lookup SUCCESS: '{extracted_title}' ({extracted_year})")
                            else:
                                logger.warning(f"    📡 Sonarr lookup FAILED: No match for '{extracted_title}'")
                                extracted_year = None
                        else:
                            logger.warning("    📡 No settings available for Sonarr API lookup")
                            extracted_year = None
                    except Exception as api_error:
                        logger.warning(f"    📡 Sonarr API error: {api_error}")
                        extracted_year = None
                else:
                    # MOVIE: Try multiple patterns for better title/year extraction
                    patterns = [
                        r'^(.+?)\s*\((\d{4})\).*$',  # Title (Year)
                        r'^(.+?)\s+(\d{4}).*$',      # Title Year
                        r'^(.+?)\.(\d{4})\..*$',     # Title.Year.quality
                    ]

                    for pattern in patterns:
                        match = re.match(pattern, folder_name)
                        if match:
                            extracted_title = match.group(1).strip().replace('.', ' ')
                            extracted_year = int(match.group(2))
                            break

                    # Fallback: Check for movie indicators even without clear year
                    if not extracted_title:
                        movie_indicators = ['bluray', 'bdrip', 'webrip', 'dvdrip', 'hdtv', '1080p', '720p', '4k', 'remux']
                        if any(indicator in folder_name.lower() for indicator in movie_indicators):
                            # Clean up the folder name
                            clean_name = folder_name
                            for tag in movie_indicators + ['x264', 'x265', 'h264', 'h265']:
                                clean_name = re.sub(rf'\b{tag}\b', '', clean_name, flags=re.IGNORECASE)

                            # Try to find year in cleaned name
                            year_match = re.search(r'\b(\d{4})\b', clean_name)
                            if year_match:
                                extracted_year = int(year_match.group(1))
                                extracted_title = clean_name[:year_match.start()].strip(' .-_')
                            else:
                                extracted_title = clean_name.strip(' .-_')
                                extracted_year = None  # Unknown year; don't force a default

                # NEW: TV Show fallback extraction for shows not in Sonarr
                if not extracted_title and detected_type == "tv_show":
                    tv_indicators = ['s01', 's02', 's03', 's04', 's05', 'season', 'episode', 'e01', 'e02']
                    if any(indicator in folder_name.lower() for indicator in tv_indicators):
                        # TV show extraction - get JUST the show name, let Sonarr provide authoritative year
                        clean_name = folder_name

                        # Extract show name before season/episode markers (ignore any years in folder)
                        season_match = re.search(r'[._-]?S\d+', clean_name, re.IGNORECASE)
                        if season_match:
                            extracted_title = clean_name[:season_match.start()].strip(' .-_')
                        else:
                            # Fallback: extract before any episode pattern like E01, E02
                            episode_match = re.search(r'[._-]?E\d+', clean_name, re.IGNORECASE)
                            if episode_match:
                                extracted_title = clean_name[:episode_match.start()].strip(' .-_')
                            else:
                                extracted_title = clean_name.strip(' .-_')

                        # Clean up extracted title - remove dots, underscores, quality tags
                        extracted_title = re.sub(r'[._-]', ' ', extracted_title)
                        extracted_title = re.sub(r'\s+', ' ', extracted_title).strip()

                        # Query Sonarr API for authoritative title and year
                        try:
                            if settings_dict:
                                sonarr_data = await _query_sonarr_for_show(extracted_title, settings_dict, logger)
                                if sonarr_data:
                                    extracted_title = sonarr_data['title']
                                    extracted_year = sonarr_data['year']
                                    logger.info(f"    📡 Sonarr lookup SUCCESS: '{extracted_title}' ({extracted_year})")
                                else:
                                    logger.warning(f"    📡 Sonarr lookup FAILED: No match for '{extracted_title}'")
                                    extracted_year = None
                            else:
                                logger.warning("    📡 No settings available for Sonarr API lookup")
                                extracted_year = None
                        except Exception as api_error:
                            logger.warning(f"    📡 Sonarr API error: {api_error}")
                            extracted_year = None

                        # Clean up extracted title
                        extracted_title = re.sub(r'[._-]', ' ', extracted_title)
                        extracted_title = re.sub(r'\s+', ' ', extracted_title).strip()

                if extracted_title:
                    if extracted_year is not None:
                        logger.info(f"    📝 Extracted: '{extracted_title}' ({extracted_year})")
                    else:
                        logger.info(f"    📝 Extracted: '{extracted_title}' (year unknown)")

                    # Analyze what processing stage this movie is at
                    stage_info = analyze_processing_stage(folder_path)
                    logger.info(f"    🔍 Detected stage: {stage_info['stage']}")
                    if stage_info['issues']:
                        for issue in stage_info['issues']:
                            logger.info(f"    ⚠️  Issue: {issue}")

                    # Create a basic movie entry with consistent structure
                    auto_unique_id = f"auto_{folder_name}"
                    auto_movie = {
                        'unique_id': auto_unique_id,
                        'id': auto_unique_id,  # Add both for compatibility
                        'title': extracted_title,
                        'cleaned_title': extracted_title,
                        'year': extracted_year,
                        'tmdb_id': None,
                        'status': 'download_completed',
                        'auto_added': True,
                        'source': 'filesystem_fallback',
                        'detected_stage': stage_info['stage'],
                        'recommended_markers': stage_info['markers']
                    }

                    # Add to database immediately for consistency
                    try:
                        filesystem_manager.metadata_db.save_movie_metadata(
                            unique_id=auto_unique_id,
                            title=extracted_title,
                            year=extracted_year,
                            metadata={'source': 'filesystem_fallback', 'auto_added': True}
                        )
                        logger.info(f"    ✅ Added to database: {extracted_title} ({extracted_year})")
                    except Exception as db_error:
                        logger.warning(f"    ⚠️  Database add failed: {db_error}")

                    # Create auto content info structure for consistent handling
                    auto_content = {
                        'title': extracted_title,
                        'cleaned_title': extracted_title,
                        'year': extracted_year,
                        'unique_id': auto_unique_id,
                        'source': 'filesystem_fallback',
                        'auto_added': True
                    }

                    size_gb = mkv_file.stat().st_size / (1024**3)
                    completed_content.append({
                        'content_info': auto_content,
                        'content_type': detected_type,
                        'main_file_path': mkv_file,
                        'download_dir': folder_path,
                        'size_gb': round(size_gb, 2)
                    })

                    logger.info(f"    ✅ AUTO-ADDED: {extracted_title} ({extracted_year}) - {size_gb:.2f} GB")
                    logger.info(f"       Source: filesystem_fallback (no prior tracking required)")
                else:
                    logger.warning(f"       Could not extract title/year from: {folder_name}")
                    logger.info(f"       Skipping this file (needs manual review)")

        except Exception as e:
            logger.warning(f"Error processing {mkv_file}: {e}")

    logger.info(f"Found {len(completed_content)} completed downloads with database matches")
    return completed_content

async def _organize_completed_content(content_info: dict, main_file_path: str, download_dir: Path, settings_dict: dict, logger_instance) -> bool:
    """
    Organizes completed download by detecting content type and routing to appropriate directory.
    Handles both movies and TV shows based on content type detection.

    Args:
        content_info: Content data dictionary (could be movie or TV show)
        main_file_path: Path to the main content file
        download_dir: The download directory containing the content
        settings_dict: Settings dictionary
        logger_instance: Logger instance

    Returns:
        bool: True if organization was successful, False otherwise
    """
    try:
        # === CRITICAL: ANALYZE ORIGINAL FOLDER BEFORE ANY RENAMING ===
        # Extract ALL information from original folder name before losing it
        logger_instance.info(f"🔍 Analyzing original download folder: {download_dir.name}")

        # Use our comprehensive analyzer that extracts everything at once
        def analyze_original_folder(folder_name):
            """Extract all metadata from original folder name before renaming destroys info"""
            import re

            folder_lower = folder_name.lower()

            # Content type detection (TV vs Movie)
            tv_patterns = [
                r's\d{1,2}(?!e)\b',          # S01, S02 (season only)
                r's\d{1,2}e\d{1,2}',         # S01E01, S02E05
                r'season\s*\d+',             # Season 1, Season.01
                r'\bs\d{1,2}\s+\d{3,4}p',    # S01 1080p
                r'complete\s*series',        # Complete Series
                r'\.s\d{1,2}\.',             # .S01. format
                r'season\.\d+\.complete',    # Season.1.Complete
                r'\.season\.\d+\.',          # .Season.1.
                r'complete$'                 # Ends with Complete (often TV series)
            ]

            is_tv_show = any(re.search(pattern, folder_lower) for pattern in tv_patterns)
            content_type = "tv_show" if is_tv_show else "movie"

            # Resolution detection
            resolution = None
            if any(x in folder_lower for x in ['2160p', '4k', 'uhd', '3840x2160']):
                resolution = "4k"
            elif any(x in folder_lower for x in ['1080p', 'fhd', '1920x1080']):
                resolution = "1080p"
            elif any(x in folder_lower for x in ['720p', 'hd', '1280x720']):
                resolution = "720p"

            # Title and year extraction
            title = None
            year = None

            # Remove quality/source indicators for cleaner title parsing
            clean_name = re.sub(r'\b(720p|1080p|2160p|4k|uhd|bluray|web-dl|webrip|hdtv|dvdrip|brrip|x264|x265|h264|h265|aac|ac3|dts|remux|repack|proper|real|final|uncut|extended|directors?\.cut|theatrical|imax)\b', '', folder_lower, flags=re.IGNORECASE)
            clean_name = re.sub(r'[\.\-_]+', ' ', clean_name).strip()

            # Extract year
            year_match = re.search(r'\b(19\d{2}|20\d{2})\b', clean_name)
            if year_match:
                year = int(year_match.group(1))
                clean_name = re.sub(r'\b(19\d{2}|20\d{2})\b', '', clean_name).strip()

            # Extract title (everything before season/episode indicators)
            if is_tv_show:
                # Remove season/episode info for title
                title_match = re.search(r'^(.*?)(?:\s+s\d+|\s+season)', clean_name, re.IGNORECASE)
                if title_match:
                    title = title_match.group(1).strip()
                else:
                    title = re.sub(r'\bs\d+.*$', '', clean_name, flags=re.IGNORECASE).strip()
            else:
                title = clean_name.strip()

            # Clean up title
            if title:
                title = re.sub(r'\s+', ' ', title).strip()
                title = title.title() if title.islower() else title

            return {
                'content_type': content_type,
                'resolution': resolution,
                'title': title,
                'year': year,
                'original_folder': folder_name,
                'confidence': 0.95 if (title and (year or is_tv_show)) else 0.7
            }

        # Analyze the original folder BEFORE any processing
        original_analysis = analyze_original_folder(download_dir.name)
        logger_instance.info(f"📊 Original folder analysis: {original_analysis}")

        # Use the analyzed content type instead of the old detector
        detection_result = type('DetectionResult', (), {
            'content_type': original_analysis['content_type'],
            'confidence': original_analysis['confidence']
        })()

        logger_instance.info(f"🎯 Content type: {detection_result.content_type} (confidence: {detection_result.confidence:.2f})")

        # ------------------------------------------------------------------
        # CONTENT-TYPE VALIDATION GATE
        # Determine expected type heuristically from content_info; if strong
        # mismatch (confidence >= gate threshold) quarantine instead of
        # misplacing (prevents TV showing up under Movies and vice‑versa).
        # ------------------------------------------------------------------
        try:
            expected_type = None
            lowered_keys = {k.lower(): k for k in content_info.keys()} if isinstance(content_info, dict) else {}
            # Explicit hint first
            hinted = content_info.get('content_type') if isinstance(content_info, dict) else None
            if hinted in ("movie", "tv_show"):
                expected_type = hinted
            # Heuristic indicators for TV
            elif any(ind in lowered_keys for ind in ("season", "episode", "series_id", "seriesid", "series_title")):
                expected_type = "tv_show"
            # Fallback default
            else:
                expected_type = "movie"

            gate_confidence = 0.65  # threshold at which we trust detector to block
            if (
                detection_result.content_type != expected_type
                and detection_result.confidence >= gate_confidence
            ):
                # Quarantine path structure
                issues_root = Path("workspace/issues_hold")
                quarantine_root = issues_root / "misclassified"
                quarantine_root.mkdir(parents=True, exist_ok=True)

                stamp = datetime.datetime.utcnow().strftime("%Y%m%d_%H%M%S")
                q_dir = quarantine_root / f"{expected_type}_as_{detection_result.content_type}_{stamp}"
                try:
                    q_dir.mkdir(parents=True, exist_ok=True)
                    # Move the entire download folder for manual inspection
                    logger_instance.warning(
                        f"⚠️  Content-type gate: expected {expected_type} but detector says {detection_result.content_type} (conf={detection_result.confidence:.2f}); quarantining {download_dir} -> {q_dir}"
                    )
                    # Use shutil.move to preserve everything (avoid partial processing)
                    import shutil as _shutil
                    _shutil.move(str(download_dir), str(q_dir / download_dir.name))
                    return False
                except Exception as q_err:
                    logger_instance.error(
                        f"Failed to quarantine misclassified folder {download_dir}: {q_err}. Proceeding without gate."  # fall through
                    )
        except Exception as gate_err:
            logger_instance.error(f"Content-type validation gate error (non-fatal): {gate_err}")

        # Get base organized directory
        organized_base_dir = None
        if "Paths" in settings_dict and "mkv_processing_output_dir" in settings_dict["Paths"]:
            organized_base_dir = Path(settings_dict["Paths"]["mkv_processing_output_dir"])
        else:
            # Fallback: Use default organized directory
            organized_base_dir = Path("workspace/2_downloaded_and_organized")

        # Enhance content_info with metadata priority system:
        # PRIORITY 1: Sonarr/Radarr official metadata (database-verified)
        # PRIORITY 2: Pre-analyzed folder metadata (fallback/backup)

        # Only use extracted folder data as fallback if Sonarr/Radarr didn't provide it
        if not content_info.get('title') and original_analysis.get('title'):
            content_info['title'] = original_analysis['title']
            if original_analysis['content_type'] == 'TV_SHOW':
                logger_instance.info(f"📁 Using extracted title '{original_analysis['title']}' (no Sonarr title available)")
            else:
                logger_instance.info(f"📁 Using extracted title '{original_analysis['title']}' (no Radarr title available)")
        elif content_info.get('title'):
            # CRITICAL FIX: Check if the database title looks messy (release name instead of clean title)
            db_title = content_info['title']

            if _title_looks_messy(db_title):
                logger_instance.warning(f"🚨 Database has messy title: '{db_title}'")

                # Try to get clean title from API directly
                clean_title = await _get_clean_title_from_api(db_title, original_analysis, settings_dict, logger_instance)
                if clean_title:
                    content_info['title'] = clean_title
                    logger_instance.info(f"✅ Fixed with clean API title: '{clean_title}'")
                else:
                    # Fallback: Clean the messy title as best we can
                    content_info['title'] = _clean_messy_title(db_title)
                    logger_instance.info(f"🧹 Fallback cleaned title: '{content_info['title']}'")

            if original_analysis['content_type'] == 'TV_SHOW':
                logger_instance.info(f"📡 Using official title '{content_info['title']}' from Sonarr")
            else:
                logger_instance.info(f"📡 Using official title '{content_info['title']}' from Radarr")

        if not content_info.get('year') and original_analysis.get('year'):
            content_info['year'] = original_analysis['year']
            if original_analysis['content_type'] == 'TV_SHOW':
                logger_instance.info(f"📁 Using extracted year '{original_analysis['year']}' (no Sonarr year available)")
            else:
                logger_instance.info(f"📁 Using extracted year '{original_analysis['year']}' (no Radarr year available)")
        elif content_info.get('year'):
            if original_analysis['content_type'] == 'TV_SHOW':
                logger_instance.info(f"📡 Using official year '{content_info['year']}' from Sonarr")
            else:
                logger_instance.info(f"📡 Using official year '{content_info['year']}' from Radarr")

        # CONSISTENCY FIX: For TV shows without year, try to get it from Sonarr
        if original_analysis['content_type'] == 'TV_SHOW' and not content_info.get('year'):
            logger_instance.info(f"🔍 TV show missing year - querying Sonarr for authoritative metadata...")
            try:
                if settings_dict:
                    sonarr_data = await _query_sonarr_for_show(content_info.get('title', ''), settings_dict, logger_instance)
                    if sonarr_data and sonarr_data.get('year'):
                        content_info['year'] = sonarr_data['year']
                        logger_instance.info(f"📡 Retrieved year from Sonarr: {content_info['year']}")
                    else:
                        logger_instance.warning(f"📡 Sonarr query returned no year for: {content_info.get('title', 'N/A')}")
                else:
                    logger_instance.warning("📡 No settings available for Sonarr API lookup")
            except Exception as e:
                logger_instance.warning(f"📡 Failed to query Sonarr for year: {e}")
        content_info['content_type'] = original_analysis['content_type']

        logger_instance.info(f"📝 Enhanced content_info: title='{content_info.get('title', 'N/A')}', year='{content_info.get('year', 'N/A')}', type='{content_info.get('content_type', 'N/A')}'")

        if original_analysis['content_type'] == 'TV_SHOW':
            logger_instance.info(f"🎯 Priority: Sonarr metadata takes precedence over folder extraction")
        else:
            logger_instance.info(f"🎯 Priority: Radarr metadata takes precedence over folder extraction")

        # Get resolution using ffprobe (PRIORITY 1) or folder name fallback (PRIORITY 2)
        # PRIORITY 1: ffprobe is most accurate (reads actual video file data)
        logger_instance.info(f"🎥 Analyzing resolution with ffprobe (most accurate)...")
        ffprobe_exe_path = settings_dict.get("Executables", {}).get("ffprobe_path", "ffprobe")
        width, height = get_video_resolution(main_file_path, ffprobe_exe_path, logger_instance)

        resolution = None

        # Use height for more accurate resolution detection (handles non-standard widths like 1440x1080)
        if height and height >= 2160:
            resolution = "4k"
            logger_instance.info(f"🎥 ffprobe detection: 4K ({width}x{height}) from video file analysis")
        elif height and height >= 1080:
            resolution = "1080p"
            logger_instance.info(f"🎥 ffprobe detection: 1080p ({width}x{height}) from video file analysis")
        elif height and height >= 720:
            resolution = "720p"
            logger_instance.info(f"🎥 ffprobe detection: 720p ({width}x{height}) from video file analysis")
        # Fallback to width-based detection if height is unavailable
        elif width and width >= 3840:
            resolution = "4k"
            logger_instance.info(f"🎥 ffprobe detection: 4K ({width}px width) from video file analysis")
        elif width and width >= 1920:
            resolution = "1080p"
            logger_instance.info(f"🎥 ffprobe detection: 1080p ({width}px width) from video file analysis")
        elif width and width >= 1280:
            resolution = "720p"
            logger_instance.info(f"🎥 ffprobe detection: 720p ({width}px width) from video file analysis")

        # PRIORITY 2: Fallback to pre-analyzed folder information if ffprobe failed
        if not resolution:
            fallback_resolution = original_analysis.get('resolution')
            if fallback_resolution:
                resolution = fallback_resolution
                logger_instance.info(f"📁 Fallback: Using pre-analyzed resolution {resolution} from folder name")
                # Set width for consistency
                if resolution == "4k":
                    width = 3840
                elif resolution == "1080p":
                    width = 1920
                elif resolution == "720p":
                    width = 1280
            else:
                # Final fallback to safe default
                resolution = "1080p"
                width = 1920
                logger_instance.warning(f"🎥 No resolution detected anywhere, defaulting to 1080p")

        logger_instance.info(f"📊 Final resolution determination: {resolution} (width: {width}px)")

        # Check if resolution-based organization is enabled
        enable_resolution_org = settings_dict.get("Paths", {}).get("enable_resolution_based_organization", "true").lower() == "true"
        if not enable_resolution_org:
            resolution = ""  # Disable resolution subdirectories

        if detection_result.content_type == "tv_show":
            # Initialize TV naming helper for TV show organization
            tv_naming_helper = TVShowNamingHelper()
            from _internal.src.file_organizer import organize_tv_show
            return await organize_tv_show(content_info, main_file_path, download_dir, organized_base_dir, resolution, tv_naming_helper, logger_instance, settings_dict)
        else:
            return await _organize_movie(content_info, main_file_path, download_dir, organized_base_dir, resolution, logger_instance)

    except Exception as e:
        logger_instance.error(f"Error organizing content {download_dir.name}: {e}", exc_info=True)
        return False


async def _organize_tv_show(content_info: dict, main_file_path: str, download_dir: Path, organized_base_dir: Path, resolution: str, tv_naming_helper: TVShowNamingHelper, logger_instance) -> bool:
    """
    Organizes a TV show download using Plex-compatible naming structure.
    """
    try:
        # Parse from folder and file to capture season/episode (including multi-episode ranges)
        tv_info = tv_naming_helper.parse_tv_show_filename(download_dir.name)
        try:
            tv_info_file = tv_naming_helper.parse_tv_show_filename(Path(main_file_path).name)
            if tv_info_file.season:
                tv_info.season = tv_info_file.season
            if tv_info_file.episode:
                tv_info.episode = tv_info_file.episode
            if getattr(tv_info_file, "end_episode", None):
                tv_info.end_episode = tv_info_file.end_episode
            if getattr(tv_info_file, "episode_list", None):
                tv_info.episode_list = tv_info_file.episode_list
        except Exception:
            pass

        # Prefer authoritative metadata title/year from content_info (Sonarr/db) over parsed guesses
        if isinstance(content_info, dict):
            # Title/casing override: use Sonarr/db title when available
            meta_title = content_info.get("title") or content_info.get("cleaned_title")
            if meta_title:
                tv_info.series_title = str(meta_title)
            meta_year = content_info.get("year") or content_info.get("Year")
            if meta_year:
                tv_info.year = meta_year

        # ===== CRITICAL FIX: Use SONARR's official metadata instead of parsed folder name =====
        # The content_info contains the AUTHORITATIVE title and year from Sonarr
        # tv_info contains parsed data from folder name which is often wrong/messy

        # STEP 1: Override parsed title/year with Sonarr's authoritative metadata
        if content_info.get('title'):
            # Clean the Sonarr title by removing technical info if it got polluted
            sonarr_title = content_info['title']
            # If the title looks like a folder name with quality info, extract just the show name
            if any(tech in sonarr_title.lower() for tech in ['.', 'bluray', 'web-dl', '1080p', 'h264', 'remux']):
                # Extract just the series name from folder-style title
                # Look for patterns like "Show.Name.S01E01" and extract just "Show Name"
                import re
                match = re.match(r'^([^.]+(?:\.[^.]+)*?)\.S\d+', sonarr_title, re.IGNORECASE)
                if match:
                    clean_title = match.group(1).replace('.', ' ').strip()
                    logger_instance.info(f"🧹 Cleaned Sonarr title: '{sonarr_title}' → '{clean_title}'")
                    tv_info.series_title = clean_title
                else:
                    # Fallback: just replace dots with spaces and clean
                    clean_title = re.sub(r'\.(?=\S)', ' ', sonarr_title).strip()
                    tv_info.series_title = clean_title
                    logger_instance.info(f"🧹 Fallback cleaned title: '{sonarr_title}' → '{clean_title}'")
            else:
                # Title looks clean, use as-is
                tv_info.series_title = sonarr_title
                logger_instance.info(f"✅ Using clean Sonarr title: '{sonarr_title}'")

        # YEAR HANDLING FIX: Ensure year is consistently included from Sonarr metadata
        if content_info.get('year'):
            tv_info.year = content_info['year']
            logger_instance.info(f"✅ Using Sonarr year: {content_info['year']}")
        elif not tv_info.year:
            # If no year from Sonarr or parsing, try to get it from Sonarr API
            logger_instance.info("🔍 No year available, will query Sonarr for year information")
            try:
                if settings_dict:
                    sonarr_data = await _query_sonarr_for_show(tv_info.series_title, settings_dict, logger_instance)
                    if sonarr_data and sonarr_data.get('year'):
                        tv_info.year = sonarr_data['year']
                        logger_instance.info(f"📡 Retrieved year from Sonarr: {tv_info.year}")
            except Exception as e:
                logger_instance.warning(f"Failed to get year from Sonarr: {e}")

        # Additional fix: If the series title already contains a year, remove it for consistency
        if tv_info.series_title:
            import re
            # Remove years that might have been added to the title (like "Detective Conan 1996")
            original_title = tv_info.series_title
            tv_info.series_title = re.sub(r'\s+\(?\d{4}\)?$', '', tv_info.series_title).strip()
            if original_title != tv_info.series_title:
                logger_instance.info(f"🧹 Removed year from title: '{original_title}' → '{tv_info.series_title}'")

        logger_instance.info(f"🎯 FINAL TV METADATA: '{tv_info.series_title}' ({tv_info.year}) - from Sonarr authority")

        # Generate TV show folder structure using Sonarr's authoritative data (same approach as movies use Radarr data)
        logger_instance.info(f"📁 Using Sonarr metadata directly (no year resolver needed)")

        # Log parsed info
        s = f"S{tv_info.season:02d}" if tv_info.season else "S??"
        if tv_info.episode:
            e = f"E{tv_info.episode:02d}"
            if getattr(tv_info, "end_episode", None) and tv_info.end_episode != tv_info.episode:
                e = f"E{tv_info.episode:02d}-{tv_info.end_episode:02d}"
        else:
            e = ""
        logger_instance.info(f"Parsed TV show info: {tv_info.series_title} {s}{e}")

        # Build final destination paths
        plex_structure = tv_naming_helper.generate_plex_tv_structure(tv_info)
        if resolution:
            tv_shows_dir = organized_base_dir / "tv_shows" / resolution
        else:
            tv_shows_dir = organized_base_dir / "tv_shows"
        final_parent_dir = tv_shows_dir / plex_structure['full_series_path']

        # Build episode filename (preserving original extension)
        extension = Path(main_file_path).suffix
        episode_filename = plex_structure['episode_filename'].replace('.mkv', extension)
        final_file_path = final_parent_dir / episode_filename

        # Cleanup: remove legacy episode path that lacked (Year) in series folder
        try:
            legacy_series = getattr(tv_naming_helper, "_clean_series_title", lambda x: x)(tv_info.series_title)
            if tv_info.season:
                legacy_parent = tv_shows_dir / legacy_series / f"Season {tv_info.season:02d}"
            else:
                legacy_parent = tv_shows_dir / legacy_series
            if legacy_parent.exists() and legacy_parent != final_parent_dir:
                # Remove any matching SxxEyy* files to avoid duplicates
                ep_stem = episode_filename.split('.')[0]  # SxxEyy or SxxEyy-zz
                for f in legacy_parent.glob(f"{ep_stem}*"):
                    try:
                        logger_instance.info(f"Removing legacy episode file: {f}")
                        f.unlink(missing_ok=True)
                    except Exception:
                        pass
                # If legacy series folder becomes empty, leave folder; safe to keep structure

            # Additional cleanup: remove incorrectly created (2024) series folder if present from earlier runs
            try:
                if tv_info.year and str(tv_info.year) != "2024":
                    wrong_series_folder = f"{legacy_series} (2024)"
                    if tv_info.season:
                        wrong_parent = tv_shows_dir / wrong_series_folder / f"Season {tv_info.season:02d}"
                    else:
                        wrong_parent = tv_shows_dir / wrong_series_folder
                    if wrong_parent.exists() and wrong_parent != final_parent_dir:
                        ep_stem = episode_filename.split('.')[0]
                        for f in wrong_parent.glob(f"{ep_stem}*"):
                            try:
                                logger_instance.info(f"Removing incorrectly year-tagged episode file: {f}")
                                f.unlink(missing_ok=True)
                            except Exception:
                                pass
            except Exception:
                pass
        except Exception:
            pass

        # If an episode already exists there, replace it to avoid clutter
        if final_file_path.exists():
            try:
                existing_size = final_file_path.stat().st_size
                new_size = Path(main_file_path).stat().st_size
                reason = "same size" if existing_size == new_size else ("newer is larger" if new_size > existing_size else "newer is smaller")
                logger_instance.info(f"Episode already exists, replacing ({reason}): {final_file_path}")
                final_file_path.unlink(missing_ok=True)
            except Exception as ex:
                logger_instance.warning(f"Could not remove existing episode ({final_file_path}): {ex}. Attempting overwrite.")

        # Ensure target directory and move the file
        if ensure_dir_exists(final_parent_dir, logger_instance):
            # LONG PATH HANDLING: Check if main_file_path is too long for Windows
            actual_main_file_path = main_file_path
            if len(main_file_path) > 250:  # Windows path limit is 260, give some buffer
                logger_instance.warning(f"🛡️  LONG PATH DETECTED: {len(main_file_path)} chars - attempting to find accessible file")

                # Try to find the actual file using directory traversal
                try:
                    # Navigate to the download directory and find the MKV file
                    download_path = Path(download_dir)
                    found_mkv = None

                    # Look for MKV files in the download directory
                    from _internal.src.fs_helpers import find_video_files
                    for potential_file in find_video_files(download_path):
                        try:
                            # Check if this file is accessible and has a reasonable size
                            if potential_file.exists() and potential_file.stat().st_size > 100 * 1024 * 1024:  # > 100MB
                                found_mkv = potential_file
                                logger_instance.info(f"     LONG PATH FIX: Found accessible MKV: {found_mkv.name}")
                                break
                        except (OSError, FileNotFoundError):
                            continue

                    if found_mkv:
                        actual_main_file_path = str(found_mkv)
                        logger_instance.info(f"     LONG PATH SUCCESS: Using accessible path with {len(actual_main_file_path)} chars")
                    else:
                        logger_instance.error(f"     LONG PATH FAILURE: No accessible MKV found in {download_dir}")
                        return False

                except Exception as e:
                    logger_instance.error(f"     LONG PATH ERROR: Failed to find accessible file: {e}")
                    return False

            # SEASON PACK HANDLING: Check if this is a season pack with multiple episodes
            download_path = Path(download_dir)
            from _internal.src.fs_helpers import find_video_files
            mkv_files = find_video_files(download_path)

            if len(mkv_files) > 1:
                # Treat as season pack only if file names map to distinct episode numbers
                parsed_eps = []
                distinct_ep_numbers = set()
                for _f in mkv_files:
                    try:
                        _info = tv_naming_helper.parse_tv_show_filename(_f.name)
                        if _info.episode:
                            distinct_ep_numbers.add((_info.season, _info.episode))
                    except Exception:
                        continue
                if len(distinct_ep_numbers) <= 1:
                    logger_instance.info("Not a true season pack (single episode repeated); falling back to single-file logic")
                else:
                    logger_instance.info(f"🎯 SEASON PACK DETECTED: Found {len(mkv_files)} potential episode files ({len(distinct_ep_numbers)} distinct episodes)")

                # Organize each episode in the season pack
                organized_count = 0
                failed_count = 0

                for mkv_file in mkv_files:
                    try:
                        # Parse episode info from individual file
                        file_tv_info = tv_naming_helper.parse_tv_show_filename(mkv_file.name)

                        # Use series info from main parsing but episode info from individual file
                        file_tv_info.series_title = tv_info.series_title
                        file_tv_info.year = tv_info.year
                        file_tv_info.season = tv_info.season  # Season should be same for all files

                        # Generate structure for this specific episode
                        file_plex_structure = tv_naming_helper.generate_plex_tv_structure(file_tv_info)
                        file_final_parent_dir = tv_shows_dir / file_plex_structure['full_series_path']

                        # Build episode filename
                        file_extension = mkv_file.suffix
                        file_episode_filename = file_plex_structure['episode_filename'].replace('.mkv', file_extension)
                        file_final_path = file_final_parent_dir / file_episode_filename

                        # Ensure target directory exists
                        if ensure_dir_exists(file_final_parent_dir, logger_instance):
                            # Handle existing files
                            if file_final_path.exists():
                                try:
                                    existing_size = file_final_path.stat().st_size
                                    new_size = mkv_file.stat().st_size
                                    reason = "same size" if existing_size == new_size else ("newer is larger" if new_size > existing_size else "newer is smaller")
                                    logger_instance.info(f"Episode already exists, replacing ({reason}): {file_final_path}")
                                    file_final_path.unlink(missing_ok=True)
                                except Exception as ex:
                                    logger_instance.warning(f"Could not remove existing episode ({file_final_path}): {ex}. Attempting overwrite.")

                            # Move the episode file
                            if safe_move_file(str(mkv_file), str(file_final_path), logger_instance=logger_instance):
                                organized_count += 1
                                logger_instance.info(f"   ✅ Organized: {file_episode_filename}")
                            else:
                                failed_count += 1
                                logger_instance.error(f"   ❌ Failed to move: {mkv_file.name}")
                        else:
                            failed_count += 1
                            logger_instance.error(f"   ❌ Could not create directory: {file_final_parent_dir}")

                    except Exception as e:
                        failed_count += 1
                        logger_instance.error(f"   ❌ Error organizing {mkv_file.name}: {e}")

                logger_instance.info(f"🎯 SEASON PACK RESULTS: {organized_count} organized, {failed_count} failed")

                # Quarantine logic for safety
                if organized_count == 0:
                    logger_instance.error("Failed to organize any files from season pack. Leaving raw folder for manual review.")
                    organization_success = False
                else:
                    remaining = list(Path(download_dir).rglob('*.mkv'))
                    if remaining and failed_count:
                        quarantine_dir = organized_base_dir / 'issues_hold' / Path(download_dir).name
                        quarantine_dir.mkdir(parents=True, exist_ok=True)
                        for leftover in remaining:
                            try:
                                target_q = quarantine_dir / leftover.name
                                leftover.replace(target_q)
                                logger_instance.warning(f"⚠️ Quarantined leftover: {leftover.name}")
                            except Exception:
                                pass
                        logger_instance.warning("Partial success with leftovers quarantined; raw folder cleaned.")
                        safe_delete_folder(str(download_dir), ignore_errors=True)
                    elif failed_count == 0:
                        logger_instance.info("All season pack files organized; removing raw folder.")
                        safe_delete_folder(str(download_dir), ignore_errors=True)
                    else:
                        logger_instance.info("Keeping raw folder (no actionable leftovers).")
                    organization_success = True

                    # CRITICAL FIX: Create organized markers for season pack
                    # This ensures the system knows the content has been processed
                    if organization_success and organized_count > 0:
                        try:
                            from _internal.utils.episode_marker_manager import get_episode_marker_manager

                            # Create episode-aware markers for each organized episode
                            logger_instance.info(f"🏷️ Creating .organized markers for {organized_count} season pack episodes...")

                            # Get all unique season/episode combinations that were organized
                            unique_episodes = set()
                            for mkv_file in mkv_files:
                                try:
                                    file_tv_info = tv_naming_helper.parse_tv_show_filename(mkv_file.name)
                                    if file_tv_info.episode and file_tv_info.season:
                                        unique_episodes.add((file_tv_info.season, file_tv_info.episode))
                                except Exception:
                                    continue

                            # Create markers for each episode
                            episode_mgr = get_episode_marker_manager(final_parent_dir, ".organized")
                            markers_created = 0

                            for season, episode in unique_episodes:
                                success = episode_mgr.add_episode(season, episode, logger_instance)
                                if success:
                                    markers_created += 1
                                    logger_instance.info(f"   ✅ Episode marker created for S{season:02d}E{episode:02d}")
                                else:
                                    logger_instance.warning(f"   ⚠️ Failed to create episode marker for S{season:02d}E{episode:02d}")

                            # Fallback: create simple .organized marker if episode markers failed
                            if markers_created == 0:
                                logger_instance.warning("Episode-aware markers failed for season pack, using simple .organized marker")
                                try:
                                    from _internal.src.marker_utils import write_organized_marker
                                    write_organized_marker(final_parent_dir, {'marker':'season_pack_fallback'})
                                except Exception:
                                    (final_parent_dir / ".organized").touch(exist_ok=True)
                                logger_instance.info("✅ Simple .organized marker created for season pack")
                            else:
                                logger_instance.info(f"✅ Created {markers_created} episode-aware markers for season pack")

                        except Exception as e:
                            # Final fallback to simple marker
                            logger_instance.warning(f"Enhanced marker system failed for season pack, using simple marker: {e}")
                            try:
                                from _internal.src.marker_utils import write_organized_marker
                                write_organized_marker(final_parent_dir, {'marker':'season_pack_fallback_exception'})
                            except Exception:
                                (final_parent_dir / ".organized").touch(exist_ok=True)
                            logger_instance.info("✅ Fallback .organized marker created for season pack")

                # Return success status for season pack processing
                logger_instance.info(f"✅ Season pack organized successfully: {organized_count} episodes processed")
                return organization_success

            else:
                # Single episode handling (original logic)
                if safe_move_file(actual_main_file_path, str(final_file_path), logger_instance=logger_instance):
                    logger_instance.info("Successfully organized TV show file. Cleaning up raw download folder.")
                    safe_delete_folder(str(download_dir), ignore_errors=True)
                    organization_success = True
                else:
                    organization_success = False

                # Enhanced context-aware marker system for TV shows
                try:
                    from _internal.utils.episode_marker_manager import get_episode_marker_manager
                    from datetime import datetime

                    # For TV shows, use episode-aware marker system directly
                    # No need for FilesystemFirstStateManager which creates workspace folders

                    # Try episode-aware marker first
                    episode_mgr = get_episode_marker_manager(final_parent_dir, ".organized")
                    success = episode_mgr.add_episode(tv_info.season, tv_info.episode, logger_instance)

                    if success:
                        logger_instance.info(f"✅ Episode-aware marker set for S{tv_info.season:02d}E{tv_info.episode:02d}")
                    else:
                        # Fallback to simple marker
                        logger_instance.warning("Episode-aware marker failed, using simple .organized marker")
                        try:
                            from _internal.src.marker_utils import write_organized_marker
                            write_organized_marker(final_parent_dir, {'marker':'single_episode_fallback'})
                        except Exception:
                            (final_parent_dir / ".organized").touch(exist_ok=True)
                        logger_instance.info("✅ Simple .organized marker created")

                except Exception as e:
                    # Fallback to original episode marker system
                    logger_instance.warning(f"Enhanced marker system failed, using episode marker: {e}")
                    try:
                        from _internal.utils.episode_marker_manager import get_episode_marker_manager
                        marker_manager = get_episode_marker_manager(final_parent_dir, ".organized")
                        success = marker_manager.add_episode(tv_info.season, tv_info.episode, logger_instance)

                        if not success:
                            try:
                                from _internal.src.marker_utils import write_organized_marker
                                write_organized_marker(final_parent_dir, {'marker':'single_episode_fallback2'})
                            except Exception:
                                (final_parent_dir / ".organized").touch(exist_ok=True)

                    except Exception as e2:
                        # Final fallback to simple marker
                        logger_instance.warning(f"Episode marker system failed, using simple marker: {e2}")
                        try:
                            from _internal.src.marker_utils import write_organized_marker
                            write_organized_marker(final_parent_dir, {'marker':'single_episode_fallback3'})
                        except Exception:
                            (final_parent_dir / ".organized").touch(exist_ok=True)

                logger_instance.info(f"✅ TV show organized successfully: {final_file_path}")
                return organization_success
        else:
            logger_instance.error(f"Failed to create TV show directory: {final_parent_dir}")
            return False

    except Exception as e:
        logger_instance.error(f"Error organizing TV show {download_dir.name}: {e}", exc_info=True)
        return False


async def _organize_movie(content_info: dict, main_file_path: str, download_dir: Path, organized_base_dir: Path, resolution: str, logger_instance, settings_dict=None) -> bool:
    """Delegate to extracted file_organizer.organize_movie for maintainability."""
    from _internal.src.file_organizer import organize_movie
    return await organize_movie(content_info, main_file_path, download_dir, organized_base_dir, resolution, logger_instance, settings_dict)


async def _organize_completed_movie(movie: dict, main_movie_file_path: str, download_dir: Path, settings_dict: dict, logger_instance) -> bool:
    """
    LEGACY WRAPPER: Organizes a completed movie download using the new content type detection system.
    This function now routes through the new _organize_completed_content function.

    Args:
        movie: Movie data dictionary
        main_movie_file_path: Path to the main movie file
        download_dir: The download directory containing the movie
        settings_dict: Settings dictionary
        logger_instance: Logger instance

    Returns:
    """
    # Route through the new content-aware organizer
    return await _organize_completed_content(movie, main_movie_file_path, download_dir, settings_dict, logger_instance)


# Main stage function for orchestrator compatibility
def run_download_and_organize_stage(movies_data_list: List[Dict[str, Any]], settings_dict: Dict[str, Any], logger_instance: logging.Logger, mcp_manager=None) -> bool:
    # UNIFIED Stage 02: Download monitoring and organization for BOTH movies and TV shows
    async def run_both_monitors():
        # Monitor both Radarr (movies) and Sonarr (TV shows)
        radarr_success = await monitor_radarr_downloads(settings_dict, logger_instance, mcp_manager)
        sonarr_success = await monitor_sonarr_downloads(settings_dict, logger_instance, mcp_manager)
        return radarr_success and sonarr_success

    return asyncio.run(run_both_monitors())

if __name__ == "__main__":
    # Start terminal output logging
    with start_terminal_logging("02_download_and_organize"):
        # Standalone execution for Pipeline 2 - Download and Organize monitoring
        # Preserves all MCP capabilities while allowing manual testing
        print("*** UNIFIED Stage 02: Download and Organize ***")
        print("=" * 50)
        print("+ Consolidated from multiple O2 scripts into one unified implementation")
        print(">> Modern Radarr API integration")
        print("-- Simplified workflow: Radarr -> SABnzbd -> Plex")
        print(">> Clean, maintainable codebase")

        import asyncio
        import logging
        from pathlib import Path
        import argparse

        # Parse command line arguments
        parser = argparse.ArgumentParser(description='Download and Organize Script - Pipeline 02')
        parser.add_argument('--movies-only', action='store_true',
                           help='Process only movies (command-line mode)')
        parser.add_argument('--tv-only', action='store_true',
                           help='Process only TV shows (command-line mode)')
        parser.add_argument('--all', action='store_true',
                           help='Process both movies and TV shows (command-line mode)')
        parser.add_argument('--monitor', action='store_true',
                           help='Background monitoring mode')

        args = parser.parse_args()

        print("   Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)")

        # Import settings loader with fallback
        try:
            from utils.common_helpers import load_settings
        except ImportError:
            import sys
            sys.path.insert(0, str(Path(__file__).parent.parent))
            from src.utils.common_helpers import load_settings

        async def robust_download_monitor():
            """
            New robust download monitor using file-based discovery.
            No manual JSON editing required!
            """
            # Setup logging for robust execution
            import logging
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            robust_logger = logging.getLogger("robust_pipeline_02")

            robust_logger.info("     Starting Robust Download Monitor (No Manual JSON Editing!)")

            # Initialize robust state management
            if create_robust_state_manager is None:
                robust_logger.warning("Robust state management not available, falling back to legacy system")
                return await standalone_download_monitor()

            state_manager = create_filesystem_first_manager()
            idempotent_ops = create_idempotent_operations(state_manager)

            # Discover movies by scanning filesystem
            robust_logger.info("     Discovering movies by scanning filesystem...")
            movies_by_stage = state_manager.discover_movies_by_stage()

            # Process completed downloads
            download_completed = movies_by_stage.get('download_completed', [])
            robust_logger.info(f"Found {len(download_completed)} movies ready for organization")

            if not download_completed:
                robust_logger.info("No movies found ready for organization")
                return

            # Process each movie
            for movie_info in download_completed:
                movie_name = f"{movie_info.get('title', 'Unknown')} ({movie_info.get('year', 'Unknown')})"
                robust_logger.info(f"     Processing: {movie_name}")

                try:
                    # Use idempotent organization (safe to run multiple times)
                    success, updated_info = idempotent_ops.safe_organize_movie(movie_info)

                    if success:
                        robust_logger.info(f"    Successfully organized: {movie_name}")
                        # Update idempotency index as well
                        try:
                            from _internal.src.idempotency import IdempotencyIndex, compute_movie_key
                            idx = IdempotencyIndex()
                            idx.mark_processed(compute_movie_key(movie_info), 'organized', {'movie_directory': updated_info.get('movie_directory')})
                        except Exception:
                            pass

                        # TODO: Add Radarr cleanup here if needed
                        # await cleanup_completed_downloads_from_radarr(...)

                    else:
                        robust_logger.error(f"    Failed to organize: {movie_name}")

                except Exception as e:
                    robust_logger.error(f"Error processing {movie_name}: {e}")

            robust_logger.info("     Robust download monitoring complete!")

        async def standalone_download_monitor():
            # Setup logging for standalone execution
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            standalone_logger = logging.getLogger("standalone_pipeline_02")

            try:
                standalone_logger.info("===== Starting Standalone Pipeline 02 Execution =====")

                # Load settings
                settings_dict = load_settings("_internal/config/settings.ini")
                standalone_logger.info("Settings loaded successfully")

                # Run SQLite-based monitoring for BOTH movies and TV shows
                standalone_logger.info("🎬 Starting Radarr (Movies) monitoring...")
                radarr_success = await monitor_radarr_downloads(settings_dict, standalone_logger, mcp_manager=None)

                standalone_logger.info("📺 Starting Sonarr (TV Shows) monitoring...")
                sonarr_success = await monitor_sonarr_downloads(settings_dict, standalone_logger, mcp_manager=None)

                overall_success = radarr_success and sonarr_success
                if overall_success:
                    standalone_logger.info("    ✅ Pipeline 02 completed successfully (Movies + TV Shows)")
                else:
                    standalone_logger.error(f"    ❌ Pipeline 02 had issues - Radarr: {radarr_success}, Sonarr: {sonarr_success}")

                standalone_logger.info("===== Finished Standalone Pipeline 02 Execution =====")

            except Exception as e:
                standalone_logger.error(f"Error in standalone execution: {e}", exc_info=True)

        # Interactive media selection
        def display_interactive_menu():
            """
            Display the main interactive menu for content type selection.

            Returns:
                str: Selected content type ('movies', 'tv_shows', 'both', 'quit')
            """
            print(f"\n{'='*60}")
            print(f"🎬📺 PlexMovieAutomator - Interactive Content Selection")
            print(f"{'='*60}")
            print(f"\nWhat type of content would you like to process?")
            print(f"  1. Movies only")
            print(f"  2. TV Shows only")
            print(f"  3. Both Movies and TV Shows")
            print(f"  4. Quit")

            while True:
                try:
                    choice = input(f"\nEnter your choice [1-4]: ").strip()

                    if choice == '1':
                        return 'movies'
                    elif choice == '2':
                        return 'tv_shows'
                    elif choice == '3':
                        return 'both'
                    elif choice == '4':
                        return 'quit'
                    else:
                        print(f"Please enter a number between 1 and 4")

                except KeyboardInterrupt:
                    print(f"\n👋 Exiting...")
                    return 'quit'

        async def main_download_monitor():
            # Setup logging for execution
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            main_logger = logging.getLogger("pipeline_02")

            try:
                main_logger.info("===== Starting Pipeline 02 Execution =====")

                # Load settings
                settings_dict = load_settings("_internal/config/settings.ini")
                main_logger.info("Settings loaded successfully")

                # Determine content type based on arguments or interactive selection
                if args.movies_only or args.tv_only or args.all:
                    # Command line mode
                    if args.movies_only:
                        content_type_choice = 'movies'
                    elif args.tv_only:
                        content_type_choice = 'tv_shows'
                    else:  # args.all
                        content_type_choice = 'both'
                    main_logger.info(f"Command-line mode: Processing {content_type_choice}")
                else:
                    # Interactive mode (default)
                    content_type_choice = display_interactive_menu()
                    
                    if content_type_choice == 'quit':
                        main_logger.info("👋 User chose to quit")
                        return

                # Process based on user selection
                radarr_success = True
                sonarr_success = True
                
                if content_type_choice in ['movies', 'both']:
                    main_logger.info("🎬 Starting Radarr (Movies) monitoring...")
                    radarr_success = await monitor_radarr_downloads(settings_dict, main_logger, mcp_manager=None)

                if content_type_choice in ['tv_shows', 'both']:
                    main_logger.info("📺 Starting Sonarr (TV Shows) monitoring...")
                    sonarr_success = await monitor_sonarr_downloads(settings_dict, main_logger, mcp_manager=None)

                overall_success = radarr_success and sonarr_success
                if overall_success:
                    if content_type_choice == 'movies':
                        main_logger.info("    ✅ Pipeline 02 completed successfully (Movies)")
                    elif content_type_choice == 'tv_shows':
                        main_logger.info("    ✅ Pipeline 02 completed successfully (TV Shows)")
                    else:
                        main_logger.info("    ✅ Pipeline 02 completed successfully (Movies + TV Shows)")
                else:
                    main_logger.error(f"    ❌ Pipeline 02 had issues - Radarr: {radarr_success}, Sonarr: {sonarr_success}")

                main_logger.info("===== Finished Pipeline 02 Execution =====")

            except Exception as e:
                main_logger.error(f"Error in execution: {e}", exc_info=True)

        # Run main monitoring with user choice or command-line args
        asyncio.run(main_download_monitor())
