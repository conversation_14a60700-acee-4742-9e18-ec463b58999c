#!/usr/bin/env python3
"""
PlexMovieAutomator/01_intake_and_nzb_search.py

Auto-activates virtual environment if not already active.
"""

import sys
import os
from pathlib import Path

# Auto-activate virtual environment
def ensure_venv():
    """Ensure we're running in the virtual environment"""
    # Get the current script directory
    root_dir = Path(__file__).parent
    venv_python = root_dir / "_internal" / "venv" / "Scripts" / "python.exe"

    # Check if we're already in venv or if venv doesn't exist
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # Already in virtual environment
        return

    if venv_python.exists():
        # Re-run this script with venv python
        import subprocess
        print(f"🔄 Activating virtual environment: {venv_python}")
        result = subprocess.run([str(venv_python)] + sys.argv, cwd=str(root_dir))
        sys.exit(result.returncode)
    else:
        print("⚠️ Virtual environment not found, running with system Python")

# Activate venv before any other imports
# ensure_venv()  # Temporarily disabled for testing

"""
PlexMovieAutomator/01_intake_and_nzb_search.py

FILESYSTEM-FIRST ARCHITECTURE:
Handles new movie requests, fetches metadata, and adds to Radarr.
Uses filesystem scanning for state discovery and metadata-only database.
No status tracking - Radarr manages downloads, Script 2 discovers completions.
"""
import sys
import time
import json
import logging
import asyncio
from pathlib import Path
from datetime import datetime, timezone, timedelta

# Import terminal logger
sys.path.insert(0, str(Path(__file__).parent / "_internal"))
from utils.terminal_logger import start_terminal_logging
from typing import Dict, List, Any, Tuple

# Utility imports with fallback
sys.path.insert(0, str(Path(__file__).parent))

# Setup paths for clean imports
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

try:
    from _internal.src.radarr_integration import RadarrClient
    from _internal.src.sonarr_integration import SonarrClient
    from _internal.src.intelligent_fallback_system import IntelligentFallbackSystem

    from utils.common_helpers import (
        get_path_setting,
        get_setting
    )
    from utils.metadata_apis import fetch_movie_metadata_for_intake
    from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
    from utils.telemetry_integration import (
        TelemetryIntegrator,
        verify_downloads_with_enhanced_tracking,
        display_enhanced_download_report
    )
except ImportError as e:
    # Fallback for testing
    from datetime import datetime, timezone, timedelta

    def get_path_setting(section, key, settings_dict=None, default=None):
        return settings_dict.get(section, {}).get(key, default) if settings_dict else default

    def get_setting(section, key, settings_dict=None, default=None, expected_type=str):
        return settings_dict.get(section, {}).get(key, default) if settings_dict else default

    # Import filesystem-first state manager for fallback
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent / "_internal"))
    from utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase

    # NOTE: No fallback metadata functions since the real ones are not async

# Modern API imports (replaced Selenium)
import aiohttp
import re

# --- TV Show Metadata Function ---
from _internal.src.metadata_fetcher import fetch_tv_metadata_for_intake  # Delegated to extracted module


from _internal.src.metadata_fetcher import fetch_tvdb_episode_data  # Delegated to extracted module


async def create_chronological_episode_queue(parsed_request: dict, tvdb_data: dict, logger_instance: logging.Logger) -> list:
    """
    Create a chronological episode download queue based on request type and TVDB data.

    Args:
        parsed_request: Parsed TV show request
        tvdb_data: TVDB episode data
        logger_instance: Logger instance

    Returns:
        list: Ordered list of episode dicts to download in chronological order
    """
    episode_queue = []

    try:
        if not tvdb_data.get("success"):
            logger_instance.error(f"Cannot create episode queue: TVDB data fetch failed")
            return []

        request_type = parsed_request.get("request_type")
        chronological_episodes = tvdb_data.get("chronological_episodes", [])

        if request_type == "full_series":
            # Add all episodes in chronological order
            episode_queue = chronological_episodes.copy()
            logger_instance.info(f"Created full series queue: {len(episode_queue)} episodes")

        elif request_type == "specific_season":
            # Add all episodes from requested season(s)
            requested_seasons = parsed_request.get("seasons", [])
            for episode in chronological_episodes:
                if episode["season"] in requested_seasons:
                    episode_queue.append(episode)
            logger_instance.info(f"Created season queue: {len(episode_queue)} episodes from seasons {requested_seasons}")

        elif request_type in ["specific_episodes", "multiple_episodes"]:
            # Add only requested episodes, but maintain chronological order
            requested_episodes = parsed_request.get("episodes", [])
            requested_set = {(ep["season"], ep["episode"]) for ep in requested_episodes}

            for episode in chronological_episodes:
                episode_key = (episode["season"], episode["episode"])
                if episode_key in requested_set:
                    episode_queue.append(episode)

            logger_instance.info(f"Created specific episode queue: {len(episode_queue)} episodes")

        # Add progress tracking metadata to each episode
        for i, episode in enumerate(episode_queue):
            episode["queue_position"] = i + 1
            episode["total_in_queue"] = len(episode_queue)
            episode["download_status"] = "pending"

        return episode_queue

    except Exception as e:
        logger_instance.error(f"Error creating episode queue: {e}")
        return []

# --- Modern Radarr API Functions (replaces Chrome driver workflow) ---

from _internal.src.quality_strategy import determine_movie_quality_profile_by_year as _determine_quality_profile_by_year
from _internal.src.quality_strategy import determine_tv_quality_profile_by_year as _determine_tv_quality_profile_by_year

# [GEMINI-REFACTOR] Modified to accept the download directory directly for clarity and reliability.
async def _add_movie_to_radarr_modern(movie_data: dict, settings_dict: dict, logger_instance: logging.Logger) -> dict:
    """
    Modern replacement for Chrome driver NZB search: Add movie directly to Radarr via API.

    This replaces the entire Chrome driver + NZB scraping workflow with a single API call
    that leverages Radarr's built-in search capabilities and Prowlarr integration.

    Implements year-based quality selection:
    - ≤2009: Largest 1080p only
    - 2010-2015: BOTH 1080p AND 4K
    - 2016+: Largest 4K only

    Returns: {"success": bool, "reason": str, "radarr_id": int|None}
    """
    import aiohttp

    # Get Radarr configuration
    radarr_url = get_setting("Radarr", "url", settings_dict=settings_dict, default="http://localhost:7878")
    radarr_api_key = get_setting("Radarr", "api_key", settings_dict=settings_dict)

    if not radarr_api_key:
        logger_instance.error("Radarr API key not configured in settings")
        return {"success": False, "reason": "missing_api_key"}

    try:
        cleaned_title = movie_data.get("cleaned_title", "")
        year = movie_data.get("year", "")
        tmdb_id = movie_data.get("tmdb_id")

        if not cleaned_title or not year:
            return {"success": False, "reason": "insufficient_metadata"}

        search_term = f"{cleaned_title} {year}"
        headers = {'X-Api-Key': radarr_api_key}

        # Determine quality strategy based on year
        quality_strategy = _determine_quality_profile_by_year(year, logger_instance, settings_dict)

        async with aiohttp.ClientSession() as session:
            logger_instance.info(f"Searching Radarr for: {search_term}")
            radarr = RadarrClient(radarr_url, radarr_api_key)

            # Search for the movie first
            search_results = await radarr.movie_lookup(session, search_term)
            if not search_results:
                return {"success": False, "reason": "no_search_results"}

            # Find best match (prefer TMDB ID match, then title/year match)
            best_match = None
            if tmdb_id:
                best_match = next((r for r in search_results if r.get('tmdbId') == tmdb_id), None)
            if not best_match:
                best_match = search_results[0]
            logger_instance.info(f"Found match: {best_match.get('title')} ({best_match.get('year')})")

            # Check if movie already exists in Radarr
            existing_movies = await radarr.get_movies(session)
            movie_tmdb_id = best_match.get('tmdbId')
            logger_instance.info(f"🔍 Checking for duplicates: TMDB ID {movie_tmdb_id} in {len(existing_movies)} existing movies")
            
            # Debug: Show all TMDB IDs in Radarr for comparison
            if existing_movies:
                radarr_tmdb_ids = [m.get('tmdbId') for m in existing_movies if m.get('tmdbId')]
                logger_instance.info(f"📊 Radarr TMDB IDs: {radarr_tmdb_ids}")
            
            existing_movie = next((m for m in existing_movies if m.get('tmdbId') == movie_tmdb_id), None)
            
            if existing_movie:
                logger_instance.info(f"🎯 DUPLICATE FOUND: Movie TMDB {movie_tmdb_id} exists in Radarr with ID {existing_movie.get('id')}")
            else:
                logger_instance.info(f"✅ NO DUPLICATE: Movie TMDB {movie_tmdb_id} not found in Radarr - safe to add")

            if existing_movie:
                        # SMART CLEANUP: Check if movie actually exists in filesystem
                        logger_instance.info(f"🔍 Movie exists in Radarr, verifying filesystem reality: {search_term}")

                        # Check complete_raw directory
                        complete_raw_dir = None
                        if "Paths" in settings_dict and "download_complete_raw_dir" in settings_dict["Paths"]:
                            raw_path = settings_dict["Paths"]["download_complete_raw_dir"]
                            if "%(download_client_active_dir)s" in raw_path:
                                base_dir = settings_dict["Paths"].get("download_client_active_dir", "workspace/1_downloading")
                                complete_raw_dir = Path(raw_path.replace("%(download_client_active_dir)s", base_dir))
                            else:
                                complete_raw_dir = Path(raw_path)
                        else:
                            complete_raw_dir = Path("workspace/1_downloading/complete_raw")

                        # Check organized directories
                        organized_dirs = []
                        if "Paths" in settings_dict and "mkv_processing_output_dir" in settings_dict["Paths"]:
                            mkv_base = Path(settings_dict["Paths"]["mkv_processing_output_dir"])
                            organized_dirs = [mkv_base / "1080p", mkv_base / "4K", mkv_base / "SD_or_unknown"]
                        else:
                            base_dir = Path("workspace/2_downloaded_and_organized")
                            organized_dirs = [base_dir / "1080p", base_dir / "4K", base_dir / "SD_or_unknown"]

                        # Look for the movie in filesystem
                        movie_title = best_match.get('title', '').lower()
                        movie_year = str(best_match.get('year', ''))
                        found_in_filesystem = False

                        # Check complete_raw
                        if complete_raw_dir.exists():
                            for item in complete_raw_dir.iterdir():
                                if item.is_dir():
                                    item_name = item.name.lower()
                                    if movie_title in item_name and movie_year in item_name:
                                        found_in_filesystem = True
                                        logger_instance.info(f"✅ Found in complete_raw: {item.name}")
                                        break

                        # Check organized directories
                        if not found_in_filesystem:
                            for organized_dir in organized_dirs:
                                if organized_dir.exists():
                                    for movie_folder in organized_dir.iterdir():
                                        if movie_folder.is_dir():
                                            folder_name = movie_folder.name.lower()
                                            if movie_title in folder_name and movie_year in folder_name:
                                                found_in_filesystem = True
                                                logger_instance.info(f"✅ Found in organized: {movie_folder}")
                                                break
                                if found_in_filesystem:
                                    break

                        if found_in_filesystem:
                            logger_instance.info(f"✅ Movie exists in Radarr and filesystem: {search_term}")
                            return {"success": True, "reason": "already_exists", "radarr_movie": existing_movie}
                        else:
                            # ORPHANED ENTRY: Exists in Radarr but not in filesystem
                            logger_instance.warning(f"🧹 ORPHANED: Movie exists in Radarr but NOT in filesystem: {search_term}")
                            logger_instance.info(f"🧹 Cleaning up orphaned Radarr entry to enable fresh download...")

                            try:
                                # Simple cleanup: Remove the movie from Radarr
                                movie_id = existing_movie.get('id')
                                delete_url = f"{radarr_url}/api/v3/movie/{movie_id}?deleteFiles=false&addImportExclusion=false"

                                async with session.delete(delete_url, headers=headers) as delete_response:
                                    if delete_response.status == 200:
                                        logger_instance.info(f"✅ Removed orphaned movie from Radarr: {search_term}")

                                        # 🔄 VERIFICATION: Wait and verify cleanup worked
                                        logger_instance.info(f"⏳ Waiting 2 seconds for Radarr to process cleanup...")
                                        await asyncio.sleep(2)

                                        # 🔍 COMPREHENSIVE VERIFICATION: Check both Radarr and SABnzbd
                                        logger_instance.info(f"🔍 Comprehensive verification: Checking Radarr + SABnzbd...")

                                        verification_success = True

                                        # 1. Verify movie is gone from Radarr
                                        updated_movies = await radarr.get_movies(session)
                                        still_exists = any(m.get('tmdbId') == movie_tmdb_id for m in updated_movies)
                                        if not still_exists:
                                            logger_instance.info(f"✅ Radarr verification: Movie removed from library")
                                        else:
                                            logger_instance.warning(f"⚠️ Radarr verification: Movie still exists after cleanup")
                                            verification_success = False

                                        # 2. Verify movie is gone from Radarr queue
                                        queue_data = await radarr.get_queue(session)
                                        queue_records = queue_data.get('records', [])
                                        movie_title_lower = best_match.get('title', '').lower()
                                        queue_matches = [item for item in queue_records if movie_title_lower in item.get('title', '').lower()]

                                        if not queue_matches:
                                            logger_instance.info(f"✅ Radarr queue verification: No queue items found")
                                        else:
                                            logger_instance.warning(f"⚠️ Radarr queue verification: {len(queue_matches)} queue items still exist")
                                            verification_success = False

                                        # 3. Verify movie is gone from SABnzbd history
                                        try:
                                            sabnzbd_api_key = settings_dict.get("DownloadAndOrganize", {}).get("sabnzbd_api_key")
                                            sabnzbd_url = settings_dict.get("DownloadAndOrganize", {}).get("sabnzbd_url", "http://127.0.0.1:8080")

                                            if sabnzbd_api_key:
                                                history_url = f"{sabnzbd_url}/api"
                                                params = {
                                                    "mode": "history",
                                                    "apikey": sabnzbd_api_key,
                                                    "output": "json",
                                                    "limit": 20
                                                }

                                                async with session.get(history_url, params=params) as sab_response:
                                                    if sab_response.status == 200:
                                                        history_data = await sab_response.json()
                                                        history_slots = history_data.get("history", {}).get("slots", [])

                                                        # Check if any history entries match our movie
                                                        title_words = movie_title_lower.split()
                                                        year_str = str(best_match.get('year', ''))

                                                        sab_matches = []
                                                        for entry in history_slots:
                                                            entry_name = entry.get("name", "").lower()
                                                            if all(word in entry_name for word in title_words) and year_str in entry_name:
                                                                sab_matches.append(entry)

                                                        if not sab_matches:
                                                            logger_instance.info(f"✅ SABnzbd verification: No history entries found")
                                                        else:
                                                            logger_instance.warning(f"⚠️ SABnzbd verification: {len(sab_matches)} history entries still exist")
                                                            # Note: We might still proceed even if SABnzbd has history
                                                    else:
                                                        logger_instance.warning(f"Could not verify SABnzbd history: HTTP {sab_response.status}")
                                            else:
                                                logger_instance.info("No SABnzbd API key - skipping SABnzbd verification")

                                        except Exception as sab_error:
                                            logger_instance.warning(f"SABnzbd verification error: {sab_error}")

                                        # Final verification result
                                        if verification_success:
                                            logger_instance.info(f"✅ VERIFICATION COMPLETE: Movie fully removed, safe to proceed")
                                            logger_instance.info(f"🔄 Starting fresh download...")
                                        else:
                                            logger_instance.warning(f"⚠️ VERIFICATION ISSUES: Some cleanup may have failed")
                                            logger_instance.info(f"🔄 Proceeding anyway - Radarr may handle conflicts...")

                                    else:
                                        logger_instance.warning(f"⚠️ Failed to remove from Radarr: HTTP {delete_response.status}")
                                        logger_instance.info(f"🔄 Proceeding with download anyway...")

                            except Exception as cleanup_error:
                                logger_instance.error(f"Error during cleanup: {cleanup_error}")
                                logger_instance.info(f"🔄 Proceeding with download anyway...")

                        # After this point, continue with normal add-to-Radarr logic

            # Add movie to Radarr with year-based quality strategy
            # Select root folder via orchestrator helper
            from _internal.src.intake_movie_orchestrator import select_root_folder as _select_root
            root_folder_path = await _select_root(session, radarr_url, radarr_api_key, settings_dict, logger_instance)

            # Query Radarr for existing root folders no longer needed here (handled in helper)
            # root_folder_path already determined above by helper

            # Use year-based quality strategy instead of single profile
            profiles_to_add = quality_strategy["profiles"]
            strategy_description = quality_strategy["description"]

            logger_instance.info(f"📋 {strategy_description}")
            logger_instance.info(f"🎬 Adding movie to Radarr with {len(profiles_to_add)} quality profile(s): {search_term}")

            added_movies = []

            # Add movie for each quality profile in the strategy
            from _internal.src.intake_movie_orchestrator import add_with_profiles as _add_profiles
            added_movies = await _add_profiles(
                session, radarr_url, radarr_api_key, best_match, profiles_to_add, root_folder_path, search_term, logger_instance,
                search_for_movie=False
            )

            # Return success if at least one profile was added successfully
            if added_movies:
                return {
                    "success": True,
                    "reason": "successfully_added",
                    "radarr_movies": added_movies,
                    "radarr_id": added_movies[0].get("id"),  # Return first ID for compatibility
                    "quality_strategy": quality_strategy["strategy"],
                    "profiles_added": len(added_movies)
                }
            else:
                return {"success": False, "reason": "all_profiles_failed"}

    except Exception as e:
        logger_instance.error(f"Exception in Radarr API call for {search_term}: {e}", exc_info=True)
        return {"success": False, "reason": f"exception_{str(e)}"}


async def _find_existing_series_by_tvdb_id(session, sonarr_url: str, headers: dict, tvdb_id: int, logger_instance):
    """Find existing series in Sonarr by TVDB ID."""
    try:
        sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
        all_series = await sonarr.get_series(session)
        for series in all_series:
                    if series.get("tvdbId") == tvdb_id:
                        return series
        return None
    except Exception as e:
        logger_instance.error(f"Error finding existing series: {e}")
        return None


async def _configure_specific_episode_monitoring(session, sonarr_url: str, headers: dict, series_id: int,
                                               season_num: int, episode_num: int, logger_instance, settings_dict: dict | None = None,
                                               disable_immediate_search: bool = False):
    """Configure Sonarr to monitor only a specific episode and optionally trigger search."""
    try:
        # First, get the series details to find the specific episode
        async with session.get(f"{sonarr_url}/api/v3/series/{series_id}", headers=headers) as response:
            if response.status != 200:
                logger_instance.error(f"Failed to get series details for ID {series_id}")
                return False

            series_data = await response.json()

        # Update series to unmonitor all seasons first
        series_data["seasons"] = [
            {**season, "monitored": False} for season in series_data.get("seasons", [])
        ]

        # Monitor only the target season
        for season in series_data["seasons"]:
            if season.get("seasonNumber") == season_num:
                season["monitored"] = True
                break

        # Update the series
        sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
        ok = await sonarr.update_series(session, series_data)
        if not ok:
            logger_instance.error(f"Failed to update series monitoring for ID {series_id}")
            return False

        # Fetch episodes with retry because series refresh may still be populating
        episodes = []
        for attempt in range(1,4):
            async with session.get(f"{sonarr_url}/api/v3/episode",
                                  params={"seriesId": series_id}, headers=headers) as response:
                if response.status == 200:
                    episodes = await response.json()
                else:
                    logger_instance.error(f"Failed to get episodes for series ID {series_id} (attempt {attempt})")
            if any(ep.get("seasonNumber") == season_num and ep.get("episodeNumber") == episode_num for ep in episodes):
                break
            logger_instance.info(f"Episode S{season_num:02d}E{episode_num:02d} not found attempt {attempt}; waiting 2s and retrying")
            await asyncio.sleep(2)

        # Find and monitor only the specific episode
        target_episode = None
        episode_updates = []

        for episode in episodes:
            if (episode.get("seasonNumber") == season_num and
                episode.get("episodeNumber") == episode_num):
                # Monitor this episode
                episode["monitored"] = True
                target_episode = episode
                episode_updates.append(episode)
            elif episode.get("seasonNumber") == season_num:
                # Unmonitor other episodes in the same season
                episode["monitored"] = False
                episode_updates.append(episode)

        # Update episodes monitoring
        if episode_updates:
            sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
            # Unmonitor all first
            await sonarr.set_episodes_monitor_state(session, [ep["id"] for ep in episode_updates], False)

            if target_episode:
                # Monitor only the target episode
                ok = await sonarr.set_episodes_monitor_state(session, [target_episode["id"]], True)
                if ok:
                    logger_instance.info(f"   🎯 Configured monitoring for S{season_num:02d}E{episode_num:02d}")

                    # Attempt smart auto-selection/grab first to enforce quality/language preferences
                    try:
                        grabbed = await _smart_select_and_grab_episode_release(session, sonarr_url, headers, series_id, target_episode["id"], logger_instance, settings_dict)
                        if grabbed:
                            logger_instance.info("   🔎 Skipping EpisodeSearch because smart selection already grabbed a release.")
                            return True
                    except Exception as pre_e:
                        logger_instance.warning(f"   ⚠️ Pre-search smart selection failed: {pre_e}")

                    # Trigger automatic search for the specific episode (only if not disabled)
                    if not disable_immediate_search:
                        search_data = {"episodeIds": [target_episode["id"]]}
                        sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
                        cmd = await sonarr.issue_command(session, {"name": "EpisodeSearch", **search_data})
                        if cmd and isinstance(cmd, dict) and cmd.get('name'):
                            logger_instance.info(f"   🔍 Triggered automatic search for S{season_num:02d}E{episode_num:02d}")
                            # Run diagnostics (non-blocking critical path, but helpful for insight)
                            try:
                                await _diagnose_sonarr_episode_search(session, sonarr_url, headers, series_id, target_episode["id"], season_num, episode_num, logger_instance)
                            except Exception as diag_e:
                                logger_instance.warning(f"   ⚠️ Episode search diagnostics failed: {diag_e}")
                            # Compare Sonarr queued item vs largest manual candidate (same quality) and replace if larger
                            try:
                                replaced = await _compare_and_optionally_replace_queued_release(
                                    session,
                                    sonarr_url,
                                    headers,
                                    series_id,
                                    target_episode["id"],
                                    logger_instance,
                                    replace_min_mb=100,
                                    wait_for_queue_seconds=60,
                                    poll_interval_seconds=3.0
                                )
                                if replaced:
                                    logger_instance.info("   🔄 Replaced Sonarr's queued release with larger manual candidate.")
                            except Exception as cmp_e:
                                logger_instance.warning(f"   ⚠️ Compare/replace step failed: {cmp_e}")
                            # Attempt smart auto-selection/grab if nothing queued
                            try:
                                await _smart_select_and_grab_episode_release(session, sonarr_url, headers, series_id, target_episode["id"], logger_instance, settings_dict)
                            except Exception as grab_e:
                                logger_instance.warning(f"   ⚠️ Smart selection step failed: {grab_e}")
                            return True
                        else:
                            logger_instance.warning(f"   ⚠️ Failed to trigger search for S{season_num:02d}E{episode_num:02d}")
                    else:
                        logger_instance.info(f"   ⏭️ Skipping immediate search trigger for S{season_num:02d}E{episode_num:02d} (preflight will handle search)")

        return True if target_episode else False

    except Exception as e:
        logger_instance.error(f"Error configuring specific episode monitoring: {e}")
        return False


async def _configure_multiple_episodes_monitoring(session, sonarr_url: str, headers: dict, series_id: int,
                                                episodes_list: list, logger_instance, settings_dict: dict,
                                                disable_immediate_search: bool = False):
    """
    Configure Sonarr to monitor only specific episodes from a list and optionally trigger search.

    Args:
        episodes_list: List of episode dicts with 'season' and 'episode' keys
        e.g., [{'season': 1, 'episode': 1}, {'season': 2, 'episode': 5}]
    """
    import asyncio

    try:
        logger_instance.info(f"   📺 Configuring monitoring for {len(episodes_list)} specific episodes")

        # Wait for Sonarr to populate episode data after adding new series
        # This can take 10-30 seconds for Sonarr to fetch from TVDB
        max_retries = 15
        retry_delay = 4
        all_episodes = []

        for attempt in range(max_retries):
            logger_instance.info(f"   🔄 Attempt {attempt + 1}/{max_retries}: Fetching episodes for series {series_id}")

            # Get all episodes for the series
            async with session.get(f"{sonarr_url}/api/v3/episode?seriesId={series_id}", headers=headers) as response:
                if response.status != 200:
                    logger_instance.error(f"Failed to get episodes for series {series_id}: {response.status}")
                    return False

                all_episodes = await response.json()
                logger_instance.info(f"   📊 Found {len(all_episodes)} episodes in Sonarr")

                if all_episodes:
                    break

                if attempt < max_retries - 1:
                    logger_instance.info(f"   ⏳ Episodes not yet populated, waiting {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 1.5  # Exponential backoff

        if not all_episodes:
            logger_instance.error(f"No episodes found after {max_retries} attempts")
            return False

        logger_instance.info(f"   📋 Found {len(all_episodes)} episodes in series")

        # Create a set of target episodes for quick lookup
        target_episodes = {(ep['season'], ep['episode']) for ep in episodes_list}
        logger_instance.info(f"   🎯 Target episodes: {target_episodes}")

        # Track which episodes we need to monitor
        episodes_to_monitor = []
        episodes_to_unmonitor = []

        for episode in all_episodes:
            season_num = episode.get("seasonNumber")
            episode_num = episode.get("episodeNumber")
            episode_key = (season_num, episode_num)

            if episode_key in target_episodes:
                # Monitor this episode
                episode["monitored"] = True
                episodes_to_monitor.append(episode)
                logger_instance.info(f"      ✓ Will monitor S{season_num:02d}E{episode_num:02d}")
            else:
                # Unmonitor other episodes
                episode["monitored"] = False
                episodes_to_unmonitor.append(episode)

        if not episodes_to_monitor:
            logger_instance.warning(f"None of the requested episodes were found in the series")
            return False

        # Update episodes monitoring - use Sonarr v3 bulk approach
        logger_instance.info(f"   📝 Configuring monitoring for {len(episodes_to_monitor)} specific episodes")

        # First, set series to monitor nothing by default
        sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
        ok = await sonarr.update_series(session, {"id": series_id, "monitored": True, "seasonFolder": True})
        if not ok:
            logger_instance.warning(f"Failed to update series monitoring via client")

        # Use Sonarr's episode monitoring endpoint to set specific episodes
        episode_updates = []
        for episode in episodes_to_monitor:
            episode_updates.append({
                "episodeIds": [episode["id"]],
                "monitored": True
            })

        # Batch update monitored episodes
        if episode_updates:
            sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
            ok = await sonarr.set_episodes_monitor_state(session, [ep["id"] for ep in episodes_to_monitor], True)
            if ok:
                logger_instance.info(f"   ✅ Successfully configured monitoring for {len(episodes_to_monitor)} episodes")
            else:
                logger_instance.warning(f"Bulk episode monitoring failed via client; attempting per-episode fallback")
                # Fallback to individual updates if bulk fails
                for episode in episodes_to_monitor:
                    async with session.put(f"{sonarr_url}/api/v3/episode/{episode['id']}",
                                         headers={**headers, "Content-Type": "application/json"},
                                         json={"id": episode["id"], "monitored": True}) as ep_response:
                        if ep_response.status in [200, 202]:
                            logger_instance.debug(f"   ✓ Monitored episode {episode['id']}")
                        else:
                            logger_instance.warning(f"Failed to monitor episode {episode['id']}: {ep_response.status}")

        logger_instance.info(f"   ✅ Episode monitoring configuration complete")

        # Trigger search for monitored episodes (only if not disabled)
        if not disable_immediate_search:
            for episode in episodes_to_monitor:
                season_num = episode.get("seasonNumber")
                episode_num = episode.get("episodeNumber")
                sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
                cmd = await sonarr.issue_command(session, {"name": "EpisodeSearch", "episodeIds": [episode["id"]]})
                if cmd and isinstance(cmd, dict) and cmd.get('name'):
                    logger_instance.info(f"   🔍 Triggered search for S{season_num:02d}E{episode_num:02d}")
                else:
                    logger_instance.warning(f"Failed to trigger search for S{season_num:02d}E{episode_num:02d}")
        else:
            logger_instance.info(f"   ⏭️ Skipping immediate search triggers for {len(episodes_to_monitor)} episodes (preflight will handle searches)")

        return True

    except Exception as e:
        logger_instance.error(f"Error configuring multiple episodes monitoring: {e}")
        return False


async def _configure_specific_season_monitoring(session, sonarr_url: str, headers: dict, series_id: int,
                                              season_num: int, logger_instance, disable_immediate_search: bool = False):
    """Configure Sonarr to monitor only a specific season and optionally trigger episode searches."""
    try:
        # Get the series details
        async with session.get(f"{sonarr_url}/api/v3/series/{series_id}", headers=headers) as response:
            if response.status != 200:
                logger_instance.error(f"Failed to get series details for ID {series_id}")
                return False

            series_data = await response.json()

        # Monitor only the target season
        for season in series_data.get("seasons", []):
            season["monitored"] = (season.get("seasonNumber") == season_num)

        # Update the series
        sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
        ok = await sonarr.update_series(session, series_data)
        if ok:
                logger_instance.info(f"   🎯 Configured monitoring for Season {season_num}")

                # Get episodes for the target season and trigger searches to populate Sonarr's release cache
                async with session.get(f"{sonarr_url}/api/v3/episode",
                                     headers=headers, params={"seriesId": series_id}) as ep_response:
                    if ep_response.status == 200:
                        episodes = await ep_response.json()
                        season_episodes = [ep for ep in episodes if ep.get("seasonNumber") == season_num]

                        if season_episodes:
                            if not disable_immediate_search:
                                logger_instance.info(f"   🔍 Triggering searches for {len(season_episodes)} episodes to populate release cache")

                                # Trigger individual episode searches to populate Sonarr's release cache for preflight
                                for episode in season_episodes:
                                    episode_id = episode.get("id")
                                    episode_num = episode.get("episodeNumber")
                                    if episode_id:
                                        search_payload = {
                                            "name": "EpisodeSearch",
                                            "episodeIds": [episode_id]
                                        }
                                        try:
                                            sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
                                            cmd = await sonarr.issue_command(session, search_payload)
                                            if cmd and isinstance(cmd, dict) and cmd.get('name'):
                                                logger_instance.info(f"      ✓ Triggered search for S{season_num:02d}E{episode_num:02d}")
                                            else:
                                                logger_instance.warning(f"      ⚠️ Failed to trigger search for S{season_num:02d}E{episode_num:02d}")
                                        except Exception as search_err:
                                            logger_instance.warning(f"      ⚠️ Search trigger error for S{season_num:02d}E{episode_num:02d}: {search_err}")

                                # Wait a moment for searches to populate the cache
                                await asyncio.sleep(2)
                                logger_instance.info(f"   ✅ Episode searches triggered - preflight analyzer can now access release cache")
                            else:
                                logger_instance.info(f"   ⏭️ Skipping immediate search triggers (preflight will handle searches)")
                        else:
                            logger_instance.warning(f"   ⚠️ No episodes found for Season {season_num}")
                    else:
                        logger_instance.warning(f"   ⚠️ Failed to fetch episodes: {ep_response.status}")

                return True

    except Exception as e:
        logger_instance.error(f"Error configuring specific season monitoring: {e}")
        return False


async def sync_sonarr_cache_for_season_pack(
    session: aiohttp.ClientSession,
    sonarr_url: str,
    headers: dict,
    series_id: int,
    season_num: int,
    target_guid: str,
    pack_title: str,
    max_retries: int = 12,
    poll_interval: int = 5,
    logger_instance: logging.Logger = None
) -> Tuple[bool, str]:
    """
    Cache Synchronization Solution for Season Packs

    Synchronizes Sonarr's release cache with preflight analyzer findings by:
    1. Triggering a SeasonSearch to warm Sonarr's cache
    2. Polling for the target GUID to appear in cache
    3. Returning when cache is synchronized

    Args:
        session: Active aiohttp session
        sonarr_url: Sonarr base URL
        headers: API headers with authentication
        series_id: Sonarr series ID
        season_num: Season number to search
        target_guid: GUID of the season pack from preflight analyzer
        pack_title: Title of the season pack for logging
        max_retries: Maximum number of cache poll attempts
        poll_interval: Seconds between cache polls
        logger_instance: Logger for status messages

    Returns:
        Tuple[bool, str]: (success, status_message)
    """
    try:
        log_prefix = f"🔄 Cache Sync S{season_num:02d}"
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Starting cache synchronization for '{pack_title}'")
        else:
            print(f"{log_prefix}: Starting cache synchronization for '{pack_title}'")

        # Step 1: Trigger SeasonSearch to warm Sonarr's cache
        search_payload = {
            "name": "SeasonSearch",
            "seriesId": series_id,
            "seasonNumber": season_num
        }

        search_start = time.time()
        async with session.post(
            f"{sonarr_url}/api/v3/command",
            headers=headers,
            json=search_payload
        ) as search_resp:
            if search_resp.status not in [200, 201]:
                error_msg = f"Failed to trigger SeasonSearch: {search_resp.status}"
                if logger_instance:
                    logger_instance.error(f"{log_prefix}: {error_msg}")
                else:
                    print(f"{log_prefix}: ❌ {error_msg}")
                return False, error_msg

            command_data = await search_resp.json()
            command_id = command_data.get('id')

            if logger_instance:
                logger_instance.info(f"{log_prefix}: SeasonSearch triggered (Command ID: {command_id})")
            else:
                print(f"{log_prefix}: ✅ SeasonSearch triggered (Command ID: {command_id})")

        # Step 2: Wait for search to complete (poll command status)
        search_completed = False
        search_timeout = 60  # 60 seconds max for search to complete

        while time.time() - search_start < search_timeout and not search_completed:
            await asyncio.sleep(2)  # Check every 2 seconds

            try:
                async with session.get(f"{sonarr_url}/api/v3/command/{command_id}", headers=headers) as cmd_resp:
                    if cmd_resp.status == 200:
                        cmd_data = await cmd_resp.json()
                        status = cmd_data.get('status', '').lower()

                        if status in ['completed', 'failed']:
                            search_completed = True
                            if logger_instance:
                                logger_instance.info(f"{log_prefix}: SeasonSearch {status}")
                            else:
                                print(f"{log_prefix}: 📊 SeasonSearch {status}")
                            break
            except Exception as cmd_err:
                # Continue polling even if individual check fails
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Command status check failed: {cmd_err}")
                pass

        # Step 3: Poll Sonarr's release cache for the target GUID
        cache_start = time.time()
        total_timeout = max_retries * poll_interval

        # Get first episode ID for cache queries
        first_episode_id = None
        try:
            async with session.get(f"{sonarr_url}/api/v3/episode", headers=headers, params={"seriesId": series_id}) as ep_resp:
                if ep_resp.status == 200:
                    episodes = await ep_resp.json()
                    season_episodes = [ep for ep in episodes if ep.get('seasonNumber') == season_num]
                    if season_episodes:
                        first_episode_id = season_episodes[0].get('id')
                        if logger_instance:
                            logger_instance.info(f"{log_prefix}: Using episode ID {first_episode_id} for cache queries")
                        else:
                            print(f"{log_prefix}: 📋 Using episode ID {first_episode_id} for cache queries")
        except Exception:
            pass

        # Poll for GUID in cache
        for attempt in range(max_retries):
            try:
                # Query releases - try episode-specific first, then general
                query_params = {}
                if first_episode_id:
                    query_params["episodeId"] = first_episode_id

                async with session.get(
                    f"{sonarr_url}/api/v3/release",
                    headers=headers,
                    params=query_params
                ) as release_resp:
                    if release_resp.status == 200:
                        releases = await release_resp.json()

                        # Check if target GUID is in releases
                        for release in releases:
                            release_guid = release.get('guid', '')
                            if release_guid == target_guid:
                                elapsed = time.time() - cache_start
                                success_msg = f"Cache synchronized in {elapsed:.1f}s (attempt {attempt + 1}/{max_retries})"
                                if logger_instance:
                                    logger_instance.info(f"{log_prefix}: ✅ {success_msg}")
                                else:
                                    print(f"{log_prefix}: ✅ {success_msg}")
                                return True, success_msg

                        # Log progress every few attempts
                        if attempt % 3 == 0 and attempt > 0:
                            if logger_instance:
                                logger_instance.info(f"{log_prefix}: Still polling cache (attempt {attempt + 1}/{max_retries}, {len(releases)} releases found)")
                            else:
                                print(f"{log_prefix}: 🔍 Still polling cache (attempt {attempt + 1}/{max_retries}, {len(releases)} releases found)")

                            # Enhanced debugging: show actual GUIDs found vs target
                            if attempt == 3:  # First detailed debug
                                found_guids = [rel.get('guid', 'NO_GUID') for rel in releases[:5]]  # First 5 for brevity
                                if logger_instance:
                                    logger_instance.info(f"{log_prefix}: Target GUID: {target_guid}")
                                    logger_instance.info(f"{log_prefix}: Found GUIDs (first 5): {found_guids}")
                                else:
                                    print(f"{log_prefix}: 🎯 Target GUID: {target_guid}")
                                    print(f"{log_prefix}: 🔍 Found GUIDs (first 5): {found_guids}")

                                # Check for partial matches or similar releases
                                similar_releases = []
                                for rel in releases:
                                    rel_title = rel.get('title', '').lower()
                                    if 'flcl' in rel_title and 's01' in rel_title:
                                        similar_releases.append({
                                            'title': rel.get('title', 'NO_TITLE'),
                                            'guid': rel.get('guid', 'NO_GUID'),
                                            'indexer': rel.get('indexer', 'NO_INDEXER')
                                        })

                                if similar_releases:
                                    if logger_instance:
                                        logger_instance.info(f"{log_prefix}: Found {len(similar_releases)} similar FLCL S01 releases")
                                    else:
                                        print(f"{log_prefix}: 📺 Found {len(similar_releases)} similar FLCL S01 releases:")

                                    for i, sim_rel in enumerate(similar_releases[:3]):  # Show first 3
                                        if logger_instance:
                                            logger_instance.info(f"{log_prefix}: Similar #{i+1}: {sim_rel['title']} (GUID: {sim_rel['guid']})")
                                        else:
                                            print(f"{log_prefix}: #{i+1}: {sim_rel['title']}")
                                            print(f"{log_prefix}:      GUID: {sim_rel['guid']}")
                                else:
                                    if logger_instance:
                                        logger_instance.info(f"{log_prefix}: No similar FLCL S01 releases found in cache")
                                    else:
                                        print(f"{log_prefix}: ⚠️ No similar FLCL S01 releases found in cache")

                    elif release_resp.status == 404:
                        # No releases yet, continue polling
                        pass
                    else:
                        if logger_instance:
                            logger_instance.warning(f"{log_prefix}: Release query failed: {release_resp.status}")
                        else:
                            print(f"{log_prefix}: ⚠️ Release query failed: {release_resp.status}")

            except Exception as poll_err:
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Cache poll attempt {attempt + 1} failed: {poll_err}")
                else:
                    print(f"{log_prefix}: ⚠️ Cache poll attempt {attempt + 1} failed: {poll_err}")

            # Wait before next poll (except on last attempt)
            if attempt < max_retries - 1:
                await asyncio.sleep(poll_interval)

        # Cache sync failed
        elapsed = time.time() - cache_start
        failure_msg = f"Cache sync timeout after {elapsed:.1f}s ({max_retries} attempts)"
        if logger_instance:
            logger_instance.warning(f"{log_prefix}: ❌ {failure_msg}")
        else:
            print(f"{log_prefix}: ❌ {failure_msg}")

        return False, failure_msg

    except Exception as sync_err:
        error_msg = f"Cache synchronization error: {sync_err}"
        if logger_instance:
            logger_instance.error(f"{log_prefix}: ❌ {error_msg}")
        else:
            print(f"{log_prefix}: ❌ {error_msg}")
        return False, error_msg


async def download_season_pack_with_cache_sync(
    session: aiohttp.ClientSession,
    sonarr_url: str,
    headers: dict,
    series_id: int,
    season_num: int,
    season_pack: dict,
    logger_instance: logging.Logger = None
) -> Tuple[bool, str]:
    """
    Download season pack with comprehensive cache synchronization and fallback strategy.

    This function implements the complete solution for season pack downloads:
    1. Attempts direct download first (fast path)
    2. On 404, uses cache synchronization
    3. Retries direct download after cache sync
    4. Falls back to SeasonSearch if all else fails
    5. Last resort: direct NZB fetch (if available)

    Args:
        session: Active aiohttp session
        sonarr_url: Sonarr base URL
        headers: API headers with authentication
        series_id: Sonarr series ID
        season_num: Season number
        season_pack: Season pack data from preflight analyzer
        logger_instance: Logger for status messages

    Returns:
        Tuple[bool, str]: (success, status_message)
    """
    try:
        pack_title = season_pack.get('title', 'Unknown')
        pack_guid = season_pack.get('guid')
        pack_indexer_id = season_pack.get("indexerId")
        pack_indexer_name = season_pack.get("indexer")

        log_prefix = f"📦 S{season_num:02d} Pack"

        # Extract and validate GUID
        if isinstance(pack_guid, dict):
            if '@content' in pack_guid:
                pack_guid = pack_guid['@content']
            elif 'guid' in pack_guid:
                pack_guid = pack_guid['guid']
            else:
                pack_guid = str(pack_guid)
        elif isinstance(pack_guid, str) and pack_guid.startswith("{'@content'"):
            try:
                import ast
                pack_guid_obj = ast.literal_eval(pack_guid)
                pack_guid = pack_guid_obj.get('@content', pack_guid)
            except:
                pass

        if not pack_guid:
            error_msg = "Missing GUID for season pack"
            if logger_instance:
                logger_instance.error(f"{log_prefix}: {error_msg}")
            else:
                print(f"{log_prefix}: ❌ {error_msg}")
            return False, error_msg

        # Map indexer name to ID if needed
        if pack_indexer_id is None and pack_indexer_name:
            try:
                async with session.get(f"{sonarr_url}/api/v3/indexer", headers=headers) as idx_resp:
                    if idx_resp.status == 200:
                        indexers = await idx_resp.json()
                        for indexer in indexers:
                            if indexer.get('name', '').lower() == pack_indexer_name.lower():
                                pack_indexer_id = indexer.get('id')
                                if logger_instance:
                                    logger_instance.info(f"{log_prefix}: Mapped indexer '{pack_indexer_name}' → ID: {pack_indexer_id}")
                                else:
                                    print(f"{log_prefix}: ✅ Mapped indexer '{pack_indexer_name}' → ID: {pack_indexer_id}")
                                break
                        if pack_indexer_id is None and indexers:
                            first_indexer = indexers[0]
                            pack_indexer_id = first_indexer.get('id')
                            if logger_instance:
                                logger_instance.info(f"{log_prefix}: Using fallback indexer: {first_indexer.get('name')} (ID: {pack_indexer_id})")
                            else:
                                print(f"{log_prefix}: 🔧 Using fallback indexer: {first_indexer.get('name')} (ID: {pack_indexer_id})")
            except Exception as idx_err:
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Could not get indexer mapping: {idx_err}")
                else:
                    print(f"{log_prefix}: ⚠️ Could not get indexer mapping: {idx_err}")

        # Prepare download payload with proper size handling
        pack_size_raw = season_pack.get('size', 0)
        pack_size = pack_size_raw

        # Debug original size value
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Raw size from pack: {pack_size_raw} (type: {type(pack_size_raw)})")
        else:
            print(f"{log_prefix}: 🐛 DEBUG: Raw size from pack: {pack_size_raw} (type: {type(pack_size_raw)})")

        # Ensure size is a valid integer
        if pack_size is None:
            pack_size = 0
        elif isinstance(pack_size, str):
            try:
                pack_size = int(float(pack_size))  # Handle string numbers, including floats
            except (ValueError, TypeError):
                pack_size = 0
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Could not convert string size '{pack_size_raw}' to int, using 0")
                else:
                    print(f"{log_prefix}: ⚠️ Could not convert string size '{pack_size_raw}' to int, using 0")
        elif isinstance(pack_size, float):
            pack_size = int(pack_size)
        elif not isinstance(pack_size, int):
            try:
                pack_size = int(pack_size)
            except (ValueError, TypeError):
                pack_size = 0
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Could not convert size '{pack_size_raw}' (type: {type(pack_size_raw)}) to int, using 0")
                else:
                    print(f"{log_prefix}: ⚠️ Could not convert size '{pack_size_raw}' (type: {type(pack_size_raw)}) to int, using 0")

        # Ensure non-negative size
        if pack_size < 0:
            pack_size = 0

        # Debug final size value
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Final size for API: {pack_size} (type: {type(pack_size)})")
        else:
            print(f"{log_prefix}: 🐛 DEBUG: Final size for API: {pack_size} (type: {type(pack_size)})")

        grab_payload = {
            "guid": pack_guid,
            "indexerId": pack_indexer_id,
            "title": pack_title,
            "size": pack_size,
            "seriesId": series_id
        }

        # Debug payload before sending
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Download payload: {grab_payload}")
        else:
            print(f"{log_prefix}: 🐛 DEBUG: Download payload: {grab_payload}")

        # Step 1: Attempt direct download (fast path)
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Attempting direct download of '{pack_title}'")
        else:
            print(f"{log_prefix}: 🚀 Attempting direct download of '{pack_title}'")

        async with session.post(
            f"{sonarr_url}/api/v3/release",
            headers=headers,
            json=grab_payload
        ) as direct_resp:
            if direct_resp.status in [200, 201]:
                success_msg = f"Direct download successful: {pack_title}"
                if logger_instance:
                    logger_instance.info(f"{log_prefix}: ✅ {success_msg}")
                else:
                    print(f"{log_prefix}: ✅ {success_msg}")
                return True, success_msg

            elif direct_resp.status == 404:
                # Cache miss detected - proceed with cache synchronization
                if logger_instance:
                    logger_instance.info(f"{log_prefix}: Cache miss detected, starting synchronization...")
                else:
                    print(f"{log_prefix}: 🔄 Cache miss detected, starting synchronization...")

                # Step 2: Synchronize cache
                cache_success, cache_msg = await sync_sonarr_cache_for_season_pack(
                    session=session,
                    sonarr_url=sonarr_url,
                    headers=headers,
                    series_id=series_id,
                    season_num=season_num,
                    target_guid=pack_guid,
                    pack_title=pack_title,
                    logger_instance=logger_instance
                )

                if cache_success:
                    # Step 3: Retry direct download after cache sync
                    if logger_instance:
                        logger_instance.info(f"{log_prefix}: Retrying download after cache sync...")
                    else:
                        print(f"{log_prefix}: 🔄 Retrying download after cache sync...")

                    async with session.post(
                        f"{sonarr_url}/api/v3/release",
                        headers=headers,
                        json=grab_payload
                    ) as retry_resp:
                        if retry_resp.status in [200, 201]:
                            success_msg = f"Download successful after cache sync: {pack_title}"
                            if logger_instance:
                                logger_instance.info(f"{log_prefix}: ✅ {success_msg}")
                            else:
                                print(f"{log_prefix}: ✅ {success_msg}")
                            return True, success_msg
                        else:
                            retry_error = await retry_resp.text()
                            if logger_instance:
                                logger_instance.warning(f"{log_prefix}: Retry failed: {retry_resp.status} - {retry_error}")
                            else:
                                print(f"{log_prefix}: ⚠️ Retry failed: {retry_resp.status} - {retry_error}")
                # Cache sync failed - continue to alternative strategies instead of immediate fallback
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Cache sync failed, continuing to alternative strategies...")
                else:
                    print(f"{log_prefix}: ⚠️ Cache sync failed, continuing to alternative strategies...")

            else:
                # Other error (not cache miss) - continue to alternative strategies
                direct_error = await direct_resp.text()
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Direct download failed: {direct_resp.status} - {direct_error}")
                else:
                    print(f"{log_prefix}: ⚠️ Direct download failed: {direct_resp.status} - {direct_error}")

        # Step 4: Alternative - Try Sonarr's Release Push API (bypasses cache entirely)
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Trying Sonarr Release Push API to bypass cache...")
        else:
            print(f"{log_prefix}: 🔄 Trying Sonarr Release Push API to bypass cache...")

        # Temporarily enable series monitoring for Release Push API (required for season packs)
        original_monitored = None
        monitoring_changed = False
        try:
            async with session.get(f"{sonarr_url}/api/v3/series/{series_id}", headers=headers) as series_resp:
                if series_resp.status == 200:
                    series_info = await series_resp.json()
                    original_monitored = series_info.get("monitored", False)

                    if not original_monitored:
                        if logger_instance:
                            logger_instance.info(f"{log_prefix}: 📡 Temporarily enabling series monitoring for Release Push")
                        else:
                            print(f"{log_prefix}: 📡 Temporarily enabling series monitoring for Release Push")

                        series_info["monitored"] = True
                        sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
                        ok = await sonarr.update_series(session, series_info)
                        if ok:
                            monitoring_changed = True
                            if logger_instance:
                                logger_instance.info(f"{log_prefix}: ✅ Temporary monitoring enabled")
                            else:
                                print(f"{log_prefix}: ✅ Temporary monitoring enabled")

                        # Also enable episode-level monitoring for the season (required for season packs)
                        try:
                            async with session.get(f"{sonarr_url}/api/v3/episode", headers=headers, params={"seriesId": series_id}) as ep_resp:
                                if ep_resp.status == 200:
                                    episodes = await ep_resp.json()
                                    season_episodes = [ep for ep in episodes if ep.get("seasonNumber") == season_num]

                                    if season_episodes:
                                        episode_ids = [ep["id"] for ep in season_episodes]
                                        sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
                                        ok = await sonarr.set_episodes_monitor_state(session, episode_ids, True)
                                        if ok:
                                            if logger_instance:
                                                logger_instance.info(f"{log_prefix}: ✅ Enabled monitoring for {len(episode_ids)} episodes in season {season_num}")
                                            else:
                                                print(f"{log_prefix}: ✅ Enabled monitoring for {len(episode_ids)} episodes in season {season_num}")
                                        else:
                                            if logger_instance:
                                                logger_instance.warning(f"{log_prefix}: ⚠️ Failed to enable episode monitoring for season {season_num}")
                                            else:
                                                print(f"{log_prefix}: ⚠️ Failed to enable episode monitoring for season {season_num}")
                        except Exception as ep_monitor_err:
                            if logger_instance:
                                logger_instance.warning(f"{log_prefix}: Could not enable episode monitoring: {ep_monitor_err}")
                            else:
                                print(f"{log_prefix}: ⚠️ Could not enable episode monitoring: {ep_monitor_err}")
        except Exception as monitor_err:
            if logger_instance:
                logger_instance.warning(f"{log_prefix}: Could not modify monitoring: {monitor_err}")
            else:
                print(f"{log_prefix}: ⚠️ Could not modify monitoring: {monitor_err}")

        try:
            # Prepare release push payload with all required fields and cutoff override attempts
            push_payload = {
                "guid": pack_guid,
                "title": pack_title,
                "downloadUrl": pack_guid,  # Use GUID as download URL for NZB links
                "protocol": "usenet",
                "indexer": pack_indexer_name or "Unknown",
                "indexerId": pack_indexer_id,
                "size": pack_size,
                "publishDate": "2024-01-01T00:00:00Z",  # Default publish date
                "seriesId": series_id,
                # Attempt to override quality cutoff logic
                "quality": {
                    "quality": {"id": 7, "name": "Bluray-1080p", "source": "bluray", "resolution": 1080},
                    "revision": {"version": 1, "real": 0, "isRepack": False}
                },
                "qualityWeight": 2000,  # Higher weight to prioritize over existing files
                "approved": True,       # Force approval
                "rejected": False,      # Explicitly not rejected
                "downloadAllowed": True,
                "fullSeason": True,
                "seasonNumber": season_num
            }

            if logger_instance:
                logger_instance.info(f"{log_prefix}: Release push payload: {push_payload}")
            else:
                print(f"{log_prefix}: 🐛 DEBUG: Release push payload: {push_payload}")

            async with session.post(
                f"{sonarr_url}/api/v3/release/push",
                headers=headers,
                json=push_payload
            ) as push_resp:
                if push_resp.status in [200, 201]:
                    # Parse the response to understand why season packs might be rejected
                    try:
                        push_response = await push_resp.json()
                        if logger_instance:
                            logger_instance.info(f"{log_prefix}: 🐛 DEBUG: Release push response: {push_response}")
                        else:
                            print(f"{log_prefix}: 🐛 DEBUG: Release push response: {push_response}")

                        # Check if the release was actually rejected despite 200 status
                        if isinstance(push_response, list) and len(push_response) > 0:
                            release_data = push_response[0]
                            if release_data.get("rejected", False):
                                rejections = release_data.get("rejections", [])
                                if logger_instance:
                                    logger_instance.warning(f"{log_prefix}: ⚠️ Release was rejected: {rejections}")
                                else:
                                    print(f"{log_prefix}: ⚠️ Release was rejected: {rejections}")

                                # Still return success for now, but with rejection info
                                push_msg = f"Release push reported success but was rejected: {pack_title} - {rejections}"
                            else:
                                # Release was approved, now trigger actual download
                                if logger_instance:
                                    logger_instance.info(f"{log_prefix}: 📥 Release approved, triggering download...")
                                else:
                                    print(f"{log_prefix}: 📥 Release approved, triggering download...")

                                # Since Release Push API only evaluates but doesn't download,
                                # let's try to manually add to download client
                                if logger_instance:
                                    logger_instance.info(f"{log_prefix}: 💾 Attempting manual download client addition")
                                else:
                                    print(f"{log_prefix}: 💾 Attempting manual download client addition")

                                # Create a download client entry manually
                                manual_download_payload = {
                                    "downloadClientId": 1,  # Assuming first download client
                                    "title": pack_title,
                                    "size": pack_size,
                                    "sizeleft": pack_size,
                                    "status": "queued",
                                    "trackedDownloadStatus": "ok",
                                    "trackedDownloadState": "downloading",
                                    "downloadId": f"manual-{pack_guid.split('/')[-1]}",
                                    "protocol": "usenet",
                                    "downloadClient": "SABnzbd",
                                    "indexer": pack_indexer_name,
                                    "outputPath": "/downloads",
                                    "episode": {
                                        "seriesId": series_id,
                                        "seasonNumber": season_num
                                    }
                                }

                                # Instead of complex manual addition, let's trigger SeasonSearch again
                                # but this time with the approved release in mind
                                search_command_payload = {
                                    "name": "SeasonSearch",
                                    "seriesId": series_id,
                                    "seasonNumber": season_num
                                }

                                sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
                                cmd = await sonarr.issue_command(session, search_command_payload)
                                if cmd and isinstance(cmd, dict) and cmd.get('id'):
                                    search_id = cmd.get('id', 'unknown')
                                    if logger_instance:
                                        logger_instance.info(f"{log_prefix}: ✅ Season search re-triggered after approval (ID: {search_id})")
                                    else:
                                        print(f"{log_prefix}: ✅ Season search re-triggered after approval (ID: {search_id})")
                                    push_msg = f"Release approved and season search re-triggered: {pack_title}"
                                else:
                                    if logger_instance:
                                        logger_instance.warning(f"{log_prefix}: ⚠️ Season search re-trigger failed")
                                    else:
                                        print(f"{log_prefix}: ⚠️ Season search re-trigger failed")
                                    push_msg = f"Release approved but season search failed: {pack_title}"
                        else:
                            push_msg = f"Release push successful: {pack_title}"
                    except Exception as parse_err:
                        # Fallback if response parsing fails
                        push_msg = f"Release push successful: {pack_title}"
                        if logger_instance:
                            logger_instance.warning(f"{log_prefix}: Could not parse push response: {parse_err}")
                        else:
                            print(f"{log_prefix}: ⚠️ Could not parse push response: {parse_err}")

                    if logger_instance:
                        logger_instance.info(f"{log_prefix}: ✅ {push_msg}")
                    else:
                        print(f"{log_prefix}: ✅ {push_msg}")

                    # Restore original monitoring state before returning
                    if monitoring_changed and original_monitored is not None:
                        try:
                            async with session.get(f"{sonarr_url}/api/v3/series/{series_id}", headers=headers) as series_resp:
                                if series_resp.status == 200:
                                    series_info = await series_resp.json()
                                    series_info["monitored"] = original_monitored
                                    sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
                                    ok = await sonarr.update_series(session, series_info)
                                    if ok:
                                        if logger_instance:
                                            logger_instance.info(f"{log_prefix}: ✅ Restored original monitoring state")
                                        else:
                                            print(f"{log_prefix}: ✅ Restored original monitoring state")
                        except Exception as restore_err:
                            if logger_instance:
                                logger_instance.warning(f"{log_prefix}: ⚠️ Failed to restore monitoring: {restore_err}")
                            else:
                                print(f"{log_prefix}: ⚠️ Failed to restore monitoring: {restore_err}")

                    return True, push_msg
                else:
                    push_error = await push_resp.text()
                    if logger_instance:
                        logger_instance.warning(f"{log_prefix}: Release push failed: {push_resp.status} - {push_error}")
                    else:
                        print(f"{log_prefix}: ⚠️ Release push failed: {push_resp.status} - {push_error}")

        except Exception as push_err:
            if logger_instance:
                logger_instance.warning(f"{log_prefix}: Release push error: {push_err}")
            else:
                print(f"{log_prefix}: ⚠️ Release push error: {push_err}")

        # Restore monitoring state in all cases before continuing to next strategies
        if monitoring_changed and original_monitored is not None:
            try:
                async with session.get(f"{sonarr_url}/api/v3/series/{series_id}", headers=headers) as series_resp:
                    if series_resp.status == 200:
                        series_info = await series_resp.json()
                        series_info["monitored"] = original_monitored
                        sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
                        ok = await sonarr.update_series(session, series_info)
                        if ok:
                            if logger_instance:
                                logger_instance.info(f"{log_prefix}: ✅ Restored original monitoring state")
                            else:
                                print(f"{log_prefix}: ✅ Restored original monitoring state")
            except Exception as restore_err:
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: ⚠️ Failed to restore monitoring: {restore_err}")
                else:
                    print(f"{log_prefix}: ⚠️ Failed to restore monitoring: {restore_err}")

        # Step 5: Last resort - direct NZB fetch using preflight analyzer
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Attempting last-resort direct NZB fetch...")
        else:
            print(f"{log_prefix}: 🔄 Attempting last-resort direct NZB fetch via preflight analyzer...")

        try:
            # Import preflight analyzer components for NZB fetching
            import sys
            from pathlib import Path

            # Add preflight_analyzer to path
            preflight_path = Path(__file__).parent / "preflight_analyzer"
            if str(preflight_path) not in sys.path:
                sys.path.insert(0, str(preflight_path))

            # Try to import and use the NZB fetch functionality
            try:
                from indexer_client import IndexerClient
                from types import SeasonPackCandidate

                # Extract indexer information
                pack_indexer_name = season_pack.get('indexer', '')
                pack_guid_raw = season_pack.get('guid', '')

                # Handle complex GUID structure
                if isinstance(pack_guid_raw, dict) and '@content' in pack_guid_raw:
                    nzb_url = pack_guid_raw['@content']
                elif isinstance(pack_guid_raw, str):
                    nzb_url = pack_guid_raw
                else:
                    nzb_url = str(pack_guid_raw)

                if logger_instance:
                    logger_instance.info(f"{log_prefix}: Using NZB URL: {nzb_url}")
                else:
                    print(f"{log_prefix}: 🌐 Using NZB URL: {nzb_url}")

                # Create indexer client and fetch NZB
                indexer_client = IndexerClient()
                nzb_content = await indexer_client.fetch_nzb(nzb_url)

                if nzb_content:
                    # Get SABnzbd settings for direct submission
                    from utils.common_helpers import get_setting
                    sabnzbd_url = get_setting('SABnzbd', 'url', settings_dict={}, default='http://localhost:8080')
                    sabnzbd_api_key = get_setting('SABnzbd', 'api_key', settings_dict={}, default='')

                    # Submit NZB directly to SABnzbd
                    import tempfile
                    import os

                    # Create temporary NZB file
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.nzb', delete=False) as temp_nzb:
                        temp_nzb.write(nzb_content)
                        temp_nzb_path = temp_nzb.name

                    try:
                        # Submit to SABnzbd using files parameter
                        import aiofiles

                        sabnzbd_add_url = f"{sabnzbd_url}/api"
                        form_data = aiohttp.FormData()
                        form_data.add_field('mode', 'addfile')
                        form_data.add_field('apikey', sabnzbd_api_key)
                        form_data.add_field('cat', 'tv')  # TV category
                        form_data.add_field('name', pack_title)

                        # Add the NZB file
                        with open(temp_nzb_path, 'rb') as nzb_file:
                            form_data.add_field('nzbfile', nzb_file, filename=f"{pack_title}.nzb", content_type='application/x-nzb')

                            async with session.post(sabnzbd_add_url, data=form_data) as sab_resp:
                                if sab_resp.status == 200:
                                    sab_result = await sab_resp.text()
                                    if 'ok' in sab_result.lower():
                                        success_msg = f"Direct NZB fetch successful: {pack_title} submitted to SABnzbd"
                                        if logger_instance:
                                            logger_instance.info(f"{log_prefix}: ✅ {success_msg}")
                                        else:
                                            print(f"{log_prefix}: ✅ {success_msg}")

                                        # Clean up temp file
                                        os.unlink(temp_nzb_path)
                                        return True, success_msg
                                    else:
                                        if logger_instance:
                                            logger_instance.error(f"{log_prefix}: SABnzbd rejected NZB: {sab_result}")
                                        else:
                                            print(f"{log_prefix}: ❌ SABnzbd rejected NZB: {sab_result}")
                                else:
                                    sab_error = await sab_resp.text()
                                    if logger_instance:
                                        logger_instance.error(f"{log_prefix}: SABnzbd submission failed: {sab_resp.status} - {sab_error}")
                                    else:
                                        print(f"{log_prefix}: ❌ SABnzbd submission failed: {sab_resp.status} - {sab_error}")

                    finally:
                        # Clean up temp file if it still exists
                        if os.path.exists(temp_nzb_path):
                            os.unlink(temp_nzb_path)

                else:
                    if logger_instance:
                        logger_instance.error(f"{log_prefix}: Failed to fetch NZB content from indexer")
                    else:
                        print(f"{log_prefix}: ❌ Failed to fetch NZB content from indexer")

            except ImportError as import_err:
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Could not import preflight analyzer components: {import_err}")
                else:
                    print(f"{log_prefix}: ⚠️ Could not import preflight analyzer components: {import_err}")

        except Exception as nzb_err:
            if logger_instance:
                logger_instance.error(f"{log_prefix}: Direct NZB fetch error: {nzb_err}")
            else:
                print(f"{log_prefix}: ❌ Direct NZB fetch error: {nzb_err}")

        # Step 6: Traditional SeasonSearch fallback (last Sonarr-based attempt)
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Trying traditional SeasonSearch as final Sonarr fallback...")
        else:
            print(f"{log_prefix}: 🔄 Trying traditional SeasonSearch as final Sonarr fallback...")

        try:
            traditional_fallback_payload = {
                "name": "SeasonSearch",
                "seriesId": series_id,
                "seasonNumber": season_num
            }

            async with session.post(
                f"{sonarr_url}/api/v3/command",
                headers=headers,
                json=traditional_fallback_payload
            ) as traditional_resp:
                if traditional_resp.status in [200, 201]:
                    traditional_msg = f"Traditional SeasonSearch fallback initiated for Season {season_num}"
                    if logger_instance:
                        logger_instance.info(f"{log_prefix}: ✅ {traditional_msg}")
                    else:
                        print(f"{log_prefix}: ✅ {traditional_msg}")
                    return True, traditional_msg
                else:
                    traditional_error = await traditional_resp.text()
                    if logger_instance:
                        logger_instance.warning(f"{log_prefix}: Traditional SeasonSearch failed: {traditional_resp.status} - {traditional_error}")
                    else:
                        print(f"{log_prefix}: ⚠️ Traditional SeasonSearch failed: {traditional_resp.status} - {traditional_error}")

        except Exception as traditional_err:
            if logger_instance:
                logger_instance.warning(f"{log_prefix}: Traditional SeasonSearch error: {traditional_err}")
            else:
                print(f"{log_prefix}: ⚠️ Traditional SeasonSearch error: {traditional_err}")

        # All attempts failed
        final_error = f"All download attempts failed for season pack: {pack_title}"
        if logger_instance:
            logger_instance.error(f"{log_prefix}: ❌ {final_error}")
        else:
            print(f"{log_prefix}: ❌ {final_error}")

        return False, final_error

    except Exception as download_err:
        error_msg = f"Season pack download error: {download_err}"
        if logger_instance:
            logger_instance.error(f"{log_prefix}: ❌ {error_msg}")
        else:
            print(f"{log_prefix}: ❌ {error_msg}")
        return False, error_msg


async def sync_radarr_cache_for_movie(
    session: aiohttp.ClientSession,
    radarr_url: str,
    headers: dict,
    movie_id: int,
    target_guid: str,
    movie_title: str,
    logger_instance: logging.Logger = None,
    max_retries: int = 12,
    poll_interval: float = 2.5
) -> Tuple[bool, str]:
    """
    Synchronize Radarr's release cache for a specific movie by triggering search and polling for target GUID.
    
    Args:
        session: Active aiohttp session
        radarr_url: Radarr base URL
        headers: API headers with authentication
        movie_id: Radarr movie ID
        target_guid: Target release GUID to look for
        movie_title: Movie title for logging
        logger_instance: Logger for status messages
        max_retries: Maximum polling attempts (default: 12)
        poll_interval: Seconds between polls (default: 2.5)
        
    Returns:
        Tuple[bool, str]: (success, status_message)
    """
    try:
        log_prefix = f"🎬 Movie Cache"
        
        # CRITICAL FIX #2: Disable monitoring before cache sync to prevent auto-grabs
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Disabling monitoring to prevent auto-grabs during cache sync...")
        else:
            print(f"{log_prefix}: 🚫 Disabling monitoring to prevent auto-grabs during cache sync...")
        
        # Get current movie data and disable monitoring
        async with session.get(f"{radarr_url}/api/v3/movie/{movie_id}", headers=headers) as get_resp:
            if get_resp.status == 200:
                movie_data = await get_resp.json()
                original_monitored = movie_data.get("monitored", True)
                movie_data["monitored"] = False  # Disable monitoring during cache sync
                
                async with session.put(f"{radarr_url}/api/v3/movie/{movie_id}", headers=headers, json=movie_data) as put_resp:
                    if put_resp.status in [200, 202]:
                        if logger_instance:
                            logger_instance.info(f"{log_prefix}: Successfully disabled monitoring (was: {original_monitored})")
                        else:
                            print(f"{log_prefix}: ✅ Successfully disabled monitoring (was: {original_monitored})")
                    else:
                        if logger_instance:
                            logger_instance.warning(f"{log_prefix}: Failed to disable monitoring: {put_resp.status}")
                        else:
                            print(f"{log_prefix}: ⚠️ Failed to disable monitoring: {put_resp.status}")
            else:
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Failed to get movie data for monitoring disable: {get_resp.status}")
                else:
                    print(f"{log_prefix}: ⚠️ Failed to get movie data for monitoring disable: {get_resp.status}")
        
        # Step 1: Trigger movie search to populate cache (read-only mode)
        search_start = time.time()
        search_timeout = 60  # Max time to wait for search command completion
        search_completed = False
        command_id = None
        
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Triggering movie search for '{movie_title}' (read-only mode)")
        else:
            print(f"{log_prefix}: 🔍 Triggering movie search for '{movie_title}' (read-only mode)")
            
        async with session.post(
            f"{radarr_url}/api/v3/command",
            headers=headers,
            json={"name": "MoviesSearch", "movieIds": [movie_id]}
        ) as search_resp:
            if search_resp.status in [200, 201, 202]:
                search_data = await search_resp.json()
                command_id = search_data.get('id')
                if logger_instance:
                    logger_instance.info(f"{log_prefix}: Movie search started (Command ID: {command_id})")
                else:
                    print(f"{log_prefix}: ✅ Movie search started (Command ID: {command_id})")
            else:
                search_error = await search_resp.text()
                error_msg = f"Failed to trigger movie search: {search_resp.status} - {search_error}"
                if logger_instance:
                    logger_instance.error(f"{log_prefix}: {error_msg}")
                else:
                    print(f"{log_prefix}: ❌ {error_msg}")
                return False, error_msg
        
        # Step 2: Wait for search command to complete (optional monitoring)
        if command_id:
            while time.time() - search_start < search_timeout and not search_completed:
                await asyncio.sleep(2)  # Check every 2 seconds
                
                try:
                    async with session.get(f"{radarr_url}/api/v3/command/{command_id}", headers=headers) as cmd_resp:
                        if cmd_resp.status == 200:
                            cmd_data = await cmd_resp.json()
                            status = cmd_data.get('status', '').lower()
                            
                            if status in ['completed', 'failed']:
                                search_completed = True
                                if logger_instance:
                                    logger_instance.info(f"{log_prefix}: Movie search {status}")
                                else:
                                    print(f"{log_prefix}: 📊 Movie search {status}")
                                break
                except Exception as cmd_err:
                    # Continue polling even if individual check fails
                    if logger_instance:
                        logger_instance.warning(f"{log_prefix}: Command status check failed: {cmd_err}")
                    pass
        
        # Step 3: Poll Radarr's release cache for the target GUID
        cache_start = time.time()
        total_timeout = max_retries * poll_interval
        
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Polling cache for target GUID (max {total_timeout:.1f}s)")
        else:
            print(f"{log_prefix}: 🔍 Polling cache for target GUID (max {total_timeout:.1f}s)")
            print(f"{log_prefix}: 🎯 Looking for GUID: {target_guid}")
            print(f"{log_prefix}: ⏱️ Will timeout after {total_timeout:.1f} seconds...")
        
        # Poll for GUID in cache
        for attempt in range(max_retries):
            try:
                # Query releases for this movie
                async with session.get(
                    f"{radarr_url}/api/v3/release",
                    headers=headers,
                    params={"movieId": movie_id}
                ) as release_resp:
                    if release_resp.status == 200:
                        releases = await release_resp.json()
                        
                        # Check if target GUID is in releases
                        for release in releases:
                            release_guid = release.get('guid', '')
                            if release_guid == target_guid:
                                elapsed = time.time() - cache_start
                                success_msg = f"Cache synchronized in {elapsed:.1f}s (attempt {attempt + 1}/{max_retries})"
                                if logger_instance:
                                    logger_instance.info(f"{log_prefix}: ✅ {success_msg}")
                                else:
                                    print(f"{log_prefix}: ✅ {success_msg}")
                                return True, success_msg
                        
                        # Log progress every few attempts
                        if attempt % 2 == 0:  # More frequent updates
                            elapsed = time.time() - cache_start
                            if logger_instance:
                                logger_instance.info(f"{log_prefix}: Still polling cache (attempt {attempt + 1}/{max_retries}, {len(releases)} releases found, {elapsed:.1f}s elapsed)")
                            else:
                                print(f"{log_prefix}: 🔍 Still polling cache (attempt {attempt + 1}/{max_retries}, {len(releases)} releases found, {elapsed:.1f}s elapsed)")
                            
                            # Enhanced debugging: show actual GUIDs found vs target
                            if attempt == 2:  # Earlier detailed debug
                                found_guids = [rel.get('guid', 'NO_GUID') for rel in releases[:3]]  # First 3 for brevity
                                if logger_instance:
                                    logger_instance.info(f"{log_prefix}: Target GUID: {target_guid}")
                                    logger_instance.info(f"{log_prefix}: Found GUIDs (first 3): {found_guids}")
                                else:
                                    print(f"{log_prefix}: 🎯 Target GUID: {target_guid}")
                                    print(f"{log_prefix}: 🔍 Found GUIDs (first 3): {found_guids}")
                    
                    elif release_resp.status == 404:
                        # No releases yet, continue polling
                        pass
                    else:
                        if logger_instance:
                            logger_instance.warning(f"{log_prefix}: Release query failed: {release_resp.status}")
                        else:
                            print(f"{log_prefix}: ⚠️ Release query failed: {release_resp.status}")
            
            except Exception as poll_err:
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Cache poll attempt {attempt + 1} failed: {poll_err}")
                else:
                    print(f"{log_prefix}: ⚠️ Cache poll attempt {attempt + 1} failed: {poll_err}")
            
            # Wait before next poll (except on last attempt)
            if attempt < max_retries - 1:
                await asyncio.sleep(poll_interval)
        
        # Cache sync failed
        elapsed = time.time() - cache_start
        failure_msg = f"Cache sync timeout after {elapsed:.1f}s ({max_retries} attempts)"
        if logger_instance:
            logger_instance.warning(f"{log_prefix}: ❌ {failure_msg}")
        else:
            print(f"{log_prefix}: ❌ {failure_msg}")
        
        return False, failure_msg
    
    except Exception as sync_err:
        error_msg = f"Cache synchronization error: {sync_err}"
        if logger_instance:
            logger_instance.error(f"{log_prefix}: ❌ {error_msg}")
        else:
            print(f"{log_prefix}: ❌ {error_msg}")
        return False, error_msg


async def download_movie_with_cache_sync(
    session: aiohttp.ClientSession,
    radarr_url: str,
    headers: dict,
    movie_id: int,
    movie_data: dict,
    logger_instance: logging.Logger = None
) -> Tuple[bool, str]:
    """
    Download movie with comprehensive cache synchronization and fallback strategy.
    
    This function implements the complete solution for movie downloads:
    1. Attempts direct download first (fast path)
    2. On 404, uses cache synchronization
    3. Retries direct download after cache sync
    4. Falls back to MovieSearch if all else fails
    
    Args:
        session: Active aiohttp session
        radarr_url: Radarr base URL
        headers: API headers with authentication
        movie_id: Radarr movie ID
        movie_data: Movie release data from preflight analyzer
        logger_instance: Logger for status messages
        
    Returns:
        Tuple[bool, str]: (success, status_message)
    """
    try:
        movie_title = movie_data.get('title', 'Unknown')
        movie_guid = movie_data.get('guid')
        movie_indexer_id = movie_data.get("indexerId")
        movie_indexer_name = movie_data.get("indexer")
        
        log_prefix = f"🎬 Movie"
        
        # Extract and validate GUID
        if isinstance(movie_guid, dict):
            if '@content' in movie_guid:
                movie_guid = movie_guid['@content']
            elif 'guid' in movie_guid:
                movie_guid = movie_guid['guid']
            else:
                movie_guid = str(movie_guid)
        elif isinstance(movie_guid, str) and movie_guid.startswith("{'@content'"):
            try:
                import ast
                movie_guid_obj = ast.literal_eval(movie_guid)
                movie_guid = movie_guid_obj.get('@content', movie_guid)
            except:
                pass
        
        if not movie_guid:
            error_msg = "Missing GUID for movie"
            if logger_instance:
                logger_instance.error(f"{log_prefix}: {error_msg}")
            else:
                print(f"{log_prefix}: ❌ {error_msg}")
            return False, error_msg
        
        # Map indexer name to ID if needed
        if movie_indexer_id is None and movie_indexer_name:
            try:
                async with session.get(f"{radarr_url}/api/v3/indexer", headers=headers) as idx_resp:
                    if idx_resp.status == 200:
                        indexers = await idx_resp.json()
                        for indexer in indexers:
                            if indexer.get('name', '').lower() == movie_indexer_name.lower():
                                movie_indexer_id = indexer.get('id')
                                if logger_instance:
                                    logger_instance.info(f"{log_prefix}: Mapped indexer '{movie_indexer_name}' → ID: {movie_indexer_id}")
                                else:
                                    print(f"{log_prefix}: ✅ Mapped indexer '{movie_indexer_name}' → ID: {movie_indexer_id}")
                                break
                        if movie_indexer_id is None and indexers:
                            first_indexer = indexers[0]
                            movie_indexer_id = first_indexer.get('id')
                            if logger_instance:
                                logger_instance.info(f"{log_prefix}: Using fallback indexer: {first_indexer.get('name')} (ID: {movie_indexer_id})")
                            else:
                                print(f"{log_prefix}: 🔧 Using fallback indexer: {first_indexer.get('name')} (ID: {movie_indexer_id})")
            except Exception as idx_err:
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Could not get indexer mapping: {idx_err}")
                else:
                    print(f"{log_prefix}: ⚠️ Could not get indexer mapping: {idx_err}")
        
        # Prepare download payload
        grab_payload = {"guid": movie_guid, "indexerId": movie_indexer_id}
        
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Download payload: {grab_payload}")
        else:
            print(f"{log_prefix}: 🐛 DEBUG: Download payload: {grab_payload}")
        
        # CRITICAL FIX: Check cache first instead of attempting direct download
        # The old logic attempted POST /api/v3/release which IMMEDIATELY downloads
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Checking if '{movie_title}' exists in cache...")
        else:
            print(f"{log_prefix}: � Checking if '{movie_title}' exists in cache...")
        
        # Step 1: Check if release exists in cache (GET, not POST)
        async with session.get(
            f"{radarr_url}/api/v3/release",
            headers=headers,
            params={"movieId": movie_id}
        ) as cache_check_resp:
            release_found_in_cache = False
            if cache_check_resp.status == 200:
                releases = await cache_check_resp.json()
                # Check if our target GUID is in the cache
                for release in releases:
                    if release.get('guid') == movie_guid:
                        release_found_in_cache = True
                        if logger_instance:
                            logger_instance.info(f"{log_prefix}: ✅ Found release in cache, proceeding with download")
                        else:
                            print(f"{log_prefix}: ✅ Found release in cache, proceeding with download")
                        break
            
            if release_found_in_cache:
                # Release is in cache, safe to download
                async with session.post(
                    f"{radarr_url}/api/v3/release",
                    headers=headers,
                    json=grab_payload
                ) as direct_resp:
                    if direct_resp.status in [200, 201]:
                        success_msg = f"Direct download successful: {movie_title}"
                        if logger_instance:
                            logger_instance.info(f"{log_prefix}: ✅ {success_msg}")
                        else:
                            print(f"{log_prefix}: ✅ {success_msg}")
                        return True, success_msg
                    else:
                        direct_error = await direct_resp.text()
                        if logger_instance:
                            logger_instance.warning(f"{log_prefix}: Download failed despite cache hit: {direct_resp.status} - {direct_error}")
                        else:
                            print(f"{log_prefix}: ⚠️ Download failed despite cache hit: {direct_resp.status} - {direct_error}")
            else:
                # Cache miss detected - proceed with cache synchronization
                if logger_instance:
                    logger_instance.info(f"{log_prefix}: Cache miss detected, starting synchronization...")
                else:
                    print(f"{log_prefix}: 🔄 Cache miss detected, starting synchronization...")
                
                # Step 2: Synchronize cache
                cache_success, cache_msg = await sync_radarr_cache_for_movie(
                    session=session,
                    radarr_url=radarr_url,
                    headers=headers,
                    movie_id=movie_id,
                    target_guid=movie_guid,
                    movie_title=movie_title,
                    logger_instance=logger_instance
                )
                
                if cache_success:
                    # Step 3: Now safe to download after cache sync
                    if logger_instance:
                        logger_instance.info(f"{log_prefix}: Retrying download after cache sync...")
                    else:
                        print(f"{log_prefix}: 🔄 Retrying download after cache sync...")
                    
                    async with session.post(
                        f"{radarr_url}/api/v3/release",
                        headers=headers,
                        json=grab_payload
                    ) as retry_resp:
                        if retry_resp.status in [200, 201]:
                            success_msg = f"Download successful after cache sync: {movie_title}"
                            if logger_instance:
                                logger_instance.info(f"{log_prefix}: ✅ {success_msg}")
                            else:
                                print(f"{log_prefix}: ✅ {success_msg}")
                            return True, success_msg
                        else:
                            retry_error = await retry_resp.text()
                            if logger_instance:
                                logger_instance.warning(f"{log_prefix}: Retry failed: {retry_resp.status} - {retry_error}")
                            else:
                                print(f"{log_prefix}: ⚠️ Retry failed: {retry_resp.status} - {retry_error}")
                # Cache sync failed - continue to fallback strategy
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Cache sync failed, falling back to MovieSearch...")
                else:
                    print(f"{log_prefix}: ⚠️ Cache sync failed, falling back to MovieSearch...")
        
        # Step 4: Intelligent Fallback System (Enhanced)
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Activating intelligent fallback system...")
        else:
            print(f"{log_prefix}: 🤖 Activating intelligent fallback system...")
        
        try:
            # Initialize the enhanced fallback system
            from _internal.utils.real_time_telemetry import RealTimeTelemetry
            
            # Load settings for fallback system
            telemetry_config = {
                'database_path': '_internal/data/memory_store.db',
                'log_level': 'INFO',
                # 🔧 FIX: Preserve state file across runs for intelligent fallback
                'clear_on_start': False
            }
            telemetry = RealTimeTelemetry(telemetry_config, logger_instance)
            
            # Create settings dict for fallback system
            from utils.common_helpers import load_settings
            settings_dict = load_settings()
            
            # Initialize intelligent fallback system with telemetry
            fallback_system = IntelligentFallbackSystem(settings_dict, logger_instance, telemetry)
            
            # Try to find a better candidate using preflight decisions
            movie_title_for_search = movie_data.get('title', movie_title)
            preflight_result = fallback_system.search_preflight_decision(movie_title_for_search)
            
            if preflight_result:
                # Found a preflight decision, try to get the next best candidate
                acceptable_candidates = preflight_result.get('acceptable_candidates', [])
                if acceptable_candidates and len(acceptable_candidates) > 1:
                    # Try the second best candidate (first one already failed)
                    next_candidate = acceptable_candidates[1]
                    fallback_guid = next_candidate.get('guid')
                    
                    if fallback_guid:
                        if logger_instance:
                            logger_instance.info(f"{log_prefix}: 🎯 Found fallback candidate: {next_candidate.get('title', 'Unknown')}")
                        else:
                            print(f"{log_prefix}: 🎯 Found fallback candidate")
                        
                        # Try downloading the fallback candidate
                        fallback_payload = {"guid": fallback_guid, "indexerId": movie_indexer_id}
                        
                        async with session.post(
                            f"{radarr_url}/api/v3/release",
                            headers=headers,
                            json=fallback_payload
                        ) as fallback_resp:
                            if fallback_resp.status in [200, 201, 202]:
                                fallback_msg = f"Intelligent fallback succeeded: {next_candidate.get('title', 'Unknown')}"
                                if logger_instance:
                                    logger_instance.info(f"{log_prefix}: ✅ {fallback_msg}")
                                else:
                                    print(f"{log_prefix}: ✅ Intelligent fallback succeeded")
                                return True, fallback_msg
        
        except Exception as fallback_err:
            if logger_instance:
                logger_instance.warning(f"{log_prefix}: Intelligent fallback failed: {fallback_err}")
            else:
                print(f"{log_prefix}: ⚠️ Intelligent fallback failed: {fallback_err}")
        
        # Step 5: Final fallback to MovieSearch
        if logger_instance:
            logger_instance.info(f"{log_prefix}: Falling back to basic MovieSearch...")
        else:
            print(f"{log_prefix}: 🔄 Falling back to basic MovieSearch...")
        
        async with session.post(
            f"{radarr_url}/api/v3/command",
            headers=headers,
            json={"name": "MoviesSearch", "movieIds": [movie_id]}
        ) as search_resp:
            if search_resp.status in [200, 201, 202]:
                fallback_msg = f"MovieSearch triggered as fallback for: {movie_title}"
                if logger_instance:
                    logger_instance.info(f"{log_prefix}: ✅ {fallback_msg}")
                else:
                    print(f"{log_prefix}: ✅ MovieSearch triggered as fallback")
                return True, fallback_msg
            else:
                search_error = await search_resp.text()
                if logger_instance:
                    logger_instance.warning(f"{log_prefix}: Fallback MovieSearch failed: {search_resp.status} - {search_error}")
                else:
                    print(f"{log_prefix}: ⚠️ Fallback MovieSearch failed: {search_resp.status}")
        
        # All attempts failed
        final_error = f"All download attempts failed for movie: {movie_title}"
        if logger_instance:
            logger_instance.error(f"{log_prefix}: ❌ {final_error}")
        else:
            print(f"{log_prefix}: ❌ {final_error}")
        
        return False, final_error
    
    except Exception as download_err:
        error_msg = f"Movie download error: {download_err}"
        if logger_instance:
            logger_instance.error(f"{log_prefix}: ❌ {error_msg}")
        else:
            print(f"{log_prefix}: ❌ {error_msg}")
        return False, error_msg


async def _diagnose_sonarr_episode_search(session, sonarr_url: str, headers: dict, series_id: int, target_episode_id: int, season_num: int, episode_num: int, logger_instance):
    """After triggering an EpisodeSearch, gather diagnostic information to explain lack of activity.

    Steps:
      1. Poll command list for the recent EpisodeSearch status.
      2. Inspect queue for entries for this series.
      3. List enabled indexers.
      4. Provide likely causes if nothing is queued.
    """
    try:
        # 1. Poll the command status (up to ~20s)
        search_command_id = None
        for _ in range(10):  # 10 * 2s = 20s max
            async with session.get(f"{sonarr_url}/api/v3/command", headers=headers) as cmd_resp:
                if cmd_resp.status == 200:
                    commands = await cmd_resp.json()
                    # Find most recent EpisodeSearch command referencing our episode id (if present in body) or series
                    for cmd in commands:
                        if cmd.get("name") == "EpisodeSearch":
                            body = cmd.get("body") or {}
                            episode_ids = body.get("episodeIds") or []
                            if target_episode_id in episode_ids:
                                search_command_id = cmd.get("id")
                                state = cmd.get("state") or cmd.get("status")
                                if state in ("queued", "started", "inProgress"):
                                    await asyncio.sleep(2)
                                    break
                                else:
                                    logger_instance.info(f"   🧪 EpisodeSearch command state: {state}")
                                    search_command_id = cmd.get("id")
                                    break
                    else:
                        # No matching command yet
                        await asyncio.sleep(2)
                        continue
                    # If we broke out because state finished, stop outer loop
                    break
        # 2. Inspect queue for this series
        sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
        queue_json = await sonarr.get_queue_page(session, page=1, page_size=50)
        series_queue_items = []
        records = queue_json.get("records") if isinstance(queue_json, dict) else queue_json
        for rec in records:
            series = rec.get("series") or {}
            if series.get("id") == series_id:
                series_queue_items.append({
                    "title": rec.get("title"),
                    "size": rec.get("size"),
                    "sizeleft": rec.get("sizeleft"),
                    "status": rec.get("status"),
                    "trackedDownloadState": rec.get("trackedDownloadState")
                })
        if series_queue_items:
            logger_instance.info(f"   🧾 Queue has {len(series_queue_items)} item(s) for the series (showing first): {series_queue_items[0]}")
        else:
            logger_instance.warning("   ⚠️ No queue items found yet for the episode after search.")

        # 3. List indexers
        sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
        indexers = await sonarr.get_indexers(session)
        enabled = [i for i in indexers if i.get("enableRss") or i.get("enableSearch")]
        logger_instance.info(f"   🌐 Indexers enabled: {len(enabled)}/{len(indexers)} (need at least one with search enabled)")
        if not enabled:
            logger_instance.warning("   ❌ No enabled indexers. Sonarr cannot find releases without at least one enabled search-capable indexer.")

        # 4. Potential causes if still nothing
        if not series_queue_items:
            logger_instance.info(
                "   🔎 Diagnostic hints: Possible reasons: (a) No releases match quality profiles 3/4; (b) Indexers disabled or misconfigured categories; (c) Episode not yet available or improper scene numbering; (d) Minimum Free Space / Root path issues; (e) Delay profile waiting window."
            )
            # Suggest manual search command as a follow-up
            logger_instance.info(
                f"   💡 You can manually trigger another search later: Sonarr UI -> Series -> The Office -> Season {season_num} -> Episode {episode_num} -> Manual Search to inspect available releases."
            )
            # 5. Attempt manual search release fetch via API for deeper insight
            try:
                sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
                releases = await sonarr.get_releases(session, target_episode_id)
                logger_instance.info(f"   🧪 Manual search candidates: {len(releases)}")
                if releases:
                    # Classify approvals and rejections using Sonarr-provided signals
                    approved = [r for r in releases if r.get("approved")]
                    rejected = [r for r in releases if not r.get("approved")]

                    logger_instance.info(
                        f"   📊 Summary: approved={len(approved)} | rejected={len(rejected)}"
                    )

                    # Show top approved candidates (up to 3)
                    if approved:
                        logger_instance.info("   ✅ Top approved candidates:")
                        for r in approved[:3]:
                            q = (r.get("quality") or {}).get("quality") or {}
                            q_name = q.get("name")
                            size = r.get("size")
                            indexer = r.get("indexer") or r.get("indexerId")
                            langs = ",".join([ln.get("name") for ln in (r.get("languages") or []) if isinstance(ln, dict)]) or "?"
                            logger_instance.info(
                                f"      • {q_name} size={size} idx={indexer} lang={langs} title={str(r.get('title') or '')[:90]}"
                            )
                    else:
                        logger_instance.info("   ❌ No approved candidates.")

                    # Aggregate rejection reasons
                    if rejected:
                        from collections import Counter
                        reason_counts = Counter()
                        reason_examples = {}
                        for r in rejected:
                            reasons = r.get("rejections") or []
                            for reason in reasons:
                                reason_counts[reason] += 1
                                if reason not in reason_examples:
                                    reason_examples[reason] = r
                        if reason_counts:
                            logger_instance.info("   🚫 Top rejection reasons:")
                            for reason, cnt in reason_counts.most_common(5):
                                logger_instance.info(f"      • {reason} (x{cnt})")
                        else:
                            logger_instance.info("   🚫 Rejected, but Sonarr did not provide explicit reasons.")

                    # If nothing queued, add concise guidance
                    logger_instance.warning(
                        "   ⚠️ Nothing queued yet. Check: (1) quality profile cutoff/allowed, (2) language profile, (3) release restrictions/preferred words, (4) delay profile, (5) indexer categories."
                    )

                    # Fetch quality definitions to see if min sizes are unrealistically high
                    try:
                        async with session.get(f"{sonarr_url}/api/v3/qualitydefinition", headers=headers) as qd_resp:
                            if qd_resp.status == 200:
                                qdefs = await qd_resp.json()
                                # Build map quality name -> (minSize, maxSize)
                                qmap = {qd.get('quality', {}).get('name'): (qd.get('minSize'), qd.get('maxSize')) for qd in qdefs}
                                # Aggregate largest candidate size per quality
                                largest_by_quality = {}
                                for r in releases:
                                    q_inner = (r.get('quality') or {}).get('quality') or {}
                                    qn = q_inner.get('name')
                                    if not qn:
                                        continue
                                    size = r.get('size') or 0
                                    if size > (largest_by_quality.get(qn) or 0):
                                        largest_by_quality[qn] = size
                                flagged = []
                                for qn, (min_sz, max_sz) in qmap.items():
                                    cand_size = largest_by_quality.get(qn)
                                    # Convert min_sz which is per-minute MB into absolute bytes for 22 minute assumption? Actually Sonarr stores absolute MB for entire episode. We'll approximate runtime 22m typical for The Office.
                                    if min_sz and cand_size and (min_sz * 1024 * 1024) > cand_size:
                                        flagged.append((qn, min_sz, cand_size))
                                if flagged:
                                    logger_instance.warning("   🚫 Quality definition minimums likely too high; some releases may be 'smaller than minimum'. Adjust in Sonarr: Settings > Quality > Quality Definitions.")
                                    for qn, min_sz, cand_size in flagged[:5]:
                                        logger_instance.warning(f"      - {qn}: min {min_sz} MB > largest candidate {cand_size/1024/1024:.1f} MB")
                                    logger_instance.info("   💡 Use your chosen per-hour targets; ensure mins are not higher than typical candidates for the episode runtime.")
                            else:
                                logger_instance.warning(f"   ⚠️ Could not fetch quality definitions (status {qd_resp.status}).")
                    except Exception as qd_e:
                        logger_instance.warning(f"   ⚠️ Quality definitions diagnostic failed: {qd_e}")
                else:
                    logger_instance.info("   ℹ️ No releases currently returned by manual search endpoint either.")
            except Exception as inner_e:
                logger_instance.warning(f"   ⚠️ Manual release fetch failed: {inner_e}")
    except Exception as e:
        logger_instance.error(f"   🛑 Diagnostic routine failed: {e}")


async def _smart_select_and_grab_episode_release(session, sonarr_url: str, headers: dict, series_id: int, episode_id: int, logger_instance, settings_dict: dict | None = None):
    """If EpisodeSearch didn't queue anything, pick the best release ourselves and grab it.

    Strategy:
      - If queue already has items for the series/episode, do nothing.
      - Fetch series (runtime, qualityProfileId) and releases for episode.
      - Fetch quality definitions (min/preferred/max per hour) and compute per-episode targets.
      - Determine tier order from series year (favor 1080p for older shows like 2005; 2160p for newer).
      - Within the first tier that has any acceptable candidates, select the release whose size is closest to that tier's top-quality preferred size target.
      - Skip releases with Sonarr rejections.
      - POST /api/v3/release to grab using guid + indexerId.
    """
    try:
        # 0) If it's already queued, skip
        async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers, params={"page": 1, "pageSize": 100}) as queue_resp:
            if queue_resp.status == 200:
                qjson = await queue_resp.json()
                records = qjson.get("records") if isinstance(qjson, dict) else qjson
                for rec in records:
                    epi = (rec.get("episode") or {})
                    if epi.get("id") == episode_id:
                        logger_instance.info("   ✅ Episode already in queue; skipping smart selection.")
                        return False

        # 1) Get series + runtime + year + profile
        async with session.get(f"{sonarr_url}/api/v3/series/{series_id}", headers=headers) as s_resp:
            if s_resp.status != 200:
                logger_instance.warning("   ⚠️ Smart select: failed to read series metadata")
                return False
            series = await s_resp.json()
        runtime_min = series.get("runtime") or 45
        year = series.get("year") or 2010
        quality_profile_id = series.get("qualityProfileId")

    # 2) Quality definitions
        async with session.get(f"{sonarr_url}/api/v3/qualitydefinition", headers=headers) as qd_resp:
            if qd_resp.status != 200:
                logger_instance.warning("   ⚠️ Smart select: cannot fetch quality definitions")
                return False
            qdefs = await qd_resp.json()
        # Build maps
        qdef_map = {}
        for qd in qdefs:
            qname = (qd.get("quality") or {}).get("name")
            if qname:
                qdef_map[qname] = {
                    "min": qd.get("minSize"),
                    "pref": qd.get("preferredSize"),
                    "max": qd.get("maxSize"),
                }

        # helper: per-episode bytes from per-hour GiB
        def per_episode_bytes(gib_per_hour: float | int | None):
            if not gib_per_hour:
                return None
            return float(gib_per_hour) * (runtime_min / 60.0) * (1024**3)

        # 3) Determine tier order by year
        # Prefer Remux when available; include WEB synonyms and Raw-HD.
        tier_1080 = [
            "Bluray-1080p Remux",
            "Bluray-1080p",
            "WEB 1080p",
            "WEBDL-1080p",
            "WEBRip-1080p",
            "Raw-HD",
            "HDTV-1080p",
        ]
        tier_720 = [
            "Bluray-720p",
            "WEB 720p",
            "WEBDL-720p",
            "WEBRip-720p",
            "HDTV-720p",
        ]
        tier_2160 = [
            "Bluray-2160p Remux",
            "Bluray-2160p",
            "WEB 2160p",
            "WEBDL-2160p",
            "WEBRip-2160p",
            "HDTV-2160p",
        ]
        tier_sd = [
            "Bluray-576p",
            "Bluray-480p",
            "WEB 480p",
            "WEBDL-480p",
            "WEBRip-480p",
            "DVD",
            "SDTV",
        ]

        if year and year <= 2008:
            tier_order = [tier_1080, tier_720, tier_sd, tier_2160]
            top_targets = {tuple(tier_1080): "Bluray-1080p", tuple(tier_720): "Bluray-720p"}
        elif 2009 <= (year or 2010) <= 2015:
            tier_order = [tier_1080, tier_720, tier_2160]
            top_targets = {tuple(tier_1080): "Bluray-1080p", tuple(tier_720): "Bluray-720p"}
        else:
            tier_order = [tier_2160, tier_1080, tier_720]
            top_targets = {tuple(tier_2160): "Bluray-2160p", tuple(tier_1080): "Bluray-1080p"}

        # 2b) Read optional scoring preference from settings (prefer larger within same quality?)
        # Read optional behavior from settings (default to True per user preference)
        prefer_larger_same_quality = True
        try:
            from utils.common_helpers import get_setting as _get
            val = _get("Sonarr", "prefer_larger_same_quality_remux", settings_dict=settings_dict, default=True)
            if isinstance(val, bool):
                prefer_larger_same_quality = val
            else:
                prefer_larger_same_quality = str(val).lower() in ("1", "true", "yes", "on")
        except Exception:
            # Keep default True if settings unavailable
            prefer_larger_same_quality = True

        # Read optional behavior from settings: allow larger non-Remux to beat Remux
        allow_non_remux_if_larger = False
        try:
            from utils.common_helpers import get_setting as _get
            val2 = _get("Sonarr", "allow_non_remux_if_larger", settings_dict=settings_dict, default=True)
            if isinstance(val2, bool):
                allow_non_remux_if_larger = val2
            else:
                allow_non_remux_if_larger = str(val2).lower() in ("1", "true", "yes", "on")
        except Exception:
            allow_non_remux_if_larger = True

        # 4) Fetch releases
        async with session.get(f"{sonarr_url}/api/v3/release", headers=headers, params={"episodeId": episode_id}) as rel_resp:
            if rel_resp.status != 200:
                logger_instance.warning("   ⚠️ Smart select: cannot fetch releases")
                return False
            all_releases = await rel_resp.json()
        if not all_releases:
            logger_instance.info("   ℹ️ Smart select: no candidate releases returned.")
            return False

        # Check season pack configuration setting
        enable_season_packs = get_setting("Advanced", "enable_season_packs", settings_dict=settings_dict, default=False, expected_type=bool)
        prefer_season_packs_for_full_seasons = get_setting("Advanced", "prefer_season_packs_for_full_seasons", settings_dict=settings_dict, default=True, expected_type=bool)
        season_pack_fallback_threshold = get_setting("Advanced", "season_pack_fallback_threshold", settings_dict=settings_dict, default=0.50, expected_type=float)
        season_pack_max_age_days = get_setting("Advanced", "season_pack_max_age_days", settings_dict=settings_dict, default=0, expected_type=int)

        # Season pack filtering logic - conditional based on configuration
        def is_season_pack(release_title: str) -> bool:
            """Detect if a release is a season pack based on naming patterns."""
            if not release_title:
                return False

            title_lower = release_title.lower()

            # Season pack indicators - these should NEVER be downloaded
            season_pack_patterns = [
                # Direct season indicators - ENHANCED
                r'\bs\d{1,2}(?!\d|\s*e\d)',  # S01, S02, etc. without episode number
                r'\bs\d{1,2}\.(?!e\d)',      # S01., S02. without episode (matches FLCL.S01.)
                r'\bs\d{1,2}\b(?!.*e\d)',    # S01 anywhere without E## later in title
                r'\bseason\s*\d{1,2}(?!\s*episode)',  # "Season 1", "Season 01"
                r'\bcomplete\s*season',      # "Complete Season"
                r'\bfull\s*season',          # "Full Season"
                r'\bentire\s*season',        # "Entire Season"
                r'\bwhole\s*season',         # "Whole Season"
                r'\bseries\s*complete',      # "Series Complete"
                r'\bcomplete\s*series',      # "Complete Series"
                # Multi-episode without specific episode numbers
                r'\bmulti\s*episode',        # "Multi Episode"
                r'\ball\s*episodes',         # "All Episodes"
                # Season range indicators
                r'\bs\d{1,2}-s\d{1,2}',      # S01-S02, etc.
                r'\bs\d{1,2}\s*-\s*\d{1,2}', # S01-02, etc.
                # Additional common season pack patterns
                r'\bseason\s*pack',          # "Season Pack"
                r'\bs\d{1,2}\s*pack',        # "S01 Pack"
                r'\bs\d{1,2}\s*complete',    # "S01 Complete"
                r'\bs\d{1,2}\s*full',        # "S01 Full"
            ]

            import re
            for pattern in season_pack_patterns:
                if re.search(pattern, title_lower):
                    return True

            # Additional check: if it contains "S##" but no "E##", likely season pack
            has_season = re.search(r'\bs\d{1,2}', title_lower)
            has_episode = re.search(r'\be\d{1,2}', title_lower)

            if has_season and not has_episode:
                return True

            return False

        # Season pack filtering logic - conditional based on configuration
        releases = []
        season_packs_found = []

        if not enable_season_packs:
            # Filter out season packs (old behavior when disabled)
            for release in all_releases:
                release_title = release.get("title", "")

                if is_season_pack(release_title):
                    season_packs_found.append(release_title)
                    logger_instance.warning(f"   🚨 REJECTED SEASON PACK: {release_title}")
                else:
                    releases.append(release)

            if season_packs_found:
                logger_instance.warning(f"   🚨 Filtered out {len(season_packs_found)} season pack(s) - INDIVIDUAL EPISODES ONLY")
                for pack_title in season_packs_found[:3]:  # Show first 3 examples
                    logger_instance.warning(f"      ❌ {pack_title}")

            if not releases:
                logger_instance.warning("   ⚠️ Smart select: no valid individual episode releases after season pack filtering")
                return False

            logger_instance.info(f"   ✅ Filtered to {len(releases)} individual episode candidates (removed {len(season_packs_found)} season packs)")
        else:
            # Season packs allowed – do NOT filter them out
            releases = list(all_releases)  # include all candidates
            # Log that packs are being allowed
            pack_count = sum(1 for r in releases if is_season_pack(r.get("title", "")))
            if pack_count:
                logger_instance.info(f"   🎁 Season pack releases detected (count={pack_count}) and allowed by configuration.")
            logger_instance.info(f"   ✅ Season pack support ENABLED - analyzing {len(releases)} release candidates (including any season packs)")

        # Utility: filter out Sonarr-rejected
        def is_rejected(rel: dict) -> bool:
            rej = rel.get("rejections") or []
            return bool(rej)

    # Evaluate tiers
        for tier in tier_order:
            # Resolve a good top target name per tier and whether this tier has any Remux candidates.
            # If there are Remux candidates, treat Remux as the top group even if quality definitions
            # don't include a specific Remux entry.
            tier_str = " ".join(tier)
            # Detect if releases for this tier include any Remux entries
            tier_has_remux = False
            try:
                # Fast scan of releases for this tier
                for _r in releases:
                    _qn = (((_r.get("quality") or {}).get("quality") or {}).get("name") or "")
                    if _qn in tier and ("remux" in _qn.lower()):
                        tier_has_remux = True
                        break
            except Exception:
                tier_has_remux = False

            if any("2160" in q for q in tier):
                default_top_qname = "Bluray-2160p"
            elif any("1080" in q for q in tier):
                default_top_qname = "Bluray-1080p"
            elif any("720" in q for q in tier):
                default_top_qname = "Bluray-720p"
            else:
                default_top_qname = tier[0]

            # If Sonarr has a quality definition entry for the exact default top name, use it for sizing
            top_qname = default_top_qname
            # Preferred target bytes for top quality in this tier
            pref_gib_h = (qdef_map.get(top_qname) or {}).get("pref")
            target_bytes = per_episode_bytes(pref_gib_h) if pref_gib_h else None

            # Helper: whether a candidate is considered the "top group" for this tier
            def is_top_group_quality(qname: str) -> bool:
                if not qname:
                    return False
                if tier_has_remux and not allow_non_remux_if_larger:
                    return "remux" in qname.lower()
                return qname == top_qname

            # candidates in this tier
            candidates = []
            for r in releases:
                qn = ((r.get("quality") or {}).get("quality") or {}).get("name")
                if qn not in tier:
                    continue
                if is_rejected(r):
                    continue
                # Only consider candidates Sonarr marks as approved (meets profile/cutoff/filters)
                if not bool(r.get("approved")):
                    continue
                # Language preference scoring
                langs = r.get("languages") or []
                lang_names = [str((ln.get("name") or "")).lower() for ln in langs if isinstance(ln, dict)]
                title_l = str(r.get("title") or "").lower()
                has_en = any("english" == n for n in lang_names)
                is_german = ("german" in lang_names) or ("german" in title_l)
                is_dubbed = ("dubbed" in title_l)
                # rank: prefer English; penalize dubbed/non-English
                if has_en and not (is_german or is_dubbed):
                    lang_rank = 2
                elif has_en:
                    lang_rank = 1
                elif is_german or is_dubbed:
                    lang_rank = -1
                else:
                    lang_rank = 0
                size = r.get("size") or 0
                # Distance to target (if target known), otherwise prefer larger
                if allow_non_remux_if_larger:
                    # Size dominates within the tier when allowed
                    score = size
                elif target_bytes:
                    dist = abs(size - target_bytes)
                    # If configured, prefer the larger size when within the top quality group for this tier
                    if prefer_larger_same_quality and is_top_group_quality(qn):
                        score = size
                    else:
                        score = -dist
                else:
                    score = size
                # Weight language heavily and prefer Remux when available
                is_remux = "remux" in (qn or "").lower()
                remux_boost = (0 if allow_non_remux_if_larger else (5e12 if is_remux else 0))
                weighted_score = remux_boost + (lang_rank * 1e12) + float(score)
                # Attach debug info for later explanation
                candidates.append((weighted_score, r, lang_rank, {
                    "qn": qn,
                    "size": size,
                    "target": target_bytes,
                    "dist": (abs(size - target_bytes) if target_bytes else None),
                    "lang_rank": lang_rank,
                    "remux": is_remux,
                    "pref_larger_same_quality": prefer_larger_same_quality,
                    "top_q_for_tier": ("Remux-group" if tier_has_remux else top_qname)
                }))

            if candidates:
                # choose best
                # If any English-present candidates exist, restrict to those
                if any(c[2] >= 1 for c in candidates):
                    candidates = [c for c in candidates if c[2] >= 1]
                candidates.sort(key=lambda x: x[0], reverse=True)
                best = candidates[0][1]
                debug = candidates[0][3]
                qn = ((best.get("quality") or {}).get("quality") or {}).get("name")
                # Log concise reasoning
                try:
                    size_mb = (debug.get("size") or 0) / (1024*1024)
                    tgt_mb = (debug.get("target") or 0) / (1024*1024) if debug.get("target") else None
                    dist_mb = (debug.get("dist") or 0) / (1024*1024) if debug.get("dist") else None
                    # Consider Remux-group a top group for the purpose of strategy explanation
                    top_label = debug.get("top_q_for_tier")
                    is_top_choice = (prefer_larger_same_quality and ((top_label == "Remux-group" and "remux" in (qn or "").lower()) or (qn == top_label)))
                    strat = "larger-within-quality" if is_top_choice else "closest-to-preferred"
                    logger_instance.info(
                        f"   🤖 Smart select chose: {qn} size={best.get('size')} title={str(best.get('title') or '')[:90]} | reason: {strat}, lang_rank={debug.get('lang_rank')}, remux={debug.get('remux')}, targetMB={tgt_mb:.0f if tgt_mb else 0}, distMB={dist_mb:.0f if dist_mb else 0}"
                    )
                except Exception:
                    logger_instance.info(f"   🤖 Smart select chose: {qn} size={best.get('size')} title={str(best.get('title') or '')[:90]}")
                # Pre-cleanup in completed_raw (optional) to avoid duplicate .1 folders
                try:
                    do_preclean = True
                    try:
                        from utils.common_helpers import get_setting as _get
                        pc_val = _get("Sonarr", "pre_cleanup_completed_raw", settings_dict=settings_dict, default=True)
                        if isinstance(pc_val, bool):
                            do_preclean = pc_val
                        else:
                            do_preclean = str(pc_val).lower() in ("1","true","yes","on")
                    except Exception:
                        do_preclean = True
                    if do_preclean:
                        # Resolve completed_raw directory path
                        complete_raw_dir = None
                        try:
                            raw_path = get_path_setting("Paths", "download_complete_raw_dir", settings_dict=settings_dict)
                            if raw_path:
                                if "%(download_client_active_dir)s" in raw_path:
                                    base_dir = get_path_setting("Paths", "download_client_active_dir", settings_dict=settings_dict) or str(Path("workspace/1_downloading"))
                                    complete_raw_dir = Path(raw_path.replace("%(download_client_active_dir)s", base_dir))
                                else:
                                    complete_raw_dir = Path(raw_path)
                            else:
                                complete_raw_dir = Path("workspace/1_downloading/complete_raw")
                        except Exception:
                            complete_raw_dir = Path("workspace/1_downloading/complete_raw")
                        # Find likely existing folder(s) for same NZB title (avoid accidental deletion by matching SxxEyy too)
                        rel_title = str(best.get("title") or "")
                        m = re.search(r"S(\d{2})E(\d{2})", rel_title, re.IGNORECASE)
                        se_tag = m.group(0) if m else None
                        if complete_raw_dir and complete_raw_dir.exists() and rel_title:
                            norm_rel = re.sub(r"[^a-z0-9]+",".", rel_title.lower())
                            removed_any = False
                            for item in complete_raw_dir.iterdir():
                                if not item.is_dir():
                                    continue
                                iname = item.name.lower()
                                if se_tag and se_tag.lower() not in iname:
                                    continue
                                # require substantial overlap with release title to consider same
                                norm_item = re.sub(r"[^a-z0-9]+",".", iname)
                                if norm_rel[:40] and norm_rel[:40] in norm_item:
                                    import shutil
                                    try:
                                        shutil.rmtree(item, ignore_errors=False)
                                        removed_any = True
                                        logger_instance.info(f"   🧹 Pre-clean: removed existing raw folder: {item.name}")
                                    except Exception as de:
                                        logger_instance.warning(f"   ⚠️ Pre-clean: failed to remove {item}: {de}")
                            if not removed_any:
                                pass
                except Exception as pc_e:
                    logger_instance.warning(f"   ⚠️ Pre-clean step error: {pc_e}")

                # Grab it
                payload = {
                    "guid": best.get("guid"),
                    "indexerId": best.get("indexerId"),
                }
                async with session.post(f"{sonarr_url}/api/v3/release", headers=headers, json=payload) as grab_resp:
                    if grab_resp.status in (200, 201):
                        logger_instance.info("   📥 Smart select: release grabbed successfully.")
                        return True
                    else:
                        txt = await grab_resp.text()
                        logger_instance.warning(f"   ⚠️ Smart select: grab failed ({grab_resp.status}): {txt}")
                        # Continue to next candidate tier if grab failed due to profile restrictions
        logger_instance.info("   ℹ️ Smart select: no acceptable release could be grabbed.")
        return False

    except Exception as e:
        logger_instance.error(f"   🛑 Smart selection failed: {e}")
        return False


async def _compare_and_optionally_replace_queued_release(session, sonarr_url: str, headers: dict, series_id: int, episode_id: int, logger_instance, replace_min_mb: int = 100, wait_for_queue_seconds: int = 45, poll_interval_seconds: float = 3.0):
    """If Sonarr queued a release, compare it to the largest manual candidate of the same quality.

    If manual best is larger by replace_min_mb, cancel the queued item (no blacklist) and grab manual best.
    """
    try:
        # Find queued item for this episode; if not present, poll briefly to allow EpisodeSearch to populate the queue
        deadline = asyncio.get_event_loop().time() + max(0, wait_for_queue_seconds)
        queued_item = None
        first = True
        while True:
            async with session.get(f"{sonarr_url}/api/v3/queue", headers=headers, params={"page": 1, "pageSize": 100}) as qresp:
                if qresp.status != 200:
                    return False
                qjson = await qresp.json()
            records = qjson.get("records") if isinstance(qjson, dict) else (qjson or [])
            queued_item = None
            for rec in records or []:
                epi = rec.get("episode") or {}
                if epi.get("id") == episode_id:
                    queued_item = rec
                    break
            if queued_item:
                break
            now = asyncio.get_event_loop().time()
            if first:
                logger_instance.info(f"   ⏳ Waiting up to {wait_for_queue_seconds}s for Sonarr to queue an item for replacement...")
                first = False
            if now >= deadline:
                logger_instance.info("   ⏱️ Queue did not populate in time; skipping compare/replace.")
                return False
            await asyncio.sleep(poll_interval_seconds)

        # Ignore if already completed/imported
        if str(queued_item.get("status", "")).lower() in ("completed", "imported"):
            return False

        queued_size = queued_item.get("size") or 0
        qqual = ((queued_item.get("quality") or {}).get("quality") or {})
        queued_qname = qqual.get("name") or ""

        # Fetch manual candidates
        async with session.get(f"{sonarr_url}/api/v3/release", headers=headers, params={"episodeId": episode_id}) as rel_resp:
            if rel_resp.status != 200:
                return False
            releases = await rel_resp.json()
        if not releases:
            return False

        # Filter to same quality, approved, not rejected
        def not_rejected(r):
            return not bool(r.get("rejections") or [])
        same_quality = [r for r in releases if (((r.get("quality") or {}).get("quality") or {}).get("name") == queued_qname) and r.get("approved") and not_rejected(r)]
        if not same_quality:
            return False

        # Pick the largest by size
        manual_best = max(same_quality, key=lambda r: r.get("size") or 0)
        manual_size = manual_best.get("size") or 0
        # If queued size is unknown/zero, assume replacement is beneficial when manual has a real size
        if (queued_size or 0) <= 0 and manual_size > 0:
            delta_mb = manual_size / (1024*1024)
        else:
            delta_mb = (manual_size - queued_size) / (1024*1024)
        if delta_mb < replace_min_mb:
            logger_instance.info(f"   🔁 Queue vs manual: same quality '{queued_qname}'. Manual larger by {delta_mb:.0f} MB < threshold {replace_min_mb} MB; leaving queue unchanged.")
            return False

        # Replace: cancel queue item (no blacklist), then grab manual
        qid = queued_item.get("id")
        if qid is not None:
            async with session.delete(f"{sonarr_url}/api/v3/queue/{qid}?blacklist=false", headers=headers) as dresp:
                if dresp.status not in (200, 202, 204):
                    txt = await dresp.text()
                    logger_instance.warning(f"   ⚠️ Could not remove queued item for replacement ({dresp.status}): {txt}")
                    return False
        logger_instance.info(f"   🔄 Replacing queued {queued_qname} ({queued_size/1024/1024:.0f} MB) with larger manual {manual_size/1024/1024:.0f} MB")

        payload = {"guid": manual_best.get("guid"), "indexerId": manual_best.get("indexerId")}
        async with session.post(f"{sonarr_url}/api/v3/release", headers=headers, json=payload) as grab_resp:
            if grab_resp.status in (200, 201):
                logger_instance.info("   📥 Manual best grabbed after replacement.")
                return True
            else:
                txt = await grab_resp.text()
                logger_instance.warning(f"   ⚠️ Replacement grab failed ({grab_resp.status}): {txt}")
                return False
    except Exception as e:
        logger_instance.warning(f"   ⚠️ Compare/replace failed: {e}")
        return False


async def _enforce_episode_size_limits(session, sonarr_url: str, headers: dict, series_id: int,
                                       max_episode_gb: float, request_specificity: str,
                                       target_season: int | None, target_episode: int | None,
                                       logger_instance, delay_seconds: float = 5.0) -> dict:
    """Check Sonarr queue for oversized episode downloads for a given series and blacklist/remove them.

    Args:
        session: aiohttp session
        sonarr_url: Base Sonarr URL
        headers: Auth headers
        series_id: Sonarr series ID
        max_episode_gb: Size threshold in GB
        request_specificity: one of full_series, specific_season, specific_episode
        target_season: season number if specific
        target_episode: episode number if specific
        logger_instance: logger
    Returns:
        dict summary with counts
    """
    try:
        threshold_bytes = max_episode_gb * (1024 ** 3)
        # Allow short delay for queue population after adding series/search trigger
        await asyncio.sleep(delay_seconds)

        # Fetch queue (pagination single page large enough for typical series initial grabs)
        async with session.get(f"{sonarr_url}/api/v3/queue?page=1&pageSize=250&includeUnknownSeries=true", headers=headers) as resp:
            if resp.status != 200:
                logger_instance.warning(f"Episode size enforcement: failed to fetch queue ({resp.status})")
                return {"checked": 0, "removed": 0, "reason": "queue_fetch_failed"}
            queue_data = await resp.json()

        # Sonarr returns {"page":1, "pageSize":250, "sortKey":"timeleft", "records":[...]}
        records = queue_data.get("records", queue_data if isinstance(queue_data, list) else [])
        oversized = []
        for r in records:
            if int(r.get("seriesId", -1)) != series_id:
                continue
            size = r.get("size") or 0
            # Episode details maybe nested under episode or episodes
            ep = r.get("episode") or {}
            season_num = ep.get("seasonNumber")
            episode_num = ep.get("episodeNumber")

            # Filter based on specificity
            if request_specificity == "specific_episode" and not (season_num == target_season and episode_num == target_episode):
                continue
            if request_specificity == "specific_season" and season_num != target_season:
                continue

            if size > threshold_bytes:
                oversized.append({
                    "queueId": r.get("id"),
                    "title": r.get("title"),
                    "season": season_num,
                    "episode": episode_num,
                    "size_gb": round(size / (1024**3), 2)
                })

        removed = 0
        for item in oversized:
            qid = item["queueId"]
            if qid is None:
                continue
            async with session.delete(f"{sonarr_url}/api/v3/queue/{qid}?blacklist=true", headers=headers) as dresp:
                if dresp.status in [200, 202, 204]:
                    removed += 1
                    logger_instance.warning(f"🛑 Removed oversized episode download S{item['season']:02d}E{item['episode']:02d} ({item['size_gb']} GB > {max_episode_gb} GB)")
                else:
                    text = await dresp.text()
                    logger_instance.warning(f"⚠️ Failed to remove oversized item queueId={qid}: {dresp.status} {text}")

        if oversized:
            logger_instance.info(f"Episode size enforcement summary: checked {len(records)} queue items, oversized={len(oversized)}, removed={removed}")
        else:
            logger_instance.info("Episode size enforcement: no oversized items detected")

        return {"checked": len(records), "oversized": len(oversized), "removed": removed}
    except Exception as e:
        logger_instance.error(f"Episode size enforcement error: {e}")
        return {"checked": 0, "removed": 0, "reason": f"exception_{e}"}

async def _ensure_season_pack_blocking(settings_dict: dict, logger_instance: logging.Logger) -> bool:
    """
    Ensure Sonarr has a release profile that blocks season packs.
    This prevents Sonarr from automatically downloading season packs.
    """
    try:
        sonarr_url = get_setting("Sonarr", "url", settings_dict=settings_dict, default="http://localhost:8989")
        sonarr_api_key = get_setting("Sonarr", "api_key", settings_dict=settings_dict)

        if not sonarr_api_key:
            logger_instance.warning("⚠️ No Sonarr API key - cannot configure season pack blocking")
            return False

        headers = {"X-Api-Key": sonarr_api_key}

        async with aiohttp.ClientSession() as session:
            # Check if season pack blocking profile already exists
            async with session.get(f"{sonarr_url}/api/v3/releaseprofile", headers=headers) as resp:
                if resp.status == 200:
                    profiles = await resp.json()

                    # Look for existing season pack blocking profile
                    for profile in profiles:
                        if profile.get("name") == "PlexMovieAutomator - Block Season Packs":
                            logger_instance.info("✅ Season pack blocking profile already exists")
                            return True

                    # Create new season pack blocking profile
                    season_pack_profile = {
                        "name": "PlexMovieAutomator - Block Season Packs",
                        "enabled": True,
                        "required": [],
                        "ignored": [
                            # Season pack patterns to block - ENHANCED for stronger detection
                            "\\bS\\d{1,2}(?![\\dE])",  # S01, S02 without episode (simplified regex)
                            "\\bS\\d{1,2}\\b(?!.*E\\d)",  # S01, S02 anywhere without E## later
                            "\\bSeason[\\s\\._-]*\\d{1,2}",  # Season 1, Season.01, Season_01
                            "\\bComplete[\\s\\._-]*Season",  # Complete Season, Complete.Season
                            "\\bFull[\\s\\._-]*Season",     # Full Season, Full.Season
                            "\\bEntire[\\s\\._-]*Season",   # Entire Season
                            "\\bWhole[\\s\\._-]*Season",    # Whole Season
                            "\\bSeries[\\s\\._-]*Complete", # Series Complete
                            "\\bComplete[\\s\\._-]*Series", # Complete Series
                            "\\bMulti[\\s\\._-]*Episode",   # Multi Episode
                            "\\bAll[\\s\\._-]*Episodes",    # All Episodes
                            # Additional patterns for common season pack formats
                            "\\bSeason[\\s\\._-]*Pack",     # Season Pack
                            "\\bS\\d{1,2}[\\s\\._-]*Pack",  # S01 Pack, S01.Pack
                            "\\bS\\d{1,2}[\\s\\._-]*Complete", # S01 Complete
                            "\\bS\\d{1,2}[\\s\\._-]*Full",     # S01 Full
                            "\\b\\d{1,2}x\\d{1,2}-\\d{1,2}x\\d{1,2}", # 1x01-1x06 ranges
                            "\\bE\\d{1,2}-E\\d{1,2}",       # E01-E06 ranges
                            "\\bE\\d{1,2}\\s*-\\s*\\d{1,2}", # E01-06 ranges
                            # CRITICAL: Block the exact pattern we're seeing
                            "\\bS\\d{1,2}\\.",              # S01., S02. (period after season)
                            "\\bS\\d{1,2}\\s+\\d{4}p",      # S01 1080p, S01 720p (season + quality)
                            # ABSOLUTE BLOCKS - NEVER ALLOW THESE EVER
                            "FLCL\\.S01\\.",                 # Block FLCL.S01. specifically
                            "FLCL\\.S\\d{1,2}\\.",           # Block any FLCL.S##. pattern
                            "\\bS\\d{1,2}\\.\\d{3,4}p",      # S01.1080p, S02.720p format
                        ],
                        "preferred": [
                            # FORCE individual episodes to be preferred
                            {
                                "term": "\\bS\\d{1,2}E\\d{1,2}\\b",  # S01E01, S01E02, etc.
                                "score": 1000
                            },
                            {
                                "term": "\\b\\d{1,2}x\\d{1,2}\\b",   # 1x01, 1x02, etc.
                                "score": 1000
                            }
                        ],
                        "includePreferredWhenRenaming": False,
                        "indexerId": 0,  # Apply to all indexers
                        "tags": []
                    }

                    # Add the profile
                    async with session.post(f"{sonarr_url}/api/v3/releaseprofile", headers=headers, json=season_pack_profile) as add_resp:
                        if add_resp.status in [200, 201]:
                            logger_instance.info("✅ Created season pack blocking profile in Sonarr")
                            return True
                        else:
                            logger_instance.error(f"❌ Failed to create season pack blocking profile: {add_resp.status}")
                            return False
                else:
                    logger_instance.warning(f"⚠️ Cannot access Sonarr release profiles: {resp.status}")
                    return False

    except Exception as e:
        logger_instance.error(f"❌ Error configuring season pack blocking: {e}")
        return False


## Previous background season pack monitor was removed; preflight analyzer now evaluates packs


async def _add_tv_show_to_sonarr_modern(tv_data: dict, settings_dict: dict, logger_instance: logging.Logger) -> dict:
    """
    Enhanced TV show addition to Sonarr with season pack blocking and season-by-season progression.

    Key Changes:
    - Blocks season packs completely via release profiles
    - Only monitors Season 1 for full series requests
    - Configures proper episode-specific monitoring
    """
    import aiohttp
    import unicodedata

    # Season pack blocking now optional (preflight analyzer evaluates packs). Controlled by setting.
    # NOTE: The old queue monitor and blanket pack removal are deprecated (see CHANGELOG) – hybrid preflight handles safety.
    try:
        from utils.common_helpers import get_setting as _gs
        sp_block_enabled = _gs('Advanced', 'enable_season_pack_block_profile', settings_dict=settings_dict, default=False)
    except Exception:
        sp_block_enabled = False
    if sp_block_enabled:
        await _ensure_season_pack_blocking(settings_dict, logger_instance)
    else:
        logger_instance.info("🧪 Skipping creation of season pack blocking profile (preflight handles pack decisions)")

    def _normalize_title(t: str) -> str:
        if not t:
            return ""
        t_norm = unicodedata.normalize('NFKD', t).encode('ascii', 'ignore').decode('ascii')
        t_norm = t_norm.lower().strip()
        # Remove region markers in parentheses at end e.g. "The Office (US)" -> "the office"
        t_norm = re.sub(r"\s*\((us|uk|au|ca)\)$", "", t_norm)
        return re.sub(r"[^a-z0-9]+", " ", t_norm).strip()

    sonarr_url = get_setting("Sonarr", "url", settings_dict=settings_dict, default="http://localhost:8989")
    sonarr_api_key = get_setting("Sonarr", "api_key", settings_dict=settings_dict)
    if not sonarr_api_key:
        logger_instance.error("Sonarr API key not configured in settings")
        return {"success": False, "reason": "missing_api_key"}

    try:
        cleaned_title = tv_data.get("cleaned_title") or tv_data.get("title") or ""
        year = tv_data.get("year")
        tvdb_id = tv_data.get("tvdb_id")
        request_specificity = tv_data.get("request_specificity", "full_series")

        if not cleaned_title:
            return {"success": False, "reason": "missing_title"}

        headers = {'X-Api-Key': sonarr_api_key}
        quality_strategy = _determine_tv_quality_profile_by_year(int(year) if year else 0, logger_instance, settings_dict)

        async with aiohttp.ClientSession() as session:
            # Season pack monitoring is now handled by the preflight analyzer

            # --- Search Phase ---
            # Use flexible term: title + optional year
            search_terms = []
            base_term = cleaned_title
            if year:
                search_terms.append(f"{base_term} {year}")
            search_terms.append(base_term)
            # De-duplicate
            seen = set()
            ordered_terms = []
            for term in search_terms:
                if term not in seen:
                    ordered_terms.append(term)
                    seen.add(term)

            all_results = []
            for term in ordered_terms:
                logger_instance.info(f"Searching Sonarr for: {term}")
                async with session.get(f"{sonarr_url}/api/v3/series/lookup", params={"term": term}, headers=headers) as resp:
                    if resp.status != 200:
                        logger_instance.warning(f"Search term '{term}' failed with status {resp.status}")
                        continue
                    res = await resp.json()
                    if res:
                        all_results.extend(res)
                # Break early if we already have many results
                if len(all_results) >= 20:
                    break

            if not all_results:
                return {"success": False, "reason": "no_search_results"}

            # Deduplicate by tvdbId/titleSlug
            dedup = {}
            for r in all_results:
                key = (r.get("tvdbId"), r.get("titleSlug"))
                if key not in dedup:
                    dedup[key] = r
            search_results = list(dedup.values())

            norm_cleaned = _normalize_title(cleaned_title)
            requested_year = year
            best_match = None
            scored = []

            # Region marker heuristics to help distinguish remakes (US, UK, AU etc.)
            region_keywords = ["(us)", "(uk)", "(au)", "(ca)"]
            for r in search_results:
                score = 0
                title_raw = r.get("title", "")
                title_norm = _normalize_title(title_raw)
                result_year = r.get("year")

                # Strong TVDB ID match
                if tvdb_id and str(r.get("tvdbId")) == str(tvdb_id):
                    score += 500
                # Exact normalized title match
                if title_norm == norm_cleaned:
                    score += 150
                # Year weighting
                if requested_year and result_year == requested_year:
                    score += 300
                elif requested_year and result_year:
                    diff = abs(int(requested_year) - int(result_year))
                    score -= 80 + diff * 10  # strong penalty to push wrong-year versions down
                # Region keyword handling: reward when user supplied year typical for that region
                title_lower = title_raw.lower()
                if requested_year and any(k in title_lower for k in region_keywords):
                    # Light bonus; exact year already heavily weighted
                    score += 10
                # Favor shows with more seasons (originals) to disambiguate remakes with fewer seasons
                seasons = r.get("seasons", [])
                score += len(seasons) * 3
                scored.append((score, r))

            scored.sort(key=lambda x: x[0], reverse=True)
            if scored:
                if requested_year:
                    # Prefer highest scoring exact-year candidate if any
                    exact_years = [r for s, r in scored if r.get("year") == requested_year]
                    if exact_years:
                        best_match = exact_years[0]
                if not best_match:
                    best_match = scored[0][1]
            else:
                best_match = search_results[0]

            # Prepare alternative suggestions for logging/reporting
            alternatives = []
            for s, r in scored[1:4]:
                alternatives.append({
                    "title": r.get("title"),
                    "year": r.get("year"),
                    "tvdbId": r.get("tvdbId"),
                    "score": s
                })

            if requested_year and best_match.get('year') != requested_year:
                logger_instance.warning(
                    f"Year mismatch: requested {requested_year} but selected {best_match.get('title')} ({best_match.get('year')}). Alternatives: {alternatives}" if alternatives else \
                    f"Year mismatch: requested {requested_year} but selected {best_match.get('title')} ({best_match.get('year')})."
                )
            logger_instance.info(
                f"Selected TV show: {best_match.get('title')} ({best_match.get('year', 'Unknown')}) | tvdbId={best_match.get('tvdbId')}" +
                (f" | alternatives: {alternatives}" if alternatives else "")
            )

            plex_tv_path = get_path_setting("Paths", "plex_tv_directory", settings_dict=settings_dict)
            if not plex_tv_path:
                plex_tv_path = "E:/TV"  # fallback
            plex_tv_path_obj = Path(plex_tv_path)
            existing_tv_roots = []
            async with aiohttp.ClientSession() as tmp_sess:
                async with tmp_sess.get(f"{sonarr_url}/api/v3/rootfolder", headers=headers) as tv_rf_resp:
                    if tv_rf_resp.status == 200:
                        try:
                            existing_tv_roots = await tv_rf_resp.json()
                            logger_instance.debug(f"📁 Found {len(existing_tv_roots)} Sonarr root folders: {[rf.get('path') for rf in existing_tv_roots]}")
                        except Exception as rf_err:
                            logger_instance.warning(f"⚠️ Failed to parse root folders: {rf_err}")
                            existing_tv_roots = []
                    else:
                        logger_instance.warning(f"⚠️ Failed to fetch Sonarr root folders: {tv_rf_resp.status}")

            normalized_tv_desired = str(plex_tv_path_obj.resolve()) if plex_tv_path_obj.drive else str(plex_tv_path_obj)
            normalized_tv_desired = normalized_tv_desired.replace('/', '\\')
            logger_instance.debug(f"📁 Desired TV path: {normalized_tv_desired}")

            chosen_tv_root = None
            for rf in existing_tv_roots:
                rf_path = rf.get('path', '')
                logger_instance.debug(f"📁 Comparing: '{str(Path(rf_path).resolve()).lower()}' vs '{str(Path(normalized_tv_desired).resolve()).lower()}'")
                if str(Path(rf_path).resolve()).lower() == str(Path(normalized_tv_desired).resolve()).lower():
                    chosen_tv_root = rf_path
                    logger_instance.info(f"📁 Matched existing Sonarr root folder: {chosen_tv_root}")
                    break
                    chosen_tv_root = rf.get('path')
                    break
            if not chosen_tv_root:
                try:
                    plex_tv_path_obj.mkdir(parents=True, exist_ok=True)
                except Exception as mk_err:
                    logger_instance.warning(f"⚠️ Could not create desired TV root '{plex_tv_path_obj}': {mk_err}")
                if existing_tv_roots:
                    chosen_tv_root = existing_tv_roots[0].get('path')
                    logger_instance.info(f"🔁 Using existing Sonarr root folder: {chosen_tv_root}")
                else:
                    chosen_tv_root = normalized_tv_desired
                    logger_instance.info(f"🆕 Attempting to use unregistered TV root folder: {chosen_tv_root}")
            root_folder_path = chosen_tv_root.replace('/', '\\')

            profiles_to_add = quality_strategy["profiles"]
            logger_instance.info(f"📋 {quality_strategy['description']}")
            logger_instance.info(f"📺 Adding TV show to Sonarr with {len(profiles_to_add)} quality profile(s): {cleaned_title}")

            added_series = []

            # Check if series already exists in Sonarr; if so, adjust monitoring instead of re-adding
            existing_series = None
            async with session.get(f"{sonarr_url}/api/v3/series", headers=headers) as existing_resp:
                if existing_resp.status == 200:
                    try:
                        existing_list = await existing_resp.json()
                        existing_series = next((s for s in existing_list if str(s.get('tvdbId')) == str(best_match.get('tvdbId'))), None)
                    except Exception:
                        existing_series = None
            if existing_series:
                series_id = existing_series.get('id')
                logger_instance.info(f"🔁 Series already exists in Sonarr (ID {series_id}); applying specificity adjustments.")
                try:
                    if request_specificity in ["specific_episode", "specific_episodes"]:
                        # Handle single episode (back-compat path) or multiple episodes (preferred)
                        episodes_list = tv_data.get("episodes", [])
                        if episodes_list:
                            # New multiple episodes format
                            from _internal.src.intake_tv_orchestrator import configure_multiple_episodes as _cfg_multi
                            await _cfg_multi(session, sonarr_url, headers.get('X-Api-Key',''), series_id, episodes_list, logger_instance, settings_dict, disable_immediate_search=True)
                        else:
                            # Single-episode back-compat format
                            from _internal.src.intake_tv_orchestrator import configure_specific_episode as _cfg_ep
                            await _cfg_ep(session, sonarr_url, headers.get('X-Api-Key',''), series_id, tv_data.get("season"), tv_data.get("episode"), logger_instance, settings_dict, disable_immediate_search=True)
                    elif request_specificity == "multiple_episodes":
                        # Handle multiple episodes
                        episodes_list = tv_data.get("episodes", [])
                        await _configure_multiple_episodes_monitoring(session, sonarr_url, headers, series_id, episodes_list, logger_instance, settings_dict, disable_immediate_search=True)
                    elif request_specificity == "specific_season":
                        from _internal.src.intake_tv_orchestrator import configure_specific_season as _cfg_season
                        await _cfg_season(session, sonarr_url, headers.get('X-Api-Key',''), series_id, tv_data.get("season"), logger_instance, disable_immediate_search=True)
                    # Trigger enforcement (optional) and return success early
                    try:
                        max_episode_gb = float(get_setting("Sonarr", "max_episode_size_gb", settings_dict=settings_dict, default=40))
                        if max_episode_gb > 0:
                            await _enforce_episode_size_limits(
                                session,
                                sonarr_url,
                                headers,
                                series_id,
                                max_episode_gb,
                                request_specificity,
                                tv_data.get("season"),
                                tv_data.get("episode"),
                                logger_instance,
                                delay_seconds=float(get_setting("Sonarr", "episode_size_enforcement_delay_seconds", settings_dict=settings_dict, default=5))
                            )
                    except Exception:
                        pass
                    return {
                        "success": True,
                        "reason": "already_exists_adjusted",
                        "sonarr_series": [existing_series],
                        "sonarr_id": series_id,
                        "quality_strategy": quality_strategy["strategy"],
                        "profiles_added": 0,
                        "alternatives": alternatives
                    }
                except Exception as adjust_err:
                    logger_instance.error(f"Failed to adjust existing series monitoring: {adjust_err}")
                    # Fall through to attempt normal add if adjustment fails
            for idx, quality_profile_id in enumerate(profiles_to_add):
                if request_specificity in ["specific_episode", "specific_episodes", "multiple_episodes"]:
                    monitor_option = "none"  # Don't monitor anything initially for specific episodes
                    search_for_missing = False
                elif request_specificity == "specific_season":
                    monitor_option = "firstSeason"
                    search_for_missing = True
                elif request_specificity == "full_series":
                    # 🚨 CRITICAL FIX: For full series, only monitor first season for season-by-season progression
                    monitor_option = "firstSeason"  # Changed from "all" to "firstSeason"
                    search_for_missing = True
                else:
                    monitor_option = "firstSeason"  # Changed: Default to firstSeason to prevent mass downloads
                    search_for_missing = True

                # Language profile (configurable; default 1)
                try:
                    tv_language_profile_id = int(get_setting("Sonarr", "tv_language_profile_id", settings_dict=settings_dict, default=1))
                except Exception:
                    tv_language_profile_id = 1

                add_series_data = {
                    "title": best_match.get("title"),
                    "year": best_match.get("year"),
                    "tvdbId": best_match.get("tvdbId"),
                    "titleSlug": best_match.get("titleSlug", _normalize_title(best_match.get("title"))).replace(' ', '-'),
                    "images": best_match.get("images", []),
                    "seasons": best_match.get("seasons", []),
                    "qualityProfileId": quality_profile_id,
                    "languageProfileId": tv_language_profile_id,
                    "monitored": False,  # Start unmonitored - preflight analyzer will enable monitoring after analysis
                    "seasonFolder": True,
                    "rootFolderPath": root_folder_path,
                    "addOptions": {
                        "monitor": monitor_option,
                        "searchForMissingEpisodes": False,  # Disable automatic search - preflight handles this
                        "searchForCutoffUnmetEpisodes": False,
                        # FORCE INDIVIDUAL EPISODE SEARCH - Prevent season pack downloads
                        "ignoreEpisodesWithFiles": False,
                        "ignoreEpisodesWithoutFiles": False,
                        # Ensure no automatic season pack downloads
                        "searchForCutoffUnmetEpisodes": False
                    }
                }

                logger_instance.info(f"   📥 Adding with quality profile {quality_profile_id}...")
                async with session.post(f"{sonarr_url}/api/v3/series", headers=headers, json=add_series_data) as resp:
                    if resp.status in [200, 201]:
                        added_item = await resp.json()
                        added_series.append(added_item)
                        series_id = added_item.get('id')
                        logger_instance.info(f"   ✅ Added: {cleaned_title} (Series ID {series_id}, Profile {quality_profile_id})")
                        # Apply specificity adjustments for new series
                        if request_specificity in ["specific_episode", "specific_episodes"]:
                            # Handle single episode (back-compat path) or multiple episodes (preferred)
                            episodes_list = tv_data.get("episodes", [])
                            if episodes_list:
                                # New multiple episodes format
                                await _configure_multiple_episodes_monitoring(session, sonarr_url, headers, series_id, episodes_list, logger_instance, settings_dict, disable_immediate_search=True)
                            else:
                                # Single-episode back-compat format - extract from historical fields
                                season = tv_data.get("season") or tv_data.get("seasons", [None])[0]
                                episode = tv_data.get("episode") or (tv_data.get("episodes", [{}])[0].get("episode") if tv_data.get("episodes") else None)
                                from _internal.src.intake_tv_orchestrator import configure_specific_episode as _cfg_ep
                                await _cfg_ep(session, sonarr_url, headers.get('X-Api-Key',''), series_id, season, episode, logger_instance, settings_dict, disable_immediate_search=True)
                        elif request_specificity == "multiple_episodes":
                            # Handle multiple episodes - CREATE REQUEST CONTEXT
                            episodes_list = tv_data.get("episodes", [])

                            # Create request context for enhanced marker tracking
                            try:
                                from _internal.utils.request_context_manager import get_request_context_manager

                                # Determine season directories that need context
                                seasons_involved = list(set(ep.get('season') for ep in episodes_list))

                                for season_num in seasons_involved:
                                    # Get season path for context creation
                                    season_episodes = [ep for ep in episodes_list if ep.get('season') == season_num]

                                    # This path needs to be constructed based on your directory structure
                                    # Using workspace temp folder for context creation
                                    temp_context_path = Path("workspace/.temp_contexts")
                                    temp_context_path.mkdir(parents=True, exist_ok=True)
                                    season_path = temp_context_path / f"season_{season_num}_context"
                                    season_path.mkdir(exist_ok=True)

                                    context_mgr = get_request_context_manager(season_path)
                                    context_mgr.create_context({
                                        "request_type": "multiple_episodes",
                                        "original_request": tv_data.get("display_title", ""),
                                        "episodes": season_episodes,
                                        "show_title": tv_data.get("show_title", ""),
                                        "year": tv_data.get("year")
                                    })
                                    logger_instance.info(f"   📋 Created request context for Season {season_num}")
                            except Exception as context_err:
                                logger_instance.warning(f"   ⚠️ Failed to create request context: {context_err}")

                            await _configure_multiple_episodes_monitoring(session, sonarr_url, headers, series_id, episodes_list, logger_instance, settings_dict, disable_immediate_search=True)
                        elif request_specificity == "specific_season":
                            seasons_list = tv_data.get("seasons", [])
                            if seasons_list:
                                # Handle multiple seasons if provided
                                for season in seasons_list:
                                    from _internal.src.intake_tv_orchestrator import configure_specific_season as _cfg_season
                                    await _cfg_season(session, sonarr_url, headers.get('X-Api-Key',''), series_id, season, logger_instance, disable_immediate_search=True)
                            else:
                                # Single-season back-compat path
                                season = tv_data.get("season")
                                from _internal.src.intake_tv_orchestrator import configure_specific_season as _cfg_season
                                await _cfg_season(session, sonarr_url, headers.get('X-Api-Key',''), series_id, season, logger_instance, disable_immediate_search=True)
                    else:
                        text = await resp.text()
                        if "exists" in text.lower():
                            logger_instance.warning(f"   ⚠️ Series already exists for profile {quality_profile_id}: {text}")
                        else:
                            logger_instance.error(f"   ❌ Failed to add profile {quality_profile_id}: {resp.status} - {text}")

            if not added_series:
                return {"success": False, "reason": "all_profiles_failed"}

            # Episode size enforcement (placeholder for future queue pruning). Just log config now.
            try:
                max_episode_gb = float(get_setting("Sonarr", "max_episode_size_gb", settings_dict=settings_dict, default=40))
                logger_instance.info(f"📏 Max episode size threshold configured: {max_episode_gb} GB")
                # Run enforcement only if threshold positive
                if max_episode_gb > 0:
                    # Use first added series id
                    series_id_for_enforcement = added_series[0].get('id')
                    if series_id_for_enforcement:
                        await _enforce_episode_size_limits(
                            session,
                            sonarr_url,
                            headers,
                            series_id_for_enforcement,
                            max_episode_gb,
                            request_specificity,
                            tv_data.get("season"),
                            tv_data.get("episode"),
                            logger_instance,
                            delay_seconds=float(get_setting("Sonarr", "episode_size_enforcement_delay_seconds", settings_dict=settings_dict, default=5))
                        )
            except Exception:
                logger_instance.info("📏 Max episode size threshold defaulting to 40 GB (setting parse error)")

            # 🔄 DYNAMIC SEASON PROGRESSION: Initialize progression state for full series requests
            if request_specificity == "full_series" and added_series:
                try:
                    # Load opt-in file
                    sequential_file = Path("sequential_series.txt")
                    opt_in_titles = set()
                    if sequential_file.exists():
                        try:
                            lines = sequential_file.read_text(encoding='utf-8').strip().split('\n')
                            opt_in_titles = {line.strip() for line in lines if line.strip() and not line.startswith('#')}
                        except Exception as e:
                            logger_instance.warning(f"Failed to load sequential_series.txt: {e}")

                    if opt_in_titles:
                        series_display = f"{cleaned_title} ({year})" if year else cleaned_title
                        if series_display in opt_in_titles:
                            logger_instance.info(f"🔄 [SEQUENTIAL] Initializing progression for: {series_display}")

                            # Initialize dynamic progression manager
                            from _internal.utils.tv_season_progression_manager import TVSeasonProgressionManager
                            progression_mgr = TVSeasonProgressionManager(logger_instance)

                            # Get the first added series
                            series_obj = added_series[0]
                            series_id = series_obj.get('id')

                            # Get episodes for this series to count seasons
                            async with session.get(f"{sonarr_url}/api/v3/episode?seriesId={series_id}", headers=headers) as ep_resp:
                                if ep_resp.status == 200:
                                    episodes = await ep_resp.json()

                                    # Initialize progression state
                                    key = await progression_mgr.init_if_needed(series_obj, episodes, opt_in_titles)

                                    if key:
                                        # Enforce only Season 1 monitoring
                                        await progression_mgr.enforce_monitoring(key, sonarr_url, sonarr_api_key)
                                        logger_instance.info(f"🔄 [SEQUENTIAL] Progression initialized for {series_display}")
                                    else:
                                        logger_instance.warning(f"🔄 [SEQUENTIAL] Failed to initialize progression for {series_display}")
                                else:
                                    logger_instance.warning(f"🔄 [SEQUENTIAL] Failed to get episodes for progression init: HTTP {ep_resp.status}")
                        else:
                            logger_instance.info(f"🔄 [SEQUENTIAL] Not opted-in for progression: {series_display}")
                    else:
                        logger_instance.debug("🔄 [SEQUENTIAL] No series opted-in for progression")

                except Exception as progression_err:
                    logger_instance.warning(f"🔄 [SEQUENTIAL] Failed to initialize progression: {progression_err}")

            # Season pack monitoring has been removed in favor of the preflight analyzer
            # Season pack evaluation is handled by the preflight analyzer
            logger_instance.info("✅ Season pack evaluation handled by preflight analyzer")

            return {
                "success": True,
                "reason": "successfully_added",
                "sonarr_series": added_series,
                "sonarr_id": added_series[0].get("id"),
                "quality_strategy": quality_strategy["strategy"],
                "profiles_added": len(added_series),
                "alternatives": alternatives
            }

    except Exception as e:
        logger_instance.error(f"Exception in Sonarr TV add for {cleaned_title}: {e}", exc_info=True)
        return {"success": False, "reason": f"exception_{e}"}
    finally:
        # Clean up monitoring task
        if 'monitoring_task' in locals() and monitoring_task and not monitoring_task.done():
            monitoring_task.cancel()
            try:
                await monitoring_task
            except asyncio.CancelledError:
                pass


# Modern intake uses direct Radarr/Sonarr APIs; manual NZB scraping via browser automation has been fully removed.

# --- MCP-Enhanced Main Stage Function (called by Orchestrator) ---
async def run_intake_and_search_stage(settings_dict: dict, logger_instance: logging.Logger, mcp_manager=None) -> bool:
    """
    MCP-Enhanced Stage 01: Intelligent intake with sequential thinking, memory learning, and error handling.
    NOW SUPPORTS BOTH MOVIES AND TV SHOWS with sophisticated year-based quality strategies.

    This stage operates purely with SQLite database - no legacy format conversion.
    Returns boolean success/failure instead of movies list.

    DUAL CONTENT PROCESSING:
    - Movies: Processed from new_movie_requests_file → Radarr with year-based quality (≤2009→1080p, 2010-2015→both, 2016+→4K)
    - TV Shows: Processed from new_tv_requests_file → Sonarr with TV-specific quality (≤2005→720p/1080p, 2006-2015→1080p, 2016-2020→both, 2021+→4K)

    MCP Enhancements:
    - Sequential task breakdown for complex operations
    - Memory-based learning from previous intake patterns
    - Intelligent error handling with GitHub issue creation
    - Performance tracking and optimization suggestions
    - SQLite-based state management for reliability

    Args:
        settings_dict: Pipeline settings
        logger_instance: Logger instance
        mcp_manager: MCP manager instance

    Returns:
        bool: True if stage completed successfully, False otherwise
    """
    logger_instance.info("===== Starting MCP-Enhanced Stage 01: Intake and NZB Search =====")

    # Initialize filesystem-first state manager
    workspace_root = Path.cwd()
    filesystem_manager = FilesystemFirstStateManager(workspace_root)
    metadata_db = MetadataOnlyDatabase(workspace_root)

    # Discover current movies by scanning filesystem
    logger_instance.info("Discovering movies by scanning filesystem...")
    movies_by_stage = filesystem_manager.discover_movies_by_stage()
    logger_instance.info(f"Found movies in {len(movies_by_stage)} stages")

    # Initialize MCP services for this stage
    sequential_service = mcp_manager.services.get('sequential_thinking') if mcp_manager else None
    memory_service = mcp_manager.services.get('memory_manager') if mcp_manager else None
    github_service = mcp_manager.services.get('github_integration') if mcp_manager else None

    # Create sequential thinking task for the entire intake process
    intake_task_id = None
    if sequential_service:
        intake_task_id = await sequential_service.create_task(
            task_type="intake_and_nzb_search",
            movie_id="batch_intake",
            pipeline_stage="01_intake_and_nzb_search",
            custom_steps=[
                "Load intake preferences from memory",
                "Process new movie requests from file",
                "Process new TV show requests from file",
                "Fetch metadata with intelligent caching",
                "Apply year-based quality strategies (movies + TV)",
                "Add content to Radarr/Sonarr with optimization",
                "Learn from metadata patterns",
                "Update memory with processing results"
            ]
        )
        if intake_task_id:
            await sequential_service.start_task(intake_task_id)
            logger_instance.info(f"Created MCP sequential task: {intake_task_id}")

    try:
        start_time = time.time()  # Track processing time for MCP analytics

        # --- 1. Load Intake Preferences from Memory ---
        intake_preferences = {}
        if memory_service:
            intake_preferences = await memory_service.retrieve_memory(
                category="intake_preferences",
                key="user_defaults"
            ) or {}
            logger_instance.info(f"Loaded intake preferences: {len(intake_preferences)} settings")

        # --- 2. Intake New Movie Requests from File ---
        raw_path = get_path_setting("General", "new_movie_requests_file", settings_dict=settings_dict)
        new_requests_file_path = Path(raw_path) if raw_path else None

        if new_requests_file_path and new_requests_file_path.exists():
            logger_instance.info(f"Processing new movie requests from: {new_requests_file_path}")
        elif not new_requests_file_path:
            logger_instance.warning("No new movie requests file configured in settings")
        elif not new_requests_file_path.exists():
            logger_instance.warning(f"New movie requests file not found: {new_requests_file_path}")

        # --- 2b. Intake New TV Show Requests from File ---
        tv_raw_path = get_path_setting("General", "new_tv_requests_file", settings_dict=settings_dict)
        new_tv_requests_file_path = Path(tv_raw_path) if tv_raw_path else None

        if new_tv_requests_file_path and new_tv_requests_file_path.exists():
            logger_instance.info(f"Processing new TV show requests from: {new_tv_requests_file_path}")
        elif not new_tv_requests_file_path:
            logger_instance.warning("No new TV show requests file configured in settings")
        elif not new_tv_requests_file_path.exists():
            logger_instance.warning(f"New TV show requests file not found: {new_tv_requests_file_path}")

        # Process movies first
        if new_requests_file_path and new_requests_file_path.exists():
            remaining_lines_in_intake_file = []
            try:
                with open(new_requests_file_path, 'r', encoding='utf-8') as f:
                    raw_request_lines = [line.strip() for line in f if line.strip() and not line.startswith('#')]

                for raw_title_input in raw_request_lines:
                    # Check for duplicates in metadata database (filesystem-first)
                    existing_movies = metadata_db.get_all_movies()
                    is_duplicate = any(movie.get("title", "").lower() == raw_title_input.lower() for movie in existing_movies)
                    if is_duplicate:
                        logger_instance.info(f"Skipping duplicate request from intake file: '{raw_title_input}'")
                        continue

                    logger_instance.info(f"Fetching metadata for new request: '{raw_title_input}'...")

                    # MCP Enhancement: Check memory for cached metadata
                    cached_metadata = None
                    if memory_service:
                        cached_metadata = await memory_service.retrieve_memory(
                            category="metadata_cache",
                            key=f"title_{raw_title_input.lower().replace(' ', '_')}"
                        )
                        if cached_metadata:
                            logger_instance.info(f"Found cached metadata for '{raw_title_input}'")

                    # Fetch metadata with intelligent caching
                    if cached_metadata and cached_metadata.get('timestamp'):
                        # Use cached metadata if it's recent (within 7 days)
                        from datetime import datetime, timedelta
                        cache_time = datetime.fromisoformat(cached_metadata['timestamp'])
                        if datetime.now(timezone.utc) - cache_time < timedelta(days=7):
                            metadata_result = cached_metadata['data']
                            logger_instance.info("Using cached metadata (fresh)")
                        else:
                            candidate = fetch_movie_metadata_for_intake(raw_title_input, settings_dict=settings_dict)
                            metadata_result = await candidate if asyncio.iscoroutine(candidate) else candidate
                            # Update cache with fresh data
                            if memory_service and metadata_result:
                                await memory_service.store_memory(
                                    category="metadata_cache",
                                    key=f"title_{raw_title_input.lower().replace(' ', '_')}",
                                    value={
                                        'data': metadata_result,
                                        'timestamp': datetime.now(timezone.utc).isoformat()
                                    },
                                    tags=["metadata", "cache", "intake"]
                                )
                    else:
                        candidate = fetch_movie_metadata_for_intake(raw_title_input, settings_dict=settings_dict)
                        metadata_result = await candidate if asyncio.iscoroutine(candidate) else candidate
                        # Cache new metadata
                        if memory_service and metadata_result:
                            await memory_service.store_memory(
                                category="metadata_cache",
                                key=f"title_{raw_title_input.lower().replace(' ', '_')}",
                                value={
                                    'data': metadata_result,
                                    'timestamp': datetime.now(timezone.utc).isoformat()
                                },
                                tags=["metadata", "cache", "intake"]
                            )

                    # FILESYSTEM-FIRST: Create movie entry in metadata-only database
                    # Use title-year identifier to match filesystem folder names
                    if metadata_result and not metadata_result.get("error"):
                        cleaned_title = metadata_result.get("title", raw_title_input)
                        year = metadata_result.get("year")
                        unique_id = f"{cleaned_title} ({year})" if year else cleaned_title

                        # Check if movie already exists in metadata database
                        existing_movie = metadata_db.get_movie_metadata(unique_id)
                        if existing_movie:
                            logger_instance.info(f"Movie already exists in metadata database: {unique_id}")
                            continue

                        # Store metadata in database (NO STATUS - filesystem manages state)
                        success = metadata_db.save_movie_metadata(
                            unique_id=unique_id,
                            title=cleaned_title,
                            year=year,
                            tmdb_id=metadata_result.get("tmdb_id"),
                            imdb_id=metadata_result.get("imdb_id"),
                            metadata={
                                'raw_title_input': raw_title_input,
                                'request_timestamp': datetime.now(timezone.utc).isoformat(),
                                'mcp_task_id': intake_task_id,
                                'metadata_details': metadata_result
                            }
                        )

                        if success:
                            logger_instance.info(f"Stored metadata for '{raw_title_input}': {unique_id}")

                            # Add movie to Radarr using orchestrator
                            logger_instance.info(f"🎬 Adding movie to Radarr: {unique_id}")
                            from _internal.src.intake_movie_orchestrator import add_movie as _orchestrate_movie
                            radarr_result = await _orchestrate_movie(metadata_result, settings_dict, logger_instance)

                            if radarr_result.get("success"):
                                logger_instance.info(f"✅ Successfully added movie to Radarr: {unique_id}")
                                logger_instance.info(f"📋 Quality strategy: {radarr_result.get('quality_strategy', 'unknown')}")
                                logger_instance.info(f"📊 Profiles added: {radarr_result.get('profiles_added', 1)}")

                                # === Interactive Choice: Preflight Analysis vs Radarr Auto-Grab (Movies) ===
                                if radarr_result.get('radarr_id'):
                                    print(f"\n🤔 Download Strategy Choice for: {metadata_result.get('cleaned_title') or raw_title_input}")
                                    print("Choose how you want to handle downloads for this movie:")
                                    print("1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)")
                                    print("2. ⚡ Radarr Auto-Grab - Let Radarr immediately search and grab based on quality profiles")
                                    print("3. ⏭️  Skip - Add to Radarr but don't start any downloads yet")

                                    while True:
                                        try:
                                            choice = input("Enter your choice (1/2/3): ").strip()
                                            if choice in ['1', '2', '3']:
                                                break
                                            else:
                                                print("❌ Please enter 1, 2, or 3")
                                        except (EOFError, KeyboardInterrupt):
                                            print("\n⚠️ Defaulting to Preflight Analysis")
                                            choice = '1'
                                            break

                                    radarr_url = get_setting('Radarr', 'url', settings_dict=settings_dict, default='http://localhost:7878')
                                    radarr_api_key = get_setting('Radarr', 'api_key', settings_dict=settings_dict)
                                    movie_id = radarr_result.get('radarr_id')

                                    if choice == '1':
                                        # --- MOVIE PREFLIGHT (Radarr) ---
                                        try:
                                            from preflight_analyzer.movie_preflight_selector import preflight_movie  # type: ignore
                                            import json as _json
                                            decisions_dir = Path('workspace/preflight_decisions')
                                            decisions_dir.mkdir(parents=True, exist_ok=True)
                                            
                                            # FIXED: Use consistent filename instead of timestamp
                                            # This prevents multiple JSON files per movie
                                            sanitized_title = metadata_result.get('title', f'movie_{movie_id}').replace(':', '').replace('/', '_').replace('\\', '_')
                                            out_path = decisions_dir / f"{sanitized_title}.json"
                                            
                                            # Check if decision already exists
                                            if out_path.exists():
                                                logger_instance.info(f"✅ Found existing preflight decision: {out_path}")
                                                with open(out_path, 'r', encoding='utf-8') as df:
                                                    preflight_result = _json.load(df)
                                                logger_instance.info(f"📝 Loaded existing movie preflight decision for: {sanitized_title}")
                                            else:
                                                cfg_path = Path('config/preflight_config.json')
                                                servers_cfg = Path('config/preflight_config.json')  # reuse if unified
                                                logger_instance.info(f"🧪 Running movie preflight analysis for Radarr ID {movie_id}")
                                                preflight_result = await preflight_movie(
                                                    config_path=cfg_path,
                                                    servers_config_path=servers_cfg,
                                                    radarr_movie_id=movie_id,
                                                    radarr_url=radarr_url,
                                                    radarr_api_key=radarr_api_key,
                                                    manual_search=False,
                                                    max_candidates=15,
                                                    sample_cap=500,
                                                    settings_dict=settings_dict,  # Pass settings for dynamic scanning
                                                )
                                                
                                                # Save new decision
                                                with open(out_path, 'w', encoding='utf-8') as df:
                                                    _json.dump(preflight_result, df, indent=2)
                                                logger_instance.info(f"📝 Movie preflight decision saved: {out_path}")

                                            best = preflight_result.get('best')
                                            if best:
                                                title = best.get('title') or 'Unknown'
                                                qname = (((best.get('quality') or {}).get('quality') or {}).get('name') if isinstance(best.get('quality'), dict) else None) or best.get('qualityName') or '?'
                                                size_b = best.get('size') or 0
                                                size_gb = (size_b / (1024**3)) if size_b else 0
                                                decision = best.get('decision') or 'UNKNOWN'
                                                runtime_min = None
                                                try:
                                                    # Try common keys from metadata
                                                    runtime_min = metadata_result.get('runtime') or metadata_result.get('movie_runtime') or metadata_result.get('duration_minutes')
                                                    if isinstance(runtime_min, str) and runtime_min.isdigit():
                                                        runtime_min = int(runtime_min)
                                                except Exception:
                                                    runtime_min = None

                                                print("\n🔬 Preflight selection (best candidate):")
                                                print(f"   • {qname}  |  {size_gb:.2f} GB  |  decision={decision}")
                                                if runtime_min:
                                                    print(f"   • Runtime: {runtime_min} min")

                                                # Mirror TV prompt extras: language/subtitle and sanity checks
                                                try:
                                                    pref_lang = get_setting('Radarr', 'preferred_audio_languages', settings_dict=settings_dict, default='')
                                                    pref_subs = get_setting('Radarr', 'preferred_subtitle_languages', settings_dict=settings_dict, default='')
                                                    if pref_lang or pref_subs:
                                                        print("   • Preferences:")
                                                        if pref_lang:
                                                            print(f"     - Audio languages: {pref_lang}")
                                                        if pref_subs:
                                                            print(f"     - Subtitle languages: {pref_subs}")
                                                except Exception:
                                                    pass

                                                # Size/runtime sanity checks (heuristic)
                                                try:
                                                    ql = (qname or '').lower()
                                                    is_4k = any(x in ql for x in ('2160', '4k', 'uhd'))
                                                    is_1080 = '1080' in ql
                                                    gb_per_hour = (size_gb / (runtime_min/60.0)) if (runtime_min and runtime_min > 0) else None
                                                    suspicious = False
                                                    notes = []
                                                    if is_4k:
                                                        if size_gb and size_gb < 8:
                                                            suspicious = True
                                                            notes.append('4K size < 8 GB')
                                                        if gb_per_hour and gb_per_hour < 6.0:
                                                            suspicious = True
                                                            notes.append(f'4K {gb_per_hour:.1f} GB/h below expected')
                                                        if 'hdr' in (title or '').lower() and size_gb and size_gb < 8:
                                                            suspicious = True
                                                            notes.append('HDR tag but very small — possible upscale')
                                                    elif is_1080:
                                                        if size_gb and size_gb < 2.0:
                                                            suspicious = True
                                                            notes.append('1080p size < 2 GB')
                                                        if gb_per_hour and gb_per_hour < 1.5:
                                                            suspicious = True
                                                            notes.append(f'1080p {gb_per_hour:.1f} GB/h below expected')
                                                    if suspicious:
                                                        print("   ⚠️ Sanity: Candidate looks suspiciously small for its quality")
                                                        for n in notes:
                                                            print(f"      - {n}")
                                                except Exception:
                                                    pass

                                            acceptable = bool(best and best.get('decision') in ('ACCEPT','RISKY_LOW_PARITY'))
                                            if acceptable:
                                                try:
                                                    confirm = input("Proceed to enable monitoring and trigger Radarr search now? [Y/n]: ").strip().lower()
                                                except (EOFError, KeyboardInterrupt):
                                                    confirm = 'y'
                                                if confirm in ('', 'y', 'yes'):
                                                    try:
                                                        import aiohttp
                                                        headers = {"X-Api-Key": radarr_api_key}
                                                        async with aiohttp.ClientSession() as monitor_session:
                                                            async with monitor_session.get(f"{radarr_url}/api/v3/movie/{movie_id}", headers=headers) as get_resp:
                                                                if get_resp.status == 200:
                                                                    movie_data = await get_resp.json()
                                                                    movie_data["monitored"] = True
                                                                    async with monitor_session.put(f"{radarr_url}/api/v3/movie/{movie_id}", headers=headers, json=movie_data) as monitor_resp:
                                                                        if monitor_resp.status in [200, 202]:
                                                                            async with monitor_session.post(f"{radarr_url}/api/v3/command", headers=headers, json={"name": "MoviesSearch", "movieIds": [movie_id]}) as search_resp:
                                                                                if search_resp.status in [200, 201, 202]:
                                                                                    print("✅ Movie monitoring enabled - Radarr will search and grab releases")
                                                                                else:
                                                                                    logger_instance.warning(f"⚠️ Failed to trigger movie search: {search_resp.status}")
                                                                        else:
                                                                            logger_instance.warning(f"⚠️ Failed to enable movie monitoring: {monitor_resp.status}")
                                                                else:
                                                                    logger_instance.warning(f"⚠️ Failed to get movie data for monitoring: {get_resp.status}")
                                                    except Exception as monitor_err:
                                                        logger_instance.warning(f"⚠️ Failed to enable monitoring/search for movie {movie_id}: {monitor_err}")
                                                else:
                                                    print("⏭️ Skipping monitoring/search per user choice.")
                                            else:
                                                logger_instance.warning("⚠️ No acceptable movie release found in preflight; leaving Radarr to handle future grabs.")
                                        except Exception as mp_err:
                                            logger_instance.error(f"Movie preflight failed: {mp_err}")

                                    elif choice == '2':
                                        # --- RADARR AUTO-GRAB ---
                                        try:
                                            import aiohttp
                                            headers = {"X-Api-Key": radarr_api_key}
                                            async with aiohttp.ClientSession() as s:
                                                async with s.get(f"{radarr_url}/api/v3/movie/{movie_id}", headers=headers) as get_resp:
                                                    if get_resp.status == 200:
                                                        movie_data = await get_resp.json()
                                                        movie_data["monitored"] = True
                                                        async with s.put(f"{radarr_url}/api/v3/movie/{movie_id}", headers=headers, json=movie_data) as put_resp:
                                                            if put_resp.status in [200, 202]:
                                                                async with s.post(f"{radarr_url}/api/v3/command", headers=headers, json={"name": "MoviesSearch", "movieIds": [movie_id]}) as cmd_resp:
                                                                    if cmd_resp.status in [200, 201, 202]:
                                                                        print("⚡ Radarr auto-grab triggered for this movie")
                                                                    else:
                                                                        logger_instance.warning(f"⚠️ Auto-grab search failed: {cmd_resp.status}")
                                                            else:
                                                                logger_instance.warning(f"⚠️ Could not set monitored on movie: {put_resp.status}")
                                                    else:
                                                        logger_instance.warning(f"⚠️ Could not load movie: {get_resp.status}")
                                        except Exception as e:
                                            logger_instance.warning(f"⚠️ Radarr auto-grab failed: {e}")

                                    else:
                                        print("⏭️ Skipping immediate downloads for this movie.")
                                else:
                                    logger_instance.info("Missing Radarr ID; skipping movie preflight/auto-grab prompts.")
                            else:
                                logger_instance.warning(f"⚠️ Failed to add movie to Radarr: {radarr_result.get('reason', 'unknown')}")
                        else:
                            logger_instance.warning(f"Failed to store metadata for '{raw_title_input}'")
                    else:
                        # No metadata found - log error but don't store in database
                        error_msg = metadata_result.get("error", "Unknown metadata lookup error.") if metadata_result else "No metadata result"
                        logger_instance.error(f"Metadata lookup failed for '{raw_title_input}': {error_msg}")
                        remaining_lines_in_intake_file.append(raw_title_input)

                # Update or clear the intake file
                with open(new_requests_file_path, 'w', encoding='utf-8') as f:
                    if remaining_lines_in_intake_file:
                        f.write('\n'.join(remaining_lines_in_intake_file))
                        logger_instance.info(f"Updated '{new_requests_file_path}' with titles that failed metadata lookup.")
                    else:
                        logger_instance.info(f"All requests from '{new_requests_file_path}' processed; file cleared.")
            except Exception as e:
                logger_instance.error(f"Error processing intake file '{new_requests_file_path}': {e}", exc_info=True)

        # Process TV shows second
        if new_tv_requests_file_path and new_tv_requests_file_path.exists():
            remaining_tv_lines_in_intake_file = []
            try:
                with open(new_tv_requests_file_path, 'r', encoding='utf-8') as f:
                    raw_tv_request_lines = [line.strip() for line in f if line.strip() and not line.startswith('#')]

                for raw_tv_title_input in raw_tv_request_lines:
                    # Check for duplicates in metadata database (filesystem-first)
                    existing_tv_shows = metadata_db.get_all_movies()  # Using same database for TV shows
                    is_tv_duplicate = any(tv_show.get("title", "").lower() == raw_tv_title_input.lower() for tv_show in existing_tv_shows)
                    if is_tv_duplicate:
                        logger_instance.info(f"Skipping duplicate TV show request from intake file: '{raw_tv_title_input}'")
                        continue

                    logger_instance.info(f"📺 Fetching TV show metadata for new request: '{raw_tv_title_input}'...")

                    # MCP Enhancement: Check memory for cached TV metadata
                    cached_tv_metadata = None
                    if memory_service:
                        cached_tv_metadata = await memory_service.retrieve_memory(
                            category="tv_metadata_cache",
                            key=f"tv_title_{raw_tv_title_input.lower().replace(' ', '_')}"
                        )
                        if cached_tv_metadata:
                            logger_instance.info(f"Found cached TV metadata for '{raw_tv_title_input}'")

                    # Fetch TV metadata with intelligent caching
                    if cached_tv_metadata and cached_tv_metadata.get('timestamp'):
                        # Use cached metadata if it's recent (within 7 days)
                        from datetime import datetime, timedelta
                        cache_time = datetime.fromisoformat(cached_tv_metadata['timestamp'])
                        if datetime.now(timezone.utc) - cache_time < timedelta(days=7):
                            tv_metadata_result = cached_tv_metadata['data']
                            logger_instance.info("Using cached TV metadata (fresh)")
                        else:
                            tv_metadata_result = await fetch_tv_metadata_for_intake(raw_tv_title_input, settings_dict=settings_dict)
                            # Update cache with fresh TV data
                            if memory_service and tv_metadata_result:
                                await memory_service.store_memory(
                                    category="tv_metadata_cache",
                                    key=f"tv_title_{raw_tv_title_input.lower().replace(' ', '_')}",
                                    value={
                                        'data': tv_metadata_result,
                                        'timestamp': datetime.now(timezone.utc).isoformat()
                                    },
                                    tags=["tv_metadata", "cache", "intake"]
                                )
                    else:
                        tv_metadata_result = await fetch_tv_metadata_for_intake(raw_tv_title_input, settings_dict=settings_dict)
                        # Cache new TV metadata
                        if memory_service and tv_metadata_result:
                            await memory_service.store_memory(
                                category="tv_metadata_cache",
                                key=f"tv_title_{raw_tv_title_input.lower().replace(' ', '_')}",
                                value={
                                    'data': tv_metadata_result,
                                    'timestamp': datetime.now(timezone.utc).isoformat()
                                },
                                tags=["tv_metadata", "cache", "intake"]
                            )

                    # FILESYSTEM-FIRST: Create TV show entry in metadata-only database
                    # Use title-year identifier to match filesystem folder names
                    if tv_metadata_result and not tv_metadata_result.get("error"):
                        cleaned_tv_title = tv_metadata_result.get("title", raw_tv_title_input)
                        tv_year = tv_metadata_result.get("year")
                        tv_unique_id = f"{cleaned_tv_title} ({tv_year})" if tv_year else cleaned_tv_title

                        # Check if TV show already exists in metadata database
                        existing_tv_show = metadata_db.get_movie_metadata(tv_unique_id)  # Using same database
                        if existing_tv_show:
                            logger_instance.info(f"TV show already exists in metadata database: {tv_unique_id}")
                            continue

                        # Store TV metadata in database (NO STATUS - filesystem manages state)
                        tv_success = metadata_db.save_movie_metadata(  # Using same function for TV shows
                            unique_id=tv_unique_id,
                            title=cleaned_tv_title,
                            year=tv_year,
                            tmdb_id=tv_metadata_result.get("tvdb_id"),  # TV shows use TVDB instead of TMDB
                            imdb_id=tv_metadata_result.get("imdb_id"),
                            metadata={
                                'raw_title_input': raw_tv_title_input,
                                'request_timestamp': datetime.now(timezone.utc).isoformat(),
                                'mcp_task_id': intake_task_id,
                                'metadata_details': tv_metadata_result,
                                'content_type': 'tv_show'  # Mark as TV show
                            }
                        )

                        if tv_success:
                            logger_instance.info(f"📺 Stored TV metadata for '{raw_tv_title_input}': {tv_unique_id}")

                            # Add TV show to Sonarr using orchestrator
                            logger_instance.info(f"📺 Adding TV show to Sonarr: {tv_unique_id}")
                            from _internal.src.intake_tv_orchestrator import add_tv_show as _orchestrate_tv
                            sonarr_result = await _orchestrate_tv(tv_metadata_result, settings_dict, logger_instance)

                            if sonarr_result.get("success"):
                                logger_instance.info(f"✅ Successfully added TV show to Sonarr: {tv_unique_id}")
                                logger_instance.info(f"📋 Quality strategy: {sonarr_result.get('quality_strategy', 'unknown')}")
                                logger_instance.info(f"📊 Profiles added: {sonarr_result.get('profiles_added', 1)}")
                            else:
                                logger_instance.warning(f"⚠️ Failed to add TV show to Sonarr: {sonarr_result.get('reason', 'unknown')}")
                        else:
                            logger_instance.warning(f"Failed to store TV metadata for '{raw_tv_title_input}'")
                    else:
                        # No TV metadata found - log error but don't store in database
                        tv_error_msg = tv_metadata_result.get("error", "Unknown TV metadata lookup error.") if tv_metadata_result else "No TV metadata result"
                        logger_instance.error(f"📺 TV metadata lookup failed for '{raw_tv_title_input}': {tv_error_msg}")
                        remaining_tv_lines_in_intake_file.append(raw_tv_title_input)

                # Update or clear the TV intake file
                with open(new_tv_requests_file_path, 'w', encoding='utf-8') as f:
                    if remaining_tv_lines_in_intake_file:
                        f.write('\n'.join(remaining_tv_lines_in_intake_file))
                        logger_instance.info(f"📺 Updated '{new_tv_requests_file_path}' with TV titles that failed metadata lookup.")
                    else:
                        logger_instance.info(f"📺 All TV requests from '{new_tv_requests_file_path}' processed; file cleared.")
            except Exception as e:
                logger_instance.error(f"📺 Error processing TV intake file '{new_tv_requests_file_path}': {e}", exc_info=True)

        # --- 3. Learn from Metadata Patterns ---
        if memory_service:
            # Store successful metadata patterns for learning
            await memory_service.store_memory(
                category="metadata_patterns",
                key=f"success_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                value={
                    "stage": "intake_and_search",
                    "success": True,
                    "processing_time": time.time() - start_time if 'start_time' in locals() else 0
                },
                tags=["metadata", "pattern", "success"]
            )

        # --- 4. Modern Radarr API Integration (replaces NZB scraping) ---
        # FILESYSTEM-FIRST: Script 1 focuses only on metadata and Radarr integration
        # NO status management - Radarr handles downloads, Script 2 discovers completions

        # NOTE: In filesystem-first architecture, Script 1 doesn't track "pending_nzb_search"
        # Instead, it just processes the requests and adds to Radarr
        # Radarr handles the download state, and Script 2 discovers completed downloads

        logger_instance.info("Radarr integration: Movies added to Radarr will be tracked by Radarr's internal state")
        logger_instance.info("Download completion will be discovered by Script 2's filesystem scanning")

        # FILESYSTEM-FIRST: Script 1 has completed its core function
        # - Processed new movie requests from intake file
        # - Fetched metadata for each movie
        # - Stored metadata in database
        # - Added movies to Radarr for automatic downloading
        #
        # Next steps happen automatically:
        # - Radarr manages download state internally
        # - Script 2 will discover completed downloads via filesystem scanning
        # - No status tracking needed in Script 1 database

        logger_instance.info("✅ Script 1 completed: All new movie and TV show requests processed")
        logger_instance.info("🎬 Movies added to Radarr with year-based quality strategies")
        logger_instance.info("📺 TV shows added to Sonarr with TV-specific quality strategies")
        logger_instance.info("📁 Download monitoring will be handled by Script 2's filesystem scanning")

        return True

    except Exception as e:
        logger_instance.error(f"Critical error in intake and NZB search: {e}")
        if sequential_service:
            await sequential_service.fail_task(intake_task_id, f"Critical error: {str(e)}")
        return False
    finally:
        # FILESYSTEM-FIRST: Clean up resources
        if metadata_db and hasattr(metadata_db, 'close'):
            metadata_db.close()
        logger_instance.info("===== Finished Filesystem-First Stage 01: Intake and Metadata Storage =====")

        return True


def display_interactive_menu():
    """
    Display the main interactive menu for content type selection.

    Returns:
        str: Selected content type ('movies', 'tv_shows', 'both', 'quit')
    """
    print(f"\n{'='*60}")
    print(f"🎬📺 PlexMovieAutomator - Interactive Content Selection")
    print(f"{'='*60}")
    print(f"\nWhat type of content would you like to process?")
    print(f"  1. Movies only")
    print(f"  2. TV Shows only")
    print(f"  3. Both Movies and TV Shows")
    print(f"  4. Quit")

    while True:
        try:
            choice = input(f"\nEnter your choice [1-4]: ").strip()

            if choice == '1':
                return 'movies'
            elif choice == '2':
                return 'tv_shows'
            elif choice == '3':
                return 'both'
            elif choice == '4':
                return 'quit'
            else:
                print(f"Please enter a number between 1 and 4")

        except KeyboardInterrupt:
            print(f"\n👋 Exiting...")
            return 'quit'


def display_content_selection(content_list: List[str], content_type: str) -> List[int]:
    """
    Display content items for interactive selection.

    Args:
        content_list: List of content titles
        content_type: 'movies' or 'tv_shows'

    Returns:
        List of selected indices (0-based)
    """
    if not content_list:
        print(f"📭 No {content_type} found in requests file")
        return []

    content_name = "Movies" if content_type == 'movies' else "TV Shows"
    emoji = "🎬" if content_type == 'movies' else "📺"

    print(f"\n{'='*60}")
    print(f"{emoji} {content_name} Available for Processing:")
    print(f"{'='*60}")

    for i, title in enumerate(content_list, 1):
        print(f"  {i:2d}. {title}")

    print(f"\n📝 Selection Options:")
    print(f"  • Single: Enter number (e.g., '3')")
    print(f"  • Multiple: Enter comma-separated numbers (e.g., '1,3,5')")
    print(f"  • All: Enter 'all' or 'a'")
    print(f"  • None: Enter 'none' or 'n' to skip")
    print(f"  • Quit: Enter 'quit' or 'q'")

    while True:
        try:
            selection = input(f"\nSelect {content_type} to process: ").strip().lower()

            if selection in ['quit', 'q']:
                return []
            elif selection in ['none', 'n']:
                return []
            elif selection in ['all', 'a']:
                return list(range(len(content_list)))
            else:
                # Parse comma-separated numbers
                try:
                    selected_numbers = [int(x.strip()) for x in selection.split(',')]
                    selected_indices = []

                    # Validate all numbers are in range
                    for num in selected_numbers:
                        if 1 <= num <= len(content_list):
                            selected_indices.append(num - 1)  # Convert to 0-based
                        else:
                            print(f"⚠️ Number {num} is out of range (1-{len(content_list)})")
                            break
                    else:
                        # All numbers were valid
                        if selected_indices:
                            # Show what was selected
                            selected_titles = [content_list[i] for i in selected_indices]
                            print(f"✅ Selected {len(selected_titles)} {content_type}:")
                            for i, title in enumerate(selected_titles, 1):
                                print(f"    {i}. {title}")

                            # Confirm selection
                            confirm = input(f"\nProceed with these selections? [y/N]: ").strip().lower()
                            if confirm in ['y', 'yes']:
                                return selected_indices
                            else:
                                print(f"Selection cancelled, please choose again...")
                                continue
                        else:
                            print(f"No valid selections made")
                            continue

                except ValueError:
                    print(f"⚠️ Invalid input. Please enter numbers separated by commas")
                    continue

        except KeyboardInterrupt:
            print(f"\n👋 Selection cancelled...")
            return []


def read_content_requests(content_type: str, settings_dict: dict) -> List[str]:
    """
    Read content requests from appropriate file.

    Args:
        content_type: 'movies' or 'tv_shows'
        settings_dict: Settings dictionary

    Returns:
        List of content titles from file
    """
    if content_type == 'movies':
        file_key = "new_movie_requests_file"
        default_file = "new_movie_requests.txt"
    else:  # tv_shows
        file_key = "new_tv_requests_file"
        default_file = "new_tv_requests.txt"

    requests_file_path = get_path_setting("General", file_key, settings_dict=settings_dict)
    if not requests_file_path:
        requests_file_path = Path(default_file)
    elif isinstance(requests_file_path, str):
        requests_file_path = Path(requests_file_path)

    if not requests_file_path.exists():
        print(f"⚠️ {content_type.title()} requests file not found: {requests_file_path}")
        return []

    try:
        with open(requests_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content_list = [line.strip() for line in f if line.strip() and not line.startswith('#')]

        print(f"📁 Loaded {len(content_list)} {content_type} from {requests_file_path}")
        return content_list

    except Exception as e:
        print(f"❌ Error reading {content_type} requests file: {e}")
        return []


async def process_selected_movies(selected_movies: List[str], settings_dict: dict, logger_instance, telemetry_integrator=None, max_search_candidates=50, quality_fallback_enabled=True) -> int:
    """
    Process selected movies through metadata fetch and Radarr addition.

    Args:
        selected_movies: List of selected movie titles
        settings_dict: Settings dictionary
        logger_instance: Logger instance
        telemetry_integrator: Optional telemetry integrator for real-time monitoring
        max_search_candidates: Maximum number of release candidates to analyze
        quality_fallback_enabled: Whether to enable quality fallback (4K→1080p)

    Returns:
        Number of successfully processed movies
    """
    if not selected_movies:
        return 0

    print(f"\n🎬 Processing {len(selected_movies)} selected movies...")
    print(f"{'='*60}")

    success_count = 0
    workspace_root = Path.cwd()
    metadata_db = MetadataOnlyDatabase(workspace_root)

    try:
        for i, raw_title in enumerate(selected_movies, 1):
            print(f"\n📍 Progress: {i}/{len(selected_movies)}")
            print(f"🎬 Processing: {raw_title}")
            # Fixed malformed f-string causing syntax issues and potential silent failures
            logger_instance.info(f"Processing movie: {raw_title}")

            # Fetch metadata
            try:
                # Support both async and (unexpected) sync implementations to avoid 'object dict can't be used in await expression'
                metadata_candidate = fetch_movie_metadata_for_intake(raw_title, settings_dict)
                if asyncio.iscoroutine(metadata_candidate):
                    metadata = await metadata_candidate
                else:
                    metadata = metadata_candidate

                if metadata and metadata.get('success'):
                    print(f"✅ Found metadata: {metadata.get('cleaned_title')} ({metadata.get('year')})")
                    logger_instance.info(f"Successfully found metadata for: {metadata.get('cleaned_title')} ({metadata.get('year')})")

                    # Add to Radarr
                    movie_data = {
                        "cleaned_title": metadata.get('cleaned_title'),
                        "year": metadata.get('year'),
                        "tmdb_id": metadata.get('tmdb_id')
                    }

                    from _internal.src.intake_movie_orchestrator import add_movie as _orchestrate_movie
                    result = await _orchestrate_movie(movie_data, settings_dict, logger_instance)

                    if result.get('success'):
                        # NEW: Use telemetry tracking instead of static messages
                        await track_movie_success_with_telemetry(result, metadata, telemetry_integrator, logger_instance)
                        success_count += 1
                        # === Interactive Choice: Preflight Analysis vs Radarr Auto-Grab (Movies) ===
                        try:
                            radarr_movie_id = result.get('radarr_id')
                            if radarr_movie_id:
                                print(f"\n🤔 Download Strategy Choice for: {metadata.get('cleaned_title') or raw_title}")
                                print("Choose how you want to handle downloads for this movie:")
                                print("1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)")
                                print("2. ⚡ Radarr Auto-Grab - Let Radarr immediately search and grab based on quality profiles")
                                print("3. ⏭️  Skip - Add to Radarr but don't start any downloads yet")

                                while True:
                                    try:
                                        choice = input("Enter your choice (1/2/3): ").strip()
                                        if choice in ['1', '2', '3']:
                                            break
                                        else:
                                            print("❌ Please enter 1, 2, or 3")
                                    except (EOFError, KeyboardInterrupt):
                                        print("\n⚠️ Defaulting to Preflight Analysis")
                                        choice = '1'
                                        break

                                radarr_url = get_setting('Radarr', 'url', settings_dict=settings_dict, default='http://localhost:7878')
                                radarr_api_key = get_setting('Radarr', 'api_key', settings_dict=settings_dict)

                                if choice == '1':
                                    # --- MOVIE PREFLIGHT (Radarr) ---
                                    try:
                                        from preflight_analyzer.movie_preflight_selector import preflight_movie  # type: ignore
                                        # Use globally imported datetime/timezone to avoid attribute issues
                                        from datetime import datetime, timezone
                                        import json as _json
                                        decisions_dir = Path('workspace/preflight_decisions')
                                        decisions_dir.mkdir(parents=True, exist_ok=True)
                                        cfg_path = Path('config/preflight_config.json')
                                        servers_cfg = Path('preflight_analyzer/sample_servers.json')  # use dedicated servers config
                                        # Get the quality strategy that was used for this movie
                                        movie_year = metadata.get('year') or metadata.get('release_year') or 1999
                                        from _internal.src.quality_strategy import determine_movie_quality_profile_by_year
                                        quality_strategy = determine_movie_quality_profile_by_year(movie_year, logger_instance, settings_dict)

                                        # FIXED: Use consistent filename instead of timestamp to prevent multiple JSON files
                                        cleaned_title = metadata.get('cleaned_title') or 'Unknown'
                                        # Remove invalid filename characters (Windows doesn't allow : < > " | ? * \)
                                        movie_title = cleaned_title.replace(' ', '_').replace(':', '').replace('<', '').replace('>', '').replace('"', '').replace('|', '').replace('?', '').replace('*', '').replace('\\', '').replace('/', '')
                                        out_path = decisions_dir / f"{movie_title}.json"
                                        print(f"🐛 DEBUG: sanitized movie_title = '{movie_title}'")
                                        print(f"🐛 DEBUG: final out_path = '{out_path}'")
                                        
                                        # Check if decision already exists
                                        if out_path.exists():
                                            logger_instance.info(f"✅ Found existing preflight decision: {out_path}")
                                            with open(out_path, 'r', encoding='utf-8') as df:
                                                preflight_result = _json.load(df)
                                            logger_instance.info(f"📝 Loaded existing movie preflight decision for: {movie_title}")
                                            print(f"✅ Using existing preflight decision for: {cleaned_title}")
                                        else:
                                            logger_instance.info(f"🧪 Running movie preflight analysis for Radarr ID {radarr_movie_id}")
                                            logger_instance.info(f"🎯 Quality constraint: {quality_strategy.get('description', 'Unknown')}")
                                            print("🔍 Fetching movie releases from Radarr...")
                                            print(f"🎯 Quality filter: {quality_strategy.get('description', 'Unknown')}")

                                            try:
                                                # Match TV behavior: run to completion without a global timeout
                                                preflight_result = await preflight_movie(
                                                    config_path=cfg_path,
                                                    servers_config_path=servers_cfg,
                                                    radarr_movie_id=radarr_movie_id,
                                                    radarr_url=radarr_url,
                                                    radarr_api_key=radarr_api_key,
                                                    manual_search=False,
                                                    max_candidates=max_search_candidates,  # Configurable search limit
                                                    sample_cap=320,     # Same as TV shows
                                                    quality_strategy=quality_strategy,  # Pass quality constraints
                                                    settings_dict=settings_dict,  # Pass settings for dynamic scanning
                                                )
                                            except Exception as pf_err:
                                                # Align with TV: log and continue, avoid premature fallback where possible
                                                logger_instance.error(f"Preflight movie analysis failed: {pf_err}")
                                                print("⚠️ Preflight analysis failed - no release selected")
                                                break
                                            
                                            # Save new decision
                                            with open(out_path, 'w', encoding='utf-8') as df:
                                                _json.dump(preflight_result, df, indent=2)
                                            logger_instance.info(f"📝 Movie preflight decision saved: {out_path}")

                                        # Show preflight stats (same format as TV shows)
                                        stats = preflight_result.get('stats', {})
                                        total_candidates = stats.get('total_candidates', 0)
                                        valid_analyses = stats.get('valid_analyses', 0)
                                        acceptable = stats.get('acceptable', 0)
                                        errors = stats.get('errors', 0)
                                        cache_hits = stats.get('cache_hits', 0)
                                        cache_hit_rate = stats.get('cache_hit_rate', 0.0)

                                        # Show cache performance if applicable (same as TV)
                                        if cache_hits > 0:
                                            print(f"💾 Cache performance: {cache_hits}/{total_candidates} hits ({cache_hit_rate:.1%}) - saved significant analysis time!")

                                        print(f"📊 Preflight Results: {valid_analyses} analyzed, {acceptable} acceptable, {errors} errors")

                                        # Process results exactly like TV shows
                                        if preflight_result and preflight_result.get('success'):
                                            acceptable_candidates = preflight_result.get('acceptable_candidates', [])
                                            all_candidates = preflight_result.get('candidates', [])

                                            print(f"\n📊 Combined Results: 1 total movie analyzed, {len(acceptable_candidates)} acceptable releases found")

                                            # Show all acceptable candidates (exactly like TV shows)
                                            if acceptable_candidates:
                                                print(f"\n🔬 Movie Preflight Analysis Results:")
                                                for i, candidate in enumerate(acceptable_candidates, 1):
                                                    cand_title = candidate.get('title', 'Unknown')
                                                    cand_size_b = candidate.get('size', 0)
                                                    cand_size_gb = (cand_size_b / (1024**3)) if cand_size_b else 0
                                                    cand_decision = candidate.get('decision', 'UNKNOWN')
                                                    cand_risk = candidate.get('risk_score', 0.0)
                                                    cand_missing = candidate.get('probe_missing_ratio', 0.0)
                                                    print(f"   #{i}. 🎬 {cand_title}")
                                                    print(f"       💾 Size: {cand_size_gb:.2f} GB ({cand_size_b:,} bytes)")
                                                    print(f"       ⚡ Risk: {cand_risk:.4f} | Missing: {cand_missing:.1%} | Decision: {cand_decision}")

                                            best = preflight_result.get('best')
                                            if best:
                                                title = best.get('title') or 'Unknown'
                                                size_b = best.get('size') or 0
                                                size_gb = (size_b / (1024**3)) if size_b else 0
                                                decision = best.get('decision') or 'UNKNOWN'
                                                risk_score = best.get('risk_score', 0.0)
                                                missing_ratio = best.get('probe_missing_ratio', 0.0)

                                                # Get runtime from metadata
                                                runtime_min = None
                                                try:
                                                    runtime_min = metadata.get('runtime') or metadata.get('movie_runtime') or metadata.get('duration_minutes')
                                                    if isinstance(runtime_min, str) and runtime_min.isdigit():
                                                        runtime_min = int(runtime_min)
                                                except Exception:
                                                    runtime_min = None

                                            print("\n🔬 Preflight selection (best candidate):")
                                            print(f"   • {size_gb:.2f} GB  |  {decision}  |  risk: {risk_score:.4f}  |  missing: {missing_ratio:.1%}")
                                            if runtime_min:
                                                print(f"   • Runtime: {runtime_min} min")
                                            print(f"   • Release: {title}")

                                            # Mirror TV prompt extras: language/subtitle and sanity checks
                                            try:
                                                pref_lang = get_setting('Radarr', 'preferred_audio_languages', settings_dict=settings_dict, default='')
                                                pref_subs = get_setting('Radarr', 'preferred_subtitle_languages', settings_dict=settings_dict, default='')
                                                if pref_lang or pref_subs:
                                                    print("   • Preferences:")
                                                    if pref_lang:
                                                        print(f"     - Audio languages: {pref_lang}")
                                                    if pref_subs:
                                                        print(f"     - Subtitle languages: {pref_subs}")
                                            except Exception:
                                                pass

                                            # Size/runtime sanity checks (heuristic) - use title instead of qname
                                            try:
                                                title_lower = (title or '').lower()
                                                is_4k = any(x in title_lower for x in ('2160', '4k', 'uhd'))
                                                is_1080 = '1080' in title_lower
                                                gb_per_hour = (size_gb / (runtime_min/60.0)) if (runtime_min and runtime_min > 0) else None
                                                suspicious = False
                                                notes = []
                                                if is_4k:
                                                    if size_gb and size_gb < 8:
                                                        suspicious = True
                                                        notes.append('4K size < 8 GB')
                                                    if gb_per_hour and gb_per_hour < 6.0:
                                                        suspicious = True
                                                        notes.append(f'4K {gb_per_hour:.1f} GB/h below expected')
                                                    if 'hdr' in (title or '').lower() and size_gb and size_gb < 8:
                                                        suspicious = True
                                                        notes.append('HDR tag but very small — possible upscale')
                                                elif is_1080:
                                                    if size_gb and size_gb < 2.0:
                                                        suspicious = True
                                                        notes.append('1080p size < 2 GB')
                                                    if gb_per_hour and gb_per_hour < 1.5:
                                                        suspicious = True
                                                        notes.append(f'1080p {gb_per_hour:.1f} GB/h below expected')
                                                if suspicious:
                                                    print("   ⚠️ Sanity: Candidate looks suspiciously small for its quality")
                                                    for n in notes:
                                                        print(f"      - {n}")
                                            except Exception:
                                                pass

                                            # Immediate download like TV shows
                                            try:
                                                confirm = input("Proceed to download this specific release now? [Y/n]: ").strip().lower()
                                            except (EOFError, KeyboardInterrupt):
                                                confirm = 'y'
                                            if confirm in ('', 'y', 'yes'):
                                                try:
                                                    # DEBUG: Check all conditions for candidate storage
                                                    print(f"🔍 DEBUG: Checking candidate storage conditions...")
                                                    print(f"   telemetry_integrator: {telemetry_integrator is not None}")
                                                    print(f"   telemetry_integrator.telemetry: {telemetry_integrator.telemetry is not None if telemetry_integrator else False}")
                                                    print(f"   best: {best is not None}")
                                                    print(f"   all_candidates: {all_candidates is not None and len(all_candidates) > 0 if all_candidates else False}")
                                                    
                                                    # CRITICAL FIX: Store candidate information for fallback BEFORE download
                                                    if telemetry_integrator and telemetry_integrator.telemetry and best and all_candidates:
                                                        print(f"💾 Storing candidate information for fallback system...")
                                                        try:
                                                            # FIXED: Find the index of the selected candidate in the ACCEPTABLE candidates list
                                                            # The fallback system uses acceptable_candidates, not all_candidates
                                                            acceptable_candidates = preflight_result.get('acceptable_candidates', [])
                                                            selected_index = None
                                                            for i, candidate in enumerate(acceptable_candidates):
                                                                if candidate.get('guid') == best.get('guid'):
                                                                    selected_index = i
                                                                    break
                                                            
                                                            if selected_index is None:
                                                                # Fallback: try to find in all_candidates and map to acceptable
                                                                print(f"⚠️ Selected candidate not found in acceptable list, using fallback search")
                                                                for i, candidate in enumerate(all_candidates):
                                                                    if candidate.get('guid') == best.get('guid'):
                                                                        # This candidate exists but isn't in acceptable list
                                                                        selected_index = 0  # Default to first acceptable candidate
                                                                        break
                                                            
                                                            # FIXED: The system's original recommendation is the same as the selected candidate
                                                            # since the preflight system automatically chose the "best" candidate
                                                            original_system_recommendation_index = 0  # TEMPORARY: Use old behavior for testing
                                                            
                                                            # Store using the correct method signature
                                                            telemetry_integrator.telemetry.store_movie_candidate_info(
                                                                radarr_id=radarr_movie_id,
                                                                candidate_details=best,  # The selected candidate
                                                                user_selection_index=selected_index,  # Index in ACCEPTABLE candidates
                                                                original_system_recommendation_index=original_system_recommendation_index  # Actual system recommendation
                                                            )
                                                            print(f"✅ Stored candidate #{selected_index + 1 if selected_index is not None else '?'} of {len(acceptable_candidates)} acceptable candidates for fallback")
                                                        except Exception as storage_err:
                                                            logger_instance.warning(f"⚠️ Failed to store candidate info: {storage_err}")
                                                            print(f"⚠️ Failed to store candidate info: {storage_err}")
                                                    else:
                                                        print(f"❌ Candidate storage skipped - one or more conditions failed")
                                                    
                                                    # Direct release grab (same as TV shows)
                                                    from preflight_analyzer.radarr_client import grab_release as _grab_movie
                                                    import aiohttp
                                                    headers = {"X-Api-Key": radarr_api_key}

                                                    # Enable monitoring first
                                                    async with aiohttp.ClientSession() as grab_session:
                                                        async with grab_session.get(f"{radarr_url}/api/v3/movie/{radarr_movie_id}", headers=headers) as get_resp:
                                                            if get_resp.status == 200:
                                                                movie_data = await get_resp.json()
                                                                movie_data["monitored"] = True
                                                                async with grab_session.put(f"{radarr_url}/api/v3/movie/{radarr_movie_id}", headers=headers, json=movie_data) as monitor_resp:
                                                                    if monitor_resp.status in [200, 202]:
                                                                        # Use new cache sync download function
                                                                        download_success, download_msg = await download_movie_with_cache_sync(
                                                                            session=grab_session,
                                                                            radarr_url=radarr_url,
                                                                            headers=headers,
                                                                            movie_id=radarr_movie_id,
                                                                            movie_data=best,
                                                                            logger_instance=logger_instance
                                                                        )
                                                                        
                                                                        if download_success:
                                                                            print(f"✅ Movie download started: {title}")
                                                                            print(f"   📥 Size: {size_gb:.2f} GB | Risk: {risk_score:.4f} | Decision: {decision}")
                                                                            
                                                                            # Update telemetry with actual NZB filename after preflight selection
                                                                            if telemetry_integrator and telemetry_integrator.telemetry:
                                                                                job_id = f"radarr_{radarr_movie_id}"
                                                                                if telemetry_integrator.update_nzb_filename(job_id, title):
                                                                                    logger_instance.info(f"📝 Updated telemetry with NZB filename: {title}")
                                                                        else:
                                                                            logger_instance.warning(f"⚠️ Movie download failed: {download_msg}")
                                                                    else:
                                                                        logger_instance.warning(f"⚠️ Failed to enable movie monitoring: {monitor_resp.status}")
                                                            else:
                                                                logger_instance.warning(f"⚠️ Failed to get movie data: {get_resp.status}")
                                                except Exception as grab_err:
                                                    logger_instance.warning(f"⚠️ Failed to grab movie release: {grab_err}")
                                            else:
                                                # Offer remaining acceptable options to choose from
                                                alt_candidates = [c for c in preflight_result.get('acceptable_candidates', []) if c.get('guid') != (best or {}).get('guid')]
                                                if not alt_candidates:
                                                    # No other acceptable releases in current quality - check for quality fallback
                                                    strategy_type = quality_strategy.get('strategy', '') if 'quality_strategy' in locals() else ''
                                                    if strategy_type == '4k_only' and quality_fallback_enabled:
                                                        print("🔄 No other 4K options available. Attempting fallback to 1080p...")
                                                        
                                                        try:
                                                            # CRITICAL FIX: Remove movie from Radarr and re-add with 1080p profile
                                                            print(f"🔄 Removing movie from Radarr (ID: {radarr_movie_id}) to switch from 4K to 1080p profile...")
                                                            
                                                            # Store original ID for telemetry update
                                                            original_radarr_movie_id = radarr_movie_id
                                                            
                                                            import aiohttp
                                                            headers = {"X-Api-Key": radarr_api_key}
                                                            async with aiohttp.ClientSession() as cleanup_session:
                                                                # Remove the movie from Radarr
                                                                async with cleanup_session.delete(f"{radarr_url}/api/v3/movie/{radarr_movie_id}?deleteFiles=false&addImportExclusion=false", headers=headers) as delete_resp:
                                                                    if delete_resp.status in [200, 204]:
                                                                        print("✅ Successfully removed movie from Radarr")
                                                                        
                                                                        # Re-add movie with 1080p profile
                                                                        print("🔄 Re-adding movie with 1080p profile...")
                                                                        
                                                                        # Get 1080p quality profile ID
                                                                        async with cleanup_session.get(f"{radarr_url}/api/v3/qualityprofile", headers=headers) as profile_resp:
                                                                            if profile_resp.status == 200:
                                                                                profiles = await profile_resp.json()
                                                                                hd_1080p_profile_id = None
                                                                                for profile in profiles:
                                                                                    profile_name = profile.get('name', '').lower()
                                                                                    if 'hd-1080p' in profile_name or '1080p' in profile_name:
                                                                                        hd_1080p_profile_id = profile['id']
                                                                                        logger_instance.info(f"Found 1080p profile: {profile['name']} (ID: {hd_1080p_profile_id})")
                                                                                        break
                                                                                
                                                                                if hd_1080p_profile_id:
                                                                                    # Get root folder
                                                                                    async with cleanup_session.get(f"{radarr_url}/api/v3/rootfolder", headers=headers) as root_resp:
                                                                                        if root_resp.status == 200:
                                                                                            root_folders = await root_resp.json()
                                                                                            root_path = root_folders[0]['path'] if root_folders else 'E:\\'
                                                                                            
                                                                                            # Re-add movie with 1080p profile
                                                                                            readd_payload = {
                                                                                                "title": metadata['cleaned_title'],
                                                                                                "year": metadata['year'],
                                                                                                "tmdbId": metadata['tmdb_id'],
                                                                                                "imdbId": metadata.get('imdb_id'),
                                                                                                "qualityProfileId": hd_1080p_profile_id,
                                                                                                "rootFolderPath": root_path,
                                                                                                "monitored": True,
                                                                                                "searchForMovie": False,
                                                                                                "minimumAvailability": "released"
                                                                                            }
                                                                                            
                                                                                            async with cleanup_session.post(f"{radarr_url}/api/v3/movie", headers=headers, json=readd_payload) as readd_resp:
                                                                                                if readd_resp.status in [200, 201]:
                                                                                                    readd_data = await readd_resp.json()
                                                                                                    new_radarr_movie_id = readd_data['id']
                                                                                                    print(f"✅ Successfully re-added movie with 1080p profile (New ID: {new_radarr_movie_id})")
                                                                                                    
                                                                                                    # Update the radarr_movie_id for the rest of the process
                                                                                                    radarr_movie_id = new_radarr_movie_id
                                                                                                    
                                                                                                    # Update telemetry job tracking with new ID
                                                                                                    if telemetry_integrator:
                                                                                                        try:
                                                                                                            # Use the stored original ID
                                                                                                            old_job_id = f"radarr_{original_radarr_movie_id}"
                                                                                                            new_job_id = f"radarr_{new_radarr_movie_id}"
                                                                                                            
                                                                                                            # Remove old job and add new one
                                                                                                            if old_job_id in telemetry_integrator.telemetry.active_jobs:
                                                                                                                old_job = telemetry_integrator.telemetry.active_jobs.pop(old_job_id)
                                                                                                                old_job.job_id = new_job_id
                                                                                                                old_job.radarr_id = new_radarr_movie_id
                                                                                                                old_job.is_fallback_workflow = True  # Mark as fallback workflow for extended verification
                                                                                                                telemetry_integrator.telemetry.active_jobs[new_job_id] = old_job
                                                                                                                logger_instance.info(f"🔄 Updated telemetry job: {old_job_id} → {new_job_id}")
                                                                                                            
                                                                                                            # CRITICAL FIX: Update movie candidate info for fallback system
                                                                                                            if hasattr(telemetry_integrator.telemetry, 'update_movie_radarr_id'):
                                                                                                                if telemetry_integrator.telemetry.update_movie_radarr_id(original_radarr_movie_id, new_radarr_movie_id):
                                                                                                                    logger_instance.info(f"🔄 Updated candidate info for fallback: {original_radarr_movie_id} → {new_radarr_movie_id}")
                                                                                                        except Exception as telemetry_err:
                                                                                                            logger_instance.warning(f"⚠️ Failed to update telemetry job ID: {telemetry_err}")
                                                                                                else:
                                                                                                    error_text = await readd_resp.text()
                                                                                                    logger_instance.error(f"⚠️ Failed to re-add movie: {readd_resp.status} - {error_text}")
                                                                                                    continue  # Skip this movie
                                                                                        else:
                                                                                            logger_instance.error("⚠️ Failed to get root folder")
                                                                                            continue
                                                                                else:
                                                                                    logger_instance.error("⚠️ Could not find 1080p quality profile")
                                                                                    continue
                                                                            else:
                                                                                logger_instance.error(f"⚠️ Failed to get quality profiles: {profile_resp.status}")
                                                                                continue
                                                                    else:
                                                                        error_text = await delete_resp.text()
                                                                        logger_instance.error(f"⚠️ Failed to remove movie: {delete_resp.status} - {error_text}")
                                                                        continue
                                                            
                                                            # Run preflight analysis with 1080p fallback strategy
                                                            fallback_quality_strategy = {
                                                                'strategy': '1080p_only',
                                                                'description': 'Fallback: 1080p only - high quality preferred',
                                                                'profiles': [hd_1080p_profile_id] if 'hd_1080p_profile_id' in locals() and hd_1080p_profile_id else [3]
                                                            }
                                                            
                                                            fallback_result = await preflight_movie(
                                                                config_path=cfg_path,
                                                                servers_config_path=servers_cfg,
                                                                radarr_movie_id=radarr_movie_id,
                                                                radarr_url=radarr_url,
                                                                radarr_api_key=radarr_api_key,
                                                                manual_search=False,
                                                                max_candidates=max_search_candidates,
                                                                sample_cap=320,
                                                                quality_strategy=fallback_quality_strategy,
                                                                settings_dict=settings_dict,  # Pass settings for dynamic scanning
                                                            )
                                                            
                                                            # SAVE FALLBACK DECISION: Update the decision file with fallback analysis
                                                            try:
                                                                # Use the same sanitized title as the original decision
                                                                cleaned_title = metadata.get('cleaned_title') or 'Unknown'
                                                                movie_title = cleaned_title.replace(' ', '_').replace(':', '').replace('<', '').replace('>', '').replace('"', '').replace('|', '').replace('?', '').replace('*', '').replace('\\', '').replace('/', '')
                                                                out_path = decisions_dir / f"{movie_title}.json"
                                                                
                                                                # Update the decision file with fallback analysis
                                                                with open(out_path, 'w', encoding='utf-8') as df:
                                                                    _json.dump(fallback_result, df, indent=2)
                                                                logger_instance.info(f"📝 Updated decision file with 1080p fallback analysis: {out_path}")
                                                                print(f"📝 Saved 1080p fallback analysis to decision file")
                                                            except Exception as save_err:
                                                                logger_instance.warning(f"⚠️ Failed to save fallback decision: {save_err}")
                                                            
                                                            fallback_acceptable = fallback_result.get('acceptable_candidates', [])
                                                            if fallback_acceptable:
                                                                print(f"✅ Found {len(fallback_acceptable)} 1080p alternatives!")
                                                                
                                                                # Get the best 1080p candidate
                                                                best_1080p = fallback_result.get('best')
                                                                if best_1080p:
                                                                    best_title = best_1080p.get('title', 'Unknown')
                                                                    best_size_gb = (best_1080p.get('size', 0) or 0) / (1024**3)
                                                                    best_risk = best_1080p.get('risk_score', 0.0) or 0.0
                                                                    best_missing = best_1080p.get('probe_missing_ratio', 0.0) or 0.0
                                                                    
                                                                    print(f"\n🏆 Best 1080p candidate:")
                                                                    print(f"   • {best_size_gb:.2f} GB | ACCEPT | risk: {best_risk:.4f} | missing: {best_missing:.1%}")
                                                                    print(f"   • Release: {best_title}")
                                                                    
                                                                    try:
                                                                        proceed_choice = input("Proceed to download this 1080p release? [Y/n]: ").strip().lower()
                                                                    except (EOFError, KeyboardInterrupt):
                                                                        proceed_choice = 'n'
                                                                    
                                                                    if proceed_choice in ['', 'y', 'yes']:
                                                                        # User accepted the best 1080p candidate
                                                                        selected_1080p = best_1080p
                                                                        print(f"🎯 Downloading best 1080p option: {best_title}")
                                                                    else:
                                                                        # User declined - show all alternatives
                                                                        print("\n🔽 1080p Fallback Options:")
                                                                        for idx, cand in enumerate(fallback_acceptable, start=1):
                                                                            c_title = cand.get('title') or 'Unknown'
                                                                            c_size_gb = (cand.get('size', 0) or 0) / (1024**3)
                                                                            c_risk = cand.get('risk_score', 0.0) or 0.0
                                                                            c_missing = cand.get('probe_missing_ratio', 0.0) or 0.0
                                                                            print(f"   {idx}. 🎬 {c_title}")
                                                                            print(f"      💾 {c_size_gb:.2f} GB | ⚡ risk: {c_risk:.4f} | missing: {c_missing:.1%}")
                                                                        
                                                                        try:
                                                                            fallback_choice = input("Enter number to download a 1080p option, or press Enter to skip: ").strip()
                                                                        except (EOFError, KeyboardInterrupt):
                                                                            fallback_choice = ''
                                                                        
                                                                        if fallback_choice.isdigit():
                                                                            sel_idx = int(fallback_choice)
                                                                            if 1 <= sel_idx <= len(fallback_acceptable):
                                                                                selected_1080p = fallback_acceptable[sel_idx - 1]
                                                                                sel_title = selected_1080p.get('title') or 'Selected 1080p Release'
                                                                                print(f"🎯 Selected 1080p option: {sel_title}")
                                                                            else:
                                                                                print("ℹ️ Invalid selection. Skipping download.")
                                                                                selected_1080p = None
                                                                        else:
                                                                            print("ℹ️ No 1080p option selected. Skipping download.")
                                                                            selected_1080p = None
                                                                else:
                                                                    # No best candidate, show all options
                                                                    selected_1080p = None
                                                                    print("\n🔽 1080p Fallback Options:")
                                                                    for idx, cand in enumerate(fallback_acceptable, start=1):
                                                                        c_title = cand.get('title') or 'Unknown'
                                                                        c_size_gb = (cand.get('size', 0) or 0) / (1024**3)
                                                                        c_risk = cand.get('risk_score', 0.0) or 0.0
                                                                        c_missing = cand.get('probe_missing_ratio', 0.0) or 0.0
                                                                        print(f"   {idx}. 🎬 {c_title}")
                                                                        print(f"      💾 {c_size_gb:.2f} GB | ⚡ risk: {c_risk:.4f} | missing: {c_missing:.1%}")
                                                                    
                                                                    try:
                                                                        fallback_choice = input("Enter number to download a 1080p option, or press Enter to skip: ").strip()
                                                                    except (EOFError, KeyboardInterrupt):
                                                                        fallback_choice = ''
                                                                    
                                                                    if fallback_choice.isdigit():
                                                                        sel_idx = int(fallback_choice)
                                                                        if 1 <= sel_idx <= len(fallback_acceptable):
                                                                            selected_1080p = fallback_acceptable[sel_idx - 1]
                                                                            sel_title = selected_1080p.get('title') or 'Selected 1080p Release'
                                                                            print(f"🎯 Selected 1080p option: {sel_title}")
                                                                        else:
                                                                            print("ℹ️ Invalid selection. Skipping download.")
                                                                            selected_1080p = None
                                                                    else:
                                                                        print("ℹ️ No 1080p option selected. Skipping download.")
                                                                        selected_1080p = None
                                                                
                                                                # Download the selected 1080p option
                                                                if selected_1080p:
                                                                    # User selected a 1080p fallback option - implement download
                                                                    sel_guid = selected_1080p.get('guid')
                                                                    sel_indexer = selected_1080p.get('indexer')
                                                                    sel_title = selected_1080p.get('title') or 'Selected 1080p Release'
                                                                    sel_size_b = int(selected_1080p.get('size') or 0)
                                                                    sel_size_gb = sel_size_b / (1024**3) if sel_size_b else 0.0
                                                                    sel_risk = float(selected_1080p.get('risk_score', 0.0) or 0.0)
                                                                    sel_missing = float(selected_1080p.get('probe_missing_ratio', 0.0) or 0.0)
                                                                    
                                                                    print(f"🎯 Selected 1080p fallback: {sel_title}")
                                                                    print(f"   💾 {sel_size_gb:.2f} GB | ⚡ risk: {sel_risk:.4f} | missing: {sel_missing:.1%}")
                                                                    
                                                                    # Attempt direct grab for selected 1080p candidate
                                                                    try:
                                                                        import aiohttp
                                                                        headers = {"X-Api-Key": radarr_api_key}
                                                                        async with aiohttp.ClientSession() as fallback_session:
                                                                            # Ensure monitored for 1080p quality
                                                                            async with fallback_session.get(f"{radarr_url}/api/v3/movie/{radarr_movie_id}", headers=headers) as get_resp:
                                                                                if get_resp.status == 200:
                                                                                    movie_data = await get_resp.json()
                                                                                    movie_data['monitored'] = True
                                                                                    
                                                                                    # Dynamically find 1080p quality profile ID
                                                                                    async with fallback_session.get(f"{radarr_url}/api/v3/qualityprofile", headers=headers) as profile_resp:
                                                                                        if profile_resp.status == 200:
                                                                                            profiles = await profile_resp.json()
                                                                                            # Find HD-1080p profile
                                                                                            hd_1080p_profile_id = None
                                                                                            for profile in profiles:
                                                                                                profile_name = profile.get('name', '').lower()
                                                                                                if 'hd-1080p' in profile_name or '1080p' in profile_name:
                                                                                                    hd_1080p_profile_id = profile['id']
                                                                                                    logger_instance.info(f"Found 1080p profile: {profile['name']} (ID: {hd_1080p_profile_id})")
                                                                                                    break
                                                                                            
                                                                                            if hd_1080p_profile_id:
                                                                                                # Update quality profile to 1080p for this grab
                                                                                                movie_data['qualityProfileId'] = hd_1080p_profile_id
                                                                                            else:
                                                                                                logger_instance.warning("⚠️ Could not find 1080p quality profile, keeping current profile")
                                                                                        else:
                                                                                            logger_instance.warning(f"⚠️ Failed to fetch quality profiles: {profile_resp.status}")
                                                                                    
                                                                                    async with fallback_session.put(f"{radarr_url}/api/v3/movie/{radarr_movie_id}", headers=headers, json=movie_data) as monitor_resp:
                                                                                        if monitor_resp.status in [200, 202]:
                                                                                            if sel_guid:
                                                                                                # Map indexer name to ID (exact same logic as normal flow)
                                                                                                indexer_id = None
                                                                                                if sel_indexer:
                                                                                                    try:
                                                                                                        async with fallback_session.get(f"{radarr_url}/api/v3/indexer", headers=headers) as idx_resp:
                                                                                                            if idx_resp.status == 200:
                                                                                                                indexers = await idx_resp.json()
                                                                                                                for indexer in indexers:
                                                                                                                    if indexer.get('name', '').lower() == sel_indexer.lower():
                                                                                                                        indexer_id = indexer.get('id')
                                                                                                                        break
                                                                                                                if indexer_id is None and indexers:
                                                                                                                    indexer_id = indexers[0].get('id')  # Fallback
                                                                                                    except Exception:
                                                                                                        pass
                                                                                                
                                                                                                # Use exact same payload as normal flow
                                                                                                grab_payload = {
                                                                                                    "guid": sel_guid,
                                                                                                    "indexerId": indexer_id
                                                                                                }
                                                                                                
                                                                                                async with fallback_session.post(f"{radarr_url}/api/v3/release", headers=headers, json=grab_payload) as grab_resp:
                                                                                                    if grab_resp.status in [200, 201, 202]:
                                                                                                        print("✅ 1080p fallback download started successfully!")
                                                                                                        logger_instance.info(f"1080p fallback grab successful: {sel_title}")
                                                                                                        
                                                                                                        # Update telemetry to track the new 1080p download
                                                                                                        if telemetry_integrator:
                                                                                                            try:
                                                                                                                # The job ID should already be updated from the remove/re-add process
                                                                                                                job_id = f"radarr_{radarr_movie_id}"
                                                                                                                logger_instance.info(f"🎯 Telemetry: 1080p fallback download started for {job_id} - continuing verification")
                                                                                                                # The telemetry job should already be tracking the correct ID from the remove/re-add process
                                                                                                            except Exception as telemetry_err:
                                                                                                                logger_instance.warning(f"⚠️ Failed to update telemetry: {telemetry_err}")
                                                                                                    else:
                                                                                                        grab_error = await grab_resp.text()
                                                                                                        logger_instance.warning(f"⚠️ 1080p fallback grab failed: {grab_resp.status} - {grab_error}")
                                                                                                        print("⚠️ Failed to start 1080p download - triggering search as fallback")
                                                                                                        
                                                                                                        # Fallback to search
                                                                                                        async with fallback_session.post(f"{radarr_url}/api/v3/command", headers=headers, json={"name": "MoviesSearch", "movieIds": [radarr_movie_id]}) as search_resp:
                                                                                                            if search_resp.status in [200, 201, 202]:
                                                                                                                print("✅ 1080p search triggered as fallback")
                                                                                                            else:
                                                                                                                logger_instance.warning(f"⚠️ 1080p search fallback failed: {search_resp.status}")
                                                                                            else:
                                                                                                logger_instance.warning("⚠️ No GUID available for 1080p fallback grab")
                                                                                        else:
                                                                                            logger_instance.warning(f"⚠️ Failed to enable monitoring for 1080p: {monitor_resp.status}")
                                                                                else:
                                                                                    logger_instance.warning(f"⚠️ Failed to get movie data for 1080p fallback: {get_resp.status}")
                                                                    except Exception as fallback_grab_err:
                                                                        logger_instance.warning(f"⚠️ Failed to grab 1080p fallback release: {fallback_grab_err}")
                                                            else:
                                                                print("❌ No 1080p alternatives available either.")
                                                                print("ℹ️ No suitable releases found. Skipping download per your choice.")
                                                        
                                                        except Exception as fallback_err:
                                                            logger_instance.warning(f"⚠️ Quality fallback analysis failed: {fallback_err}")
                                                            print("ℹ️ Fallback analysis failed. Skipping download per your choice.")
                                                    else:
                                                        print("ℹ️ No other acceptable releases available. Skipping download per your choice.")
                                                else:
                                                    print("\n🔁 You declined the top choice. Other acceptable releases:")
                                                    for idx, cand in enumerate(alt_candidates, start=1):
                                                        c_title = cand.get('title') or 'Unknown'
                                                        c_size_gb = (cand.get('size', 0) or 0) / (1024**3)
                                                        c_risk = cand.get('risk_score', 0.0) or 0.0
                                                        c_missing = cand.get('probe_missing_ratio', 0.0) or 0.0
                                                        print(f"   {idx}. 🎬 {c_title}")
                                                        print(f"      💾 {c_size_gb:.2f} GB | ⚡ risk: {c_risk:.4f} | missing: {c_missing:.1%}")
                                                    try:
                                                        alt_choice = input("Enter number to download one of these, or press Enter to skip: ").strip()
                                                    except (EOFError, KeyboardInterrupt):
                                                        alt_choice = ''
                                                    if alt_choice.isdigit():
                                                        sel_idx = int(alt_choice)
                                                        if 1 <= sel_idx <= len(alt_candidates):
                                                            cand = alt_candidates[sel_idx - 1]
                                                            sel_guid = cand.get('guid')
                                                            sel_indexer = cand.get('indexer')
                                                            sel_title = cand.get('title') or 'Selected Release'
                                                            sel_size_b = int(cand.get('size') or 0)
                                                            sel_size_gb = sel_size_b / (1024**3) if sel_size_b else 0.0
                                                            sel_risk = float(cand.get('risk_score', 0.0) or 0.0)
                                                            sel_missing = float(cand.get('probe_missing_ratio', 0.0) or 0.0)
                                                            # Attempt direct grab for selected candidate
                                                            try:
                                                                import aiohttp
                                                                headers = {"X-Api-Key": radarr_api_key}
                                                                async with aiohttp.ClientSession() as grab_session:
                                                                    # Ensure monitored
                                                                    async with grab_session.get(f"{radarr_url}/api/v3/movie/{radarr_movie_id}", headers=headers) as get_resp:
                                                                        if get_resp.status == 200:
                                                                            movie_data = await get_resp.json()
                                                                            movie_data["monitored"] = True
                                                                            async with grab_session.put(f"{radarr_url}/api/v3/movie/{radarr_movie_id}", headers=headers, json=movie_data) as monitor_resp:
                                                                                if monitor_resp.status in [200, 202]:
                                                                                    # Map indexer name to ID
                                                                                    indexer_id = None
                                                                                    if sel_indexer:
                                                                                        try:
                                                                                            async with grab_session.get(f"{radarr_url}/api/v3/indexer", headers=headers) as idx_resp:
                                                                                                if idx_resp.status == 200:
                                                                                                    indexers = await idx_resp.json()
                                                                                                    for indexer in indexers:
                                                                                                        if indexer.get('name', '').lower() == sel_indexer.lower():
                                                                                                            indexer_id = indexer.get('id')
                                                                                                            break
                                                                                                    if indexer_id is None and indexers:
                                                                                                        indexer_id = indexers[0].get('id')
                                                                                        except Exception:
                                                                                            pass
                                                                                    if sel_guid:
                                                                                        # CRITICAL FIX: Store candidate information for fallback BEFORE alternative download
                                                                                        if telemetry_integrator and telemetry_integrator.telemetry and cand and all_candidates:
                                                                                            print(f"💾 Storing alternative candidate information for fallback system...")
                                                                                            try:
                                                                                                # FIXED: Find the index of the selected alternative candidate in the ACCEPTABLE candidates list
                                                                                                acceptable_candidates = preflight_result.get('acceptable_candidates', [])
                                                                                                selected_index = None
                                                                                                for i, candidate in enumerate(acceptable_candidates):
                                                                                                    if candidate.get('guid') == cand.get('guid'):
                                                                                                        selected_index = i
                                                                                                        break
                                                                                                
                                                                                                if selected_index is None:
                                                                                                    # Fallback: try to find in all_candidates and map to acceptable
                                                                                                    print(f"⚠️ Selected alternative candidate not found in acceptable list, using fallback search")
                                                                                                    selected_index = 0  # Default to first acceptable candidate
                                                                                                
                                                                                                # FIXED: Find the original system recommendation index (the "best" candidate)
                                                                                                original_system_recommendation_index = 0  # TEMPORARY: Use old behavior for testing
                                                                                                
                                                                                                # Store using the correct method signature
                                                                                                telemetry_integrator.telemetry.store_movie_candidate_info(
                                                                                                    radarr_id=radarr_movie_id,
                                                                                                    candidate_details=cand,  # The alternative candidate chosen
                                                                                                    user_selection_index=selected_index,  # Index in ACCEPTABLE candidates
                                                                                                    original_system_recommendation_index=original_system_recommendation_index  # Actual system recommendation
                                                                                                )
                                                                                                print(f"✅ Stored alternative candidate #{selected_index + 1 if selected_index is not None else '?'} of {len(acceptable_candidates)} acceptable candidates for fallback")
                                                                                            except Exception as storage_err:
                                                                                                logger_instance.warning(f"⚠️ Failed to store candidate info: {storage_err}")
                                                                                        
                                                                                        # Use new cache sync download function
                                                                                        sel_movie_data = {
                                                                                            'guid': sel_guid,
                                                                                            'indexer': cand.get('indexer'),
                                                                                            'indexerId': indexer_id,
                                                                                            'title': sel_title
                                                                                        }
                                                                                        
                                                                                        download_success, download_msg = await download_movie_with_cache_sync(
                                                                                            session=grab_session,
                                                                                            radarr_url=radarr_url,
                                                                                            headers=headers,
                                                                                            movie_id=radarr_movie_id,
                                                                                            movie_data=sel_movie_data,
                                                                                            logger_instance=logger_instance
                                                                                        )
                                                                                        
                                                                                        if download_success:
                                                                                            print(f"✅ Movie download started: {sel_title}")
                                                                                            print(f"   📥 Size: {sel_size_gb:.2f} GB | Risk: {sel_risk:.4f} | Decision: ACCEPT")
                                                                                            
                                                                                            # Update telemetry with actual NZB filename after preflight selection
                                                                                            if telemetry_integrator and telemetry_integrator.telemetry:
                                                                                                job_id = f"radarr_{radarr_movie_id}"
                                                                                                if telemetry_integrator.update_nzb_filename(job_id, sel_title):
                                                                                                    logger_instance.info(f"📝 Updated telemetry with NZB filename: {sel_title}")
                                                                                            
                                                                                            # Summary for selected
                                                                                            print(f"\n🔬 Preflight Movie Selection (downloading):")
                                                                                            print(f"   #1. 🎬 {sel_title}")
                                                                                            print(f"       💾 Size: {sel_size_gb:.2f} GB ({sel_size_b:,} bytes)")
                                                                                            print(f"       ⚡ Risk: {sel_risk:.4f} | Missing: {sel_missing:.1%} | Decision: ACCEPT")
                                                                                            print(f"\n📊 Movie Preflight Summary: 1 movie | Total: {sel_size_gb:.2f} GB")
                                                                                            print(f"🎯 Download started immediately after analysis")
                                                                                        else:
                                                                                            logger_instance.warning(f"⚠️ Selected movie download failed: {download_msg}")
                                                                                    else:
                                                                                        logger_instance.warning("⚠️ No GUID available for selected release")
                                                                        else:
                                                                            logger_instance.warning(f"⚠️ Failed to get movie data for selection: {get_resp.status}")
                                                            except Exception as grab_err:
                                                                logger_instance.warning(f"⚠️ Failed to grab selected movie release: {grab_err}")
                                                        else:
                                                            print(f"❌ Invalid selection: '{sel_idx}'. Please choose a number between 1 and {len(alt_candidates)}.")
                                                    elif alt_choice != '':
                                                        print(f"❌ Invalid input: '{alt_choice}'. Please enter a number or press Enter to skip.")
                                                    else:
                                                        print("ℹ️ Skipped starting a download per your choice.")

                                            # Show comprehensive summary like TV shows only if we actually started a download
                                            if confirm in ('', 'y', 'yes'):
                                                print(f"\n✅ Preflight found and started download for 1 movie")
                                                print(f"\n🔬 Preflight Movie Selection (downloading):")
                                                print(f"   #1. 🎬 {title}")
                                                print(f"       💾 Size: {size_gb:.2f} GB ({size_b:,} bytes)")
                                                print(f"       ⚡ Risk: {risk_score:.4f} | Missing: {missing_ratio:.1%} | Decision: {decision}")
                                                if runtime_min:
                                                    print(f"       ⏱️ Runtime: {runtime_min} min")

                                                print(f"\n📊 Movie Preflight Summary: 1 movie | Total: {size_gb:.2f} GB")
                                                print(f"🎯 Download started immediately after analysis")
                                        else:
                                            print("⚠️ No acceptable movie release found in preflight analysis")
                                            logger_instance.warning("⚠️ No acceptable movie release found in preflight; leaving Radarr to handle future grabs.")
                                            # Still offer to enable monitoring for future releases
                                            try:
                                                confirm = input("Enable monitoring anyway for future releases? [Y/n]: ").strip().lower()
                                            except (EOFError, KeyboardInterrupt):
                                                confirm = 'y'
                                            if confirm in ('', 'y', 'yes'):
                                                try:
                                                    import aiohttp
                                                    headers = {"X-Api-Key": radarr_api_key}
                                                    async with aiohttp.ClientSession() as monitor_session:
                                                        async with monitor_session.get(f"{radarr_url}/api/v3/movie/{radarr_movie_id}", headers=headers) as get_resp:
                                                            if get_resp.status == 200:
                                                                movie_data = await get_resp.json()
                                                                movie_data["monitored"] = True
                                                                async with monitor_session.put(f"{radarr_url}/api/v3/movie/{radarr_movie_id}", headers=headers, json=movie_data) as monitor_resp:
                                                                    if monitor_resp.status in [200, 202]:
                                                                        print("✅ Movie monitoring enabled for future releases")
                                                                    else:
                                                                        logger_instance.warning(f"⚠️ Failed to enable monitoring: {monitor_resp.status}")
                                                            else:
                                                                logger_instance.warning(f"⚠️ Failed to get movie data: {get_resp.status}")
                                                except Exception as monitor_err:
                                                    logger_instance.warning(f"⚠️ Failed to enable monitoring: {monitor_err}")
                                            else:
                                                print("⏭️ Movie left unmonitored per user choice")
                                    except Exception as mp_err:
                                        logger_instance.error(f"Movie preflight failed: {mp_err}")

                                elif choice == '2':
                                    # --- RADARR AUTO-GRAB ---
                                    try:
                                        import aiohttp
                                        headers = {"X-Api-Key": radarr_api_key}
                                        async with aiohttp.ClientSession() as s:
                                            async with s.get(f"{radarr_url}/api/v3/movie/{radarr_movie_id}", headers=headers) as get_resp:
                                                if get_resp.status == 200:
                                                    movie_data = await get_resp.json()
                                                    movie_data["monitored"] = True
                                                    async with s.put(f"{radarr_url}/api/v3/movie/{radarr_movie_id}", headers=headers, json=movie_data) as put_resp:
                                                        if put_resp.status in [200, 202]:
                                                            async with s.post(f"{radarr_url}/api/v3/command", headers=headers, json={"name": "MoviesSearch", "movieIds": [radarr_movie_id]}) as cmd_resp:
                                                                if cmd_resp.status in [200, 201, 202]:
                                                                    print("⚡ Radarr auto-grab triggered for this movie")
                                                                else:
                                                                    logger_instance.warning(f"⚠️ Auto-grab search failed: {cmd_resp.status}")
                                                        else:
                                                            logger_instance.warning(f"⚠️ Could not set monitored on movie: {put_resp.status}")
                                                else:
                                                    logger_instance.warning(f"⚠️ Could not load movie: {get_resp.status}")
                                    except Exception as e:
                                        logger_instance.warning(f"⚠️ Failed to trigger auto-grab: {e}")
                                else:
                                    print("⏭️ Skipping - movie left in Radarr without starting downloads")
                        except Exception as strat_err:
                            logger_instance.warning(f"Movie strategy prompt failed: {strat_err}")


                        # Store metadata
                        cleaned_title = metadata.get('cleaned_title')
                        year = metadata.get('year')
                        tmdb_id = str(metadata.get('tmdb_id'))
                        title_year_id = f"{cleaned_title} ({year})"

                        success = metadata_db.save_movie_metadata(
                            unique_id=title_year_id,
                            title=cleaned_title,
                            year=year if isinstance(year, int) else None,
                            tmdb_id=tmdb_id,
                            imdb_id=metadata.get('imdb_id'),
                            metadata={
                                **metadata,
                                'raw_title_input': raw_title,
                                'radarr_id': result.get('radarr_id')
                            }
                        )

                        if success:
                            logger_instance.info(f"Stored metadata for: {title_year_id}")
                        else:
                            logger_instance.warning(f"Failed to store metadata for: {title_year_id}")

                    else:
                        print(f"❌ Failed to add to Radarr: {result.get('reason', 'unknown error')}")
                        logger_instance.error(f"Failed to add movie to Radarr: {result.get('reason')}")

                else:
                    print(f"❌ Failed to get metadata for: {raw_title}")
                    logger_instance.error(f"Failed to get metadata for: {raw_title}")

            except Exception as e:
                print(f"❌ Error processing {raw_title}: {e}")
                logger_instance.error(f"Error processing movie {raw_title}: {e}")

    finally:
        metadata_db.close()

    return success_count


def parse_tv_show_request(raw_title: str) -> dict:
    """
    Enhanced TV show request parser with support for multiple episodes and TVDB integration.

    Supported formats:
    - "Breaking Bad (2008)" - Full series
    - "The Office (2005) S01" - Specific season
    - "Game of Thrones (2011) S01E01" - Single episode
    - "Kim Possible (2002) S02E02, S02E04" - Multiple episodes same season
    - "Friends (1994) S01E01, S02E05" - Multiple episodes different seasons
    - "Friends" - Full series (no year)

    Args:
        raw_title: Raw TV show request string

    Returns:
        dict: Enhanced parsed components with multi-episode support
    """
    import re

    # Initialize result
    result = {
        'request_type': 'full_series',  # full_series, specific_season, specific_episodes, multiple_episodes
        'show_title': raw_title.strip(),
        'year': None,
        'seasons': [],  # List of seasons involved
        'episodes': [],  # List of episode dicts: [{'season': 1, 'episode': 1}, ...]
        'display_title': raw_title.strip(),
        'sonarr_params': {},
        'requires_tvdb_lookup': False,
        'chronological_episodes': []  # Will be populated by TVDB lookup for full series
    }

    # Clean the input
    clean_title = raw_title.strip()

    # Pattern for multiple episodes: "Show Title (Year) S01E01, S01E05, S02E03"
    # Fixed regex to properly capture comma-separated episodes
    multi_episode_pattern = r'^(.+?)\s*(?:\((\d{4})\))?\s+(S\d{1,2}E\d{1,2}(?:\s*,\s*S\d{1,2}E\d{1,2})+)$'
    multi_match = re.match(multi_episode_pattern, clean_title, re.IGNORECASE)

    if multi_match:
        result['show_title'] = multi_match.group(1).strip()
        result['year'] = int(multi_match.group(2)) if multi_match.group(2) else None
        episodes_str = multi_match.group(3)

        # Parse individual episodes
        episode_items = re.findall(r'S(\d{1,2})E(\d{1,2})', episodes_str, re.IGNORECASE)

        if len(episode_items) > 1:
            result['request_type'] = 'multiple_episodes'
            result['episodes'] = [{'season': int(s), 'episode': int(e)} for s, e in episode_items]
            result['seasons'] = sorted(list(set(ep['season'] for ep in result['episodes'])))

            # Create display title
            episodes_display = ', '.join([f"S{ep['season']:02d}E{ep['episode']:02d}" for ep in result['episodes']])
            result['display_title'] = f"{result['show_title']} ({result['year']}) {episodes_display}" if result['year'] else f"{result['show_title']} {episodes_display}"

            result['sonarr_params'] = {
                'seasons': result['seasons'],
                'monitor_episodes': 'specific_episodes',
                'episodes_list': result['episodes']
            }
            return result
        elif len(episode_items) == 1:
            # Single episode, continue with existing logic
            s, e = episode_items[0]
            result['request_type'] = 'specific_episodes'
            result['episodes'] = [{'season': int(s), 'episode': int(e)}]
            result['seasons'] = [int(s)]
            result['display_title'] = f"{result['show_title']} ({result['year']}) S{int(s):02d}E{int(e):02d}" if result['year'] else f"{result['show_title']} S{int(s):02d}E{int(e):02d}"
            result['sonarr_params'] = {
                'seasons': [int(s)],
                'monitor_episodes': f"S{int(s):02d}E{int(e):02d}",
                'episodes_list': result['episodes']
            }
            return result

    # Pattern for single episode: "Show Title (Year) S01E01"
    episode_pattern = r'^(.+?)\s*(?:\((\d{4})\))?\s+S(\d{1,2})E(\d{1,2})$'
    episode_match = re.match(episode_pattern, clean_title, re.IGNORECASE)
    if not episode_match:
        nl_episode_pattern = r'^(.+?)\s*(?:\((\d{4})\))?\s*Season\s+(\d{1,2})\s*Episode\s+(\d{1,2})$'
        episode_match = re.match(nl_episode_pattern, clean_title, re.IGNORECASE)

    if episode_match:
        result['request_type'] = 'specific_episodes'
        result['show_title'] = episode_match.group(1).strip()
        result['year'] = int(episode_match.group(2)) if episode_match.group(2) else None
        season = int(episode_match.group(3))
        episode = int(episode_match.group(4))
        result['seasons'] = [season]
        result['episodes'] = [{'season': season, 'episode': episode}]
        result['display_title'] = f"{result['show_title']} ({result['year']}) S{season:02d}E{episode:02d}" if result['year'] else f"{result['show_title']} S{season:02d}E{episode:02d}"
        result['sonarr_params'] = {
            'seasons': [season],
            'monitor_episodes': f"S{season:02d}E{episode:02d}",
            'episodes_list': result['episodes']
        }
        return result

    # Pattern for season-specific request: "Show Title (Year) S01"
    season_pattern = r'^(.+?)\s*(?:\((\d{4})\))?\s+S(\d{1,2})$'
    season_match = re.match(season_pattern, clean_title, re.IGNORECASE)
    if not season_match:
        nl_season_pattern = r'^(.+?)\s*(?:\((\d{4})\))?\s*Season\s+(\d{1,2})$'
        season_match = re.match(nl_season_pattern, clean_title, re.IGNORECASE)

    if season_match:
        result['request_type'] = 'specific_season'
        result['show_title'] = season_match.group(1).strip()
        result['year'] = int(season_match.group(2)) if season_match.group(2) else None
        season = int(season_match.group(3))
        result['seasons'] = [season]
        result['display_title'] = f"{result['show_title']} ({result['year']}) S{season:02d}" if result['year'] else f"{result['show_title']} S{season:02d}"
        result['sonarr_params'] = {
            'seasons': [season],
            'monitor_episodes': 'season',
            'requires_tvdb_lookup': True  # Need to get all episodes in season
        }
        result['requires_tvdb_lookup'] = True
        return result

    # Pattern for full series with year: "Show Title (Year)"
    full_with_year_pattern = r'^(.+?)\s*\((\d{4})\)$'
    full_year_match = re.match(full_with_year_pattern, clean_title)

    if full_year_match:
        result['request_type'] = 'full_series'
        result['show_title'] = full_year_match.group(1).strip()
        result['year'] = int(full_year_match.group(2))
        result['display_title'] = f"{result['show_title']} ({result['year']})"
        result['sonarr_params'] = {
            'seasons': [1],  # Changed: Only Season 1 for season-by-season progression
            'monitor_episodes': 'season_1_only'  # Changed: Custom marker for Season 1 only
        }
        result['requires_tvdb_lookup'] = True  # Need to get all episodes for chronological order
        return result

    # Default: full series without year
    result['request_type'] = 'full_series'
    result['show_title'] = clean_title
    result['display_title'] = clean_title
    result['sonarr_params'] = {
        'seasons': [1],  # Changed: Only Season 1 for season-by-season progression
        'monitor_episodes': 'season_1_only'  # Changed: Custom marker for Season 1 only
    }
    result['requires_tvdb_lookup'] = True  # Need to get all episodes for chronological order

    return result


def display_tv_show_selection_with_specificity(tv_show_list: List[str]) -> List[int]:
    """
    Display TV show selection with specificity information.

    Args:
        tv_show_list: List of raw TV show request strings

    Returns:
        List of selected indices (0-based)
    """
    if not tv_show_list:
        print(f"📭 No TV shows found in requests file")
        return []

    print(f"\n{'='*70}")
    print(f"📺 TV Shows Available for Processing:")
    print(f"{'='*70}")

    # Parse and display each TV show with its specificity
    for i, raw_title in enumerate(tv_show_list, 1):
        parsed = parse_tv_show_request(raw_title)

        # Create enhanced specificity indicator
        if parsed['request_type'] == 'specific_episodes':
            if len(parsed.get('episodes', [])) == 1:
                ep = parsed['episodes'][0]
                specificity = f"📺 Episode S{ep['season']:02d}E{ep['episode']:02d}"
            else:
                episodes_str = ', '.join([f"S{ep['season']:02d}E{ep['episode']:02d}" for ep in parsed.get('episodes', [])])
                specificity = f"📺 Episodes {episodes_str}"
        elif parsed['request_type'] == 'multiple_episodes':
            episodes_str = ', '.join([f"S{ep['season']:02d}E{ep['episode']:02d}" for ep in parsed.get('episodes', [])])
            specificity = f"📺 Multi-Episodes {episodes_str}"
        elif parsed['request_type'] == 'specific_season':
            seasons_str = ', '.join([f"S{s:02d}" for s in parsed.get('seasons', [])])
            specificity = f"📀 Season {seasons_str}"
        else:
            specificity = f"📚 Complete Series"

        print(f"  {i:2d}. {parsed['display_title']:<40} {specificity:<25}")
        if parsed.get('requires_tvdb_lookup'):
            print(f"      📋 Will use TVDB for chronological episode tracking")

    print(f"\n📊 Legend:")
    print(f"   📺 Episode    - Single episode download")
    print(f"   📀 Season     - Full season download (all episodes)")
    print(f"   📚 Series     - Complete series (all seasons)")

    print(f"\n📝 Selection Options:")
    print(f"  • Single: Enter number (e.g., '3')")
    print(f"  • Multiple: Enter comma-separated numbers (e.g., '1,3,5')")
    print(f"  • All: Enter 'all' or 'a'")
    print(f"  • None: Enter 'none' or 'n' to skip")
    print(f"  • Quit: Enter 'quit' or 'q'")

    while True:
        try:
            selection = input(f"\nSelect TV shows to process: ").strip().lower()

            if selection in ['quit', 'q']:
                return []
            elif selection in ['none', 'n']:
                return []
            elif selection in ['all', 'a']:
                return list(range(len(tv_show_list)))
            else:
                # Parse comma-separated numbers
                try:
                    selected_numbers = [int(x.strip()) for x in selection.split(',')]
                    selected_indices = []

                    # Validate all numbers are in range
                    for num in selected_numbers:
                        if 1 <= num <= len(tv_show_list):
                            selected_indices.append(num - 1)  # Convert to 0-based
                        else:
                            print(f"⚠️ Number {num} is out of range (1-{len(tv_show_list)})")
                            break
                    else:
                        # All numbers were valid
                        if selected_indices:
                            # Show what was selected (sizes depend on Sonarr Quality Definitions)
                            selected_shows = [tv_show_list[i] for i in selected_indices]

                            print(f"\n✅ Selected {len(selected_shows)} TV shows:")
                            for i, raw_title in enumerate(selected_shows, 1):
                                parsed = parse_tv_show_request(raw_title)

                                if parsed['request_type'] == 'specific_episode':
                                    type_icon = "📺"
                                elif parsed['request_type'] == 'specific_season':
                                    type_icon = "📀"
                                else:
                                    type_icon = "📚"

                                print(f"    {i}. {type_icon} {parsed['display_title']}")

                            # Confirm selection
                            confirm = input(f"\nProceed with these selections? [y/N]: ").strip().lower()
                            if confirm in ['y', 'yes']:
                                return selected_indices
                            else:
                                print(f"Selection cancelled, please choose again...")
                                continue
                        else:
                            print(f"No valid selections made")
                            continue

                except ValueError:
                    print(f"⚠️ Invalid input. Please enter numbers separated by commas")
                    continue

        except KeyboardInterrupt:
            print(f"\n👋 Selection cancelled...")
            return []


async def process_selected_tv_shows(selected_tv_shows: List[str], settings_dict: dict, logger_instance, telemetry_integrator=None) -> int:
    """
    Process selected TV shows with granular specificity support.
    Handles full series, specific seasons, and specific episodes.

    Args:
        selected_tv_shows: List of selected TV show request strings
        settings_dict: Settings dictionary
        logger_instance: Logger instance

    Returns:
        Number of successfully processed TV shows
    """
    if not selected_tv_shows:
        return 0

    print(f"\n📺 Processing {len(selected_tv_shows)} selected TV shows...")
    print(f"{'='*60}")

    success_count = 0
    workspace_root = Path.cwd()
    metadata_db = MetadataOnlyDatabase(workspace_root)

    # Compact dashboard toggle
    try:
        from utils.common_helpers import get_setting as _get
        compact_output = _get("UI", "compact_output", settings_dict=settings_dict, default=True)
        if not isinstance(compact_output, bool):
            compact_output = str(compact_output).lower() in ("1","true","yes","on")
    except Exception:
        compact_output = True

    dashboard_rows: List[dict] = []

    try:
        for i, raw_title in enumerate(selected_tv_shows, 1):
            print(f"\n📍 Progress: {i}/{len(selected_tv_shows)}")

            # Parse the TV show request for specificity
            parsed_request = parse_tv_show_request(raw_title)

            print(f"📺 Processing: {parsed_request['display_title']}")
            print(f"   🎯 Request Type: {parsed_request['request_type'].replace('_', ' ').title()}")

            # Enhanced target display for new episode types
            if parsed_request['request_type'] in ['specific_episodes', 'multiple_episodes']:
                episodes = parsed_request.get('episodes', [])
                if len(episodes) == 1:
                    ep = episodes[0]
                    print(f"   📺 Target: Season {ep['season']}, Episode {ep['episode']}")
                else:
                    episodes_str = ', '.join([f"S{ep['season']:02d}E{ep['episode']:02d}" for ep in episodes])
                    print(f"   📺 Target: {episodes_str}")
            elif parsed_request['request_type'] == 'specific_season':
                seasons = parsed_request.get('seasons', [])
                seasons_str = ', '.join([f"S{s:02d}" for s in seasons])
                print(f"   📀 Target: {seasons_str} (all episodes)")
            else:
                print(f"   📚 Target: Complete series (all seasons)")

            logger_instance.info(f"Processing TV show: {parsed_request['display_title']} ({parsed_request['request_type']})")

            # Fetch TV metadata using the cleaned show title
            try:
                # Use TMDb TV search for proper TV show metadata
                metadata = await fetch_tv_metadata_for_intake(parsed_request['show_title'], parsed_request.get('year'), settings_dict)

                if metadata and metadata.get('success'):
                    print(f"✅ Found metadata: {metadata.get('cleaned_title')} ({metadata.get('year', 'Unknown')})")
                    logger_instance.info(f"Successfully found TV metadata for: {metadata.get('cleaned_title')}")

                    # Extract variables for use throughout the processing
                    cleaned_title = metadata.get('cleaned_title') or parsed_request.get('show_title') or raw_title
                    # Prefer the requested year over metadata year for TV shows
                    year = parsed_request.get('year') or metadata.get('year') or 'Unknown'

                    # Create enhanced TV data with specificity parameters
                    tv_data = {
                        "cleaned_title": cleaned_title,
                        "year": year,
                        "tvdb_id": metadata.get('tvdb_id') or metadata.get('tmdb_id'),
                        "request_specificity": parsed_request['request_type'],
                        "sonarr_params": parsed_request['sonarr_params'],
                        "raw_request": raw_title
                    }

                    # Enhanced TVDB Integration for chronological episode tracking
                    tvdb_data = None
                    episode_queue = []

                    if parsed_request.get('requires_tvdb_lookup'):
                        print(f"🔍 Fetching complete episode data from TVDB...")
                        logger_instance.info(f"Fetching TVDB episode data for: {cleaned_title}")

                        tvdb_id = metadata.get('tvdb_id') or metadata.get('tmdb_id')
                        if tvdb_id:
                            tvdb_data = await fetch_tvdb_episode_data(tvdb_id, settings_dict)

                            if tvdb_data.get('success'):
                                total_episodes = tvdb_data.get('total_episodes', 0)
                                total_seasons = tvdb_data.get('total_seasons', 0)

                                # 🚨 DEBUG: Log what TMDb/TVDB is actually returning
                                metadata_source = tvdb_data.get('metadata_source', 'Unknown')
                                print(f"🔍 DEBUG: Data source: {metadata_source}")
                                print(f"🔍 DEBUG: Total seasons reported: {total_seasons}")
                                print(f"🔍 DEBUG: Total episodes reported: {total_episodes}")

                                # Check if Season 1 exists and count its episodes
                                episodes_by_season = tvdb_data.get('episodes_by_season', {})
                                if episodes_by_season:
                                    for season_num, season_data in episodes_by_season.items():
                                        episode_count = season_data.get('episode_count', 0)
                                        print(f"🔍 DEBUG: Season {season_num}: {episode_count} episodes")
                                        if season_num == 1:
                                            print(f"🔍 DEBUG: Season 1 episodes: {[ep.get('title', 'Unknown') for ep in season_data.get('episodes', [])[:5]]}")

                                print(f"✅ Found complete series data: {total_seasons} seasons, {total_episodes} episodes")
                                logger_instance.info(f"TVDB data retrieved: {total_seasons} seasons, {total_episodes} episodes")

                                # Create chronological episode queue
                                episode_queue = await create_chronological_episode_queue(parsed_request, tvdb_data, logger_instance)

                                if episode_queue:
                                    print(f"📋 Created chronological download queue: {len(episode_queue)} episodes")
                                    if len(episode_queue) <= 5:
                                        print("   📺 Episodes to download:")
                                        for ep in episode_queue:
                                            print(f"      S{ep['season']:02d}E{ep['episode']:02d}: {ep.get('title', 'Unknown Title')}")
                                    else:
                                        print(f"   📺 First 3 episodes: S{episode_queue[0]['season']:02d}E{episode_queue[0]['episode']:02d}, S{episode_queue[1]['season']:02d}E{episode_queue[1]['episode']:02d}, S{episode_queue[2]['season']:02d}E{episode_queue[2]['episode']:02d}...")
                                        print(f"   📺 Last episode: S{episode_queue[-1]['season']:02d}E{episode_queue[-1]['episode']:02d}")
                                else:
                                    print("⚠️ No episodes found for download queue")
                            else:
                                print(f"⚠️ Could not fetch TVDB episode data: {tvdb_data.get('error', 'Unknown error')}")
                                logger_instance.warning(f"TVDB fetch failed: {tvdb_data.get('error', 'unknown')}")
                        else:
                            print("⚠️ No TVDB ID available for episode lookup")

                    # Enhanced: Season-by-season progression for full series
                    if parsed_request['request_type'] == 'full_series':
                        # Register series for season-by-season progression
                        try:
                            # Sequential progression handled during Sonarr addition
                            logger_instance.info(f"📚 Full series request for {cleaned_title} ({year}) - sequential progression will be configured during Sonarr addition")

                            # For full series, only queue Season 1 initially
                            season_1_episodes = [ep for ep in episode_queue if ep['season'] == 1]
                            episode_queue = season_1_episodes  # Override to only Season 1

                            logger_instance.info(f"🎯 Season-by-season mode: Queuing Season 1 only ({len(episode_queue)} episodes)")
                            print(f"🎯 Season-by-season mode: Starting with Season 1 ({len(episode_queue)} episodes)")

                        except Exception as e:
                            logger_instance.warning(f"Failed to set up season progression: {e}, falling back to full series queue")

                    # Create proper TV data structure for Sonarr with enhanced episode info
                    tv_sonarr_data = {
                        "cleaned_title": cleaned_title,  # Use cleaned_title key for consistency
                        "year": year,
                        "tvdb_id": metadata.get('tvdb_id') or metadata.get('tmdb_id'),
                        "imdb_id": metadata.get('imdb_id'),
                        "request_specificity": parsed_request['request_type'],
                        "seasons": parsed_request.get('seasons', []),
                        "episodes": parsed_request.get('episodes', []),
                        "display_title": parsed_request['display_title'],
                        "episode_queue": episode_queue,  # Enhanced: chronological episode tracking
                        "tvdb_episode_data": tvdb_data    # Enhanced: complete series metadata
                    }

                    from _internal.src.intake_tv_orchestrator import add_tv_show as _orchestrate_tv
                    sonarr_result = await _orchestrate_tv(tv_sonarr_data, settings_dict, logger_instance)

                    if sonarr_result.get("success"):
                        # NEW: Use telemetry tracking instead of static messages
                        await track_tv_show_success_with_telemetry(sonarr_result, metadata, telemetry_integrator, logger_instance)

                        # Enhanced status display based on request type
                        if parsed_request['request_type'] == 'specific_episodes':
                            if len(parsed_request.get('episodes', [])) == 1:
                                ep = parsed_request['episodes'][0]
                                print(f"   📺 Configured for: S{ep['season']:02d}E{ep['episode']:02d} only")
                            else:
                                episodes_str = ', '.join([f"S{ep['season']:02d}E{ep['episode']:02d}" for ep in parsed_request.get('episodes', [])])
                                print(f"   📺 Configured for: {episodes_str}")
                        elif parsed_request['request_type'] == 'multiple_episodes':
                            episodes_str = ', '.join([f"S{ep['season']:02d}E{ep['episode']:02d}" for ep in parsed_request.get('episodes', [])])
                            print(f"   � Configured for multiple episodes: {episodes_str}")
                        elif parsed_request['request_type'] == 'specific_season':
                            seasons_str = ', '.join([f"S{s:02d}" for s in parsed_request.get('seasons', [])])
                            print(f"   📀 Configured for: {seasons_str} (all episodes)")
                            if episode_queue:
                                print(f"   📋 Queued {len(episode_queue)} episodes for chronological download")
                        else:
                            print(f"   📚 Configured for: Season-by-season progression (Season 1 first)")
                            if episode_queue:
                                print(f"   📋 Queued {len(episode_queue)} episodes for chronological download")

                        # Show alternative matches for transparency
                        alternatives = sonarr_result.get('alternatives')
                        if alternatives:
                            print("\n🔍 Alternative candidate matches (top scoring):")
                            for alt in alternatives:
                                print(f"   • {alt.get('title')} ({alt.get('year','?')}) tvdb:{alt.get('tvdbId')} score:{alt.get('score')}")
                        logger_instance.info(f"✅ Successfully added TV show to Sonarr: {metadata.get('cleaned_title') or raw_title}")

                        # === Interactive Choice: Preflight Analysis vs Sonarr Auto-Grab ===
                        print(f"\n🤔 Download Strategy Choice for: {cleaned_title}")
                        print("Choose how you want to handle downloads for this show:")
                        print("1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)")
                        print("2. ⚡ Sonarr Auto-Grab - Let Sonarr immediately search and grab based on quality profiles")
                        print("3. ⏭️  Skip - Add to Sonarr but don't start any downloads yet")

                        while True:
                            try:
                                choice = input("Enter your choice (1/2/3): ").strip()
                                if choice in ['1', '2', '3']:
                                    break
                                else:
                                    print("❌ Please enter 1, 2, or 3")
                            except (EOFError, KeyboardInterrupt):
                                print("\n⚠️ Defaulting to Preflight Analysis")
                                choice = '1'
                                break

                        if choice == '1':
                            print(f"🔬 Using Preflight Analysis for {cleaned_title}")
                            logger_instance.info(f"User selected preflight analysis for: {cleaned_title}")

                            # === Integrated Preflight Analyzer Invocation ===
                            try:
                                from preflight_analyzer.integrated_selector import preflight_for_season
                                from preflight_analyzer.sonarr_client import grab_release as _grab
                                pre_cfg_path = Path('config/preflight_config.json')
                                servers_cfg_path = Path('preflight_analyzer') / 'sample_servers.json'
                                series_id = sonarr_result.get('sonarr_id')
                                if series_id:
                                    # Group episodes by season to handle multi-season requests
                                    seasons_to_analyze = {}  # season_number -> list of episode numbers

                                if parsed_request['request_type'] == 'specific_season' and parsed_request.get('seasons'):
                                    # Full season requests
                                    for season_num in parsed_request['seasons']:
                                        seasons_to_analyze[season_num] = None  # None means all episodes in season
                                elif parsed_request['request_type'] in ('specific_episodes','multiple_episodes') and parsed_request.get('episodes'):
                                    # Group episodes by season
                                    for episode in parsed_request['episodes']:
                                        season_num = episode['season']
                                        episode_num = episode['episode']
                                        if season_num not in seasons_to_analyze:
                                            seasons_to_analyze[season_num] = []
                                        if seasons_to_analyze[season_num] is not None:  # Only append if not None (full season)
                                            seasons_to_analyze[season_num].append(episode_num)
                                elif parsed_request['request_type'] == 'full_series':
                                    # Full series requests - get ALL seasons from Sonarr
                                    print(f"🔍 Full series request detected - querying Sonarr for all seasons...")
                                    try:
                                        import urllib.request
                                        import urllib.parse
                                        import json

                                        sonarr_url = get_setting('Sonarr','url', settings_dict=settings_dict, default='http://localhost:8989')
                                        sonarr_api_key = get_setting('Sonarr','api_key', settings_dict=settings_dict)

                                        # Get all episodes for this series to determine available seasons
                                        eps_url = f"{sonarr_url.rstrip('/')}/api/v3/episode"
                                        q = urllib.parse.urlencode({'seriesId': series_id})
                                        req = urllib.request.Request(f"{eps_url}?{q}", headers={'X-Api-Key': sonarr_api_key})
                                        with urllib.request.urlopen(req, timeout=15.0) as resp:  # nosec
                                            all_episodes = json.loads(resp.read().decode('utf-8', 'replace'))

                                        # Extract unique seasons and count episodes per season
                                        season_counts = {}
                                        for ep in all_episodes:
                                            season_num = ep.get('seasonNumber')
                                            if season_num is not None and season_num > 0:  # Skip specials (season 0)
                                                if season_num not in season_counts:
                                                    season_counts[season_num] = 0
                                                season_counts[season_num] += 1

                                        if season_counts:
                                            # Add all seasons for analysis
                                            for season_num in sorted(season_counts.keys()):
                                                seasons_to_analyze[season_num] = None  # None means all episodes in season

                                            total_episodes = sum(season_counts.values())
                                            print(f"📊 Found {len(season_counts)} seasons with {total_episodes} total episodes:")
                                            for season_num in sorted(season_counts.keys()):
                                                print(f"   Season {season_num}: {season_counts[season_num]} episodes")
                                        else:
                                            print(f"⚠️ No seasons found in Sonarr - defaulting to Season 1")
                                            seasons_to_analyze[1] = None

                                    except Exception as e:
                                        print(f"⚠️ Error querying Sonarr for seasons: {e}")
                                        print(f"   Defaulting to Season 1 for safety")
                                        seasons_to_analyze[1] = None
                                else:
                                    # Fallback - Default to Season 1
                                    seasons_to_analyze[1] = None

                                print(f"🔎 Preflight analyzing {len(seasons_to_analyze)} season(s): {list(seasons_to_analyze.keys())}")
                                for season_num, episodes in seasons_to_analyze.items():
                                    if episodes:
                                        print(f"   Season {season_num}: Episodes {sorted(episodes)}")
                                    else:
                                        print(f"   Season {season_num}: All episodes")

                                # Wait for Sonarr to populate episode metadata after series addition
                                import asyncio
                                logger_instance.debug("⏳ Waiting for Sonarr to populate episode metadata...")
                                await asyncio.sleep(8)  # Give Sonarr more time to fetch episode data from TVDB

                                # Process each season and start downloads immediately after analysis
                                all_decisions = {}
                                all_acceptable_episodes = []
                                all_acceptable_packs = []
                                total_grabbed_count = 0

                                # Import download session dependencies upfront
                                import aiohttp
                                import json
                                sonarr_url = get_setting('Sonarr','url', settings_dict=settings_dict, default='http://localhost:8989')
                                sonarr_api_key = get_setting('Sonarr','api_key', settings_dict=settings_dict)
                                headers = {"X-Api-Key": sonarr_api_key}

                                # Track which episode IDs have already been queued (safety net against duplicates)
                                processed_episode_ids = set()

                                # Define immediate download callback for individual episodes
                                async def immediate_episode_download(episode_result, episode_id):
                                    """Download callback for individual episodes as soon as they're analyzed"""
                                    # Deduplicate per-episode to enforce one file per episode
                                    try:
                                        if episode_id in processed_episode_ids:
                                            return
                                    except Exception:
                                        pass
                                    guid = episode_result.get('guid')
                                    if not guid or not episode_id:
                                        print(f"   ⚠️ Skipping callback download: missing GUID or episode_id")
                                        return

                                    # Get indexer information
                                    indexer_id = episode_result.get('indexerId') or episode_result.get('indexer_id')
                                    indexer_name = episode_result.get('indexer')

                                    try:
                                        async with aiohttp.ClientSession() as session:
                                            # Map indexer name to ID if needed
                                            if indexer_id is None and indexer_name:
                                                try:
                                                    async with session.get(f"{sonarr_url}/api/v3/indexer", headers=headers) as idx_resp:
                                                        if idx_resp.status == 200:
                                                            indexers = await idx_resp.json()
                                                            for indexer in indexers:
                                                                if indexer.get('name', '').lower() == indexer_name.lower():
                                                                    indexer_id = indexer.get('id')
                                                                    break
                                                            if indexer_id is None and indexers:
                                                                indexer_id = indexers[0].get('id')  # Fallback
                                                except Exception:
                                                    pass

                                            # Use direct release download instead of EpisodeSearch
                                            grab_payload = {
                                                "guid": guid,
                                                "indexerId": indexer_id
                                            }
                                            title = episode_result.get('title', 'Unknown')

                                            async with session.post(
                                                f"{sonarr_url}/api/v3/release",
                                                headers=headers,
                                                json=grab_payload
                                            ) as resp:
                                                if resp.status in [200, 201]:
                                                    if telemetry_integrator:
                                                        print(f"   📊 Episode queued: {title} (tracking enabled)")
                                                    else:
                                                        print(f"   🚀 Immediate download started: {title}")
                                                    try:
                                                        processed_episode_ids.add(episode_id)
                                                    except Exception:
                                                        pass
                                                    return True
                                                else:
                                                    error_text = await resp.text()
                                                    print(f"   ⚠️ Immediate download failed: {resp.status}")
                                                    print(f"   🐛 DEBUG: Error response: {error_text}")
                                                    return False
                                    except Exception as e:
                                        print(f"   ⚠️ Immediate download error: {e}")
                                        return False

                                for season_num, specific_episodes_list in seasons_to_analyze.items():
                                    print(f"\n🎯 Analyzing Season {season_num}...")

                                    # Use callback for individual episodes only (not full seasons)
                                    use_callback = specific_episodes_list is not None and len(specific_episodes_list or []) <= 3

                                    decision = await preflight_for_season(
                                        config_path=pre_cfg_path,
                                        servers_config_path=servers_cfg_path if servers_cfg_path.exists() else None,
                                        sonarr_series_id=series_id,
                                        season_number=season_num,
                                        sonarr_url=sonarr_url,
                                        sonarr_api_key=sonarr_api_key,
                                        max_candidates=8,  # Reduced from 15 for faster execution
                                        sample_cap=320,    # Reduced from 500 for faster execution
                                        accept_threshold=0.50,
                                        manual_search=False,  # Disable manual search for unmonitored series to avoid HTTP 500
                                        attempt_pack=True,
                                        specific_episodes=specific_episodes_list,  # Pass specific episodes to limit analysis
                                        download_callback=immediate_episode_download if use_callback else None
                                    )

                                    all_decisions[season_num] = decision

                                    # 🔥 CRITICAL BUG FIX: Only collect ACCEPTED releases, not all analyzed releases!
                                    # Previously was downloading ALL episodes including REJECTED ones!
                                    all_episodes = decision.get('episodes', [])
                                    season_episodes = [ep for ep in all_episodes if ep.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]

                                    # 🔥 NEW FIX: Select only ONE best release per episode (lowest risk score)
                                    episodes_by_id = {}
                                    for ep in season_episodes:
                                        episode_id = ep.get('episode_id')
                                        if episode_id not in episodes_by_id:
                                            episodes_by_id[episode_id] = []
                                        episodes_by_id[episode_id].append(ep)

                                    # Pick the best release for each episode using quality-first logic
                                    final_season_episodes = []
                                    for episode_id, candidates in episodes_by_id.items():
                                        print(f"   🔧 DEBUG: Selecting best from {len(candidates)} candidates for episode {episode_id}")
                                        
                                        # Quality-first selection logic with language and consistency preferences
                                        def _pick_best_episode(cands):
                                            if not cands:
                                                return None
                                            
                                            def _resolution_score(x):
                                                title = str(x.get('title', '')).lower()
                                                if '2160p' in title or '4k' in title:
                                                    return 2160
                                                elif '1080p' in title:
                                                    return 1080
                                                elif '720p' in title:
                                                    return 720
                                                elif '480p' in title:
                                                    return 480
                                                else:
                                                    size_gb = x.get('size', 0) / (1024**3)
                                                    if size_gb > 8:
                                                        return 1080
                                                    elif size_gb > 1:
                                                        return 720
                                                    else:
                                                        return 480
                                            
                                            def _size(x):
                                                return x.get('size', 0)
                                            
                                            def _risk(x):
                                                return x.get('risk_score', float('inf'))
                                            
                                            def _language_preference(x):
                                                title = str(x.get('title', '')).upper()
                                                # Prefer English titles over German/foreign language
                                                if 'GERMAN' in title or 'DEUTSCH' in title:
                                                    return 0  # Lower preference for German
                                                else:
                                                    return 1  # Higher preference for English/other
                                            
                                            def _release_group_consistency(x):
                                                title = str(x.get('title', '')).upper()
                                                # Prefer consistent release groups (Yassmiso appears frequently)
                                                if 'YASSMISO' in title:
                                                    return 2  # High preference for Yassmiso
                                                elif 'CNHD' in title:
                                                    return 1  # Medium preference for CNHD  
                                                else:
                                                    return 0  # Lower preference for others
                                            
                                            # Filter out extremely high-risk files only
                                            max_acceptable_risk = 0.15
                                            quality_candidates = [x for x in cands if _risk(x) <= max_acceptable_risk]
                                            
                                            if not quality_candidates:
                                                return min(cands, key=_risk)  # Fallback to lowest risk
                                            
                                            # Enhanced quality scoring: Resolution > Language > Release Group > Size > Risk
                                            def quality_score(x):
                                                return (
                                                    _resolution_score(x),           # 1. Higher resolution first
                                                    _language_preference(x),        # 2. English over German (most important for consistency)
                                                    _release_group_consistency(x),  # 3. Consistent release groups (Yassmiso preferred)
                                                    _size(x),                       # 4. Larger size fourth
                                                    -_risk(x)                       # 5. Lower risk last
                                                )
                                            
                                            best = max(quality_candidates, key=quality_score)
                                            res = _resolution_score(best)
                                            size_gb = _size(best) / (1024**3)
                                            risk = _risk(best)
                                            lang_pref = "🇩🇪" if _language_preference(best) == 0 else "🇺🇸"
                                            group = "Yassmiso" if "YASSMISO" in str(best.get('title', '')).upper() else "Other"
                                            print(f"   🔧 NEW LOGIC: Selected {best.get('title', 'Unknown')[:45]}... - {res}p, {size_gb:.2f}GB, {lang_pref}, {group}, risk: {risk:.4f}")
                                            return best
                                        
                                        best_episode = _pick_best_episode(candidates)
                                        if best_episode:
                                            final_season_episodes.append(best_episode)

                                    # Also filter packs to only accepted ones AND season-specific ones
                                    all_packs = decision.get('packs', [])
                                    accepted_packs = [pack for pack in all_packs if pack.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]

                                    # 🔥 NEW FIX: Choose season-specific pack (S01 only, not S01-S05)
                                    season_specific_packs = []
                                    print(f"   🐛 DEBUG: Filtering {len(accepted_packs)} accepted packs for season {season_num}")
                                    for pack in accepted_packs:
                                        title = pack.get('title', '').lower()
                                        print(f"   🐛 DEBUG: Checking pack '{title}'")
                                        # Look for S01 but reject multi-season packs like S01-S05
                                        season_pattern = f's{season_num:02d}'
                                        has_season = season_pattern in title
                                        is_multi_season = '-s' in title or 's01-s' in title
                                        print(f"   🐛 DEBUG: Season pattern '{season_pattern}' found: {has_season}, Multi-season: {is_multi_season}")

                                        if has_season and not is_multi_season:
                                            season_specific_packs.append(pack)
                                            print(f"   🐛 DEBUG: ✅ Pack ACCEPTED: {title}")
                                        else:
                                            print(f"   🐛 DEBUG: ❌ Pack REJECTED: {title}")

                                    print(f"   🐛 DEBUG: Found {len(season_specific_packs)} season-specific packs")

                                    # Pick the best season-specific pack (if any)
                                    season_pack = None
                                    if season_specific_packs:
                                        season_pack = min(season_specific_packs, key=lambda x: x.get('risk_score', 1.0))
                                        print(f"   🐛 DEBUG: Selected best pack: {season_pack.get('title')}")
                                    else:
                                        print(f"   🐛 DEBUG: No season-specific packs found, season_pack = None")

                                    # Log results for this season
                                    stats = decision.get('stats', {})
                                    print(f"   ➤ Season {season_num}: {stats.get('episodes_covered', 0)}/{stats.get('episodes_total', 0)} episodes covered ({stats.get('accept_fraction', 0):.2%}) with {stats.get('episodes_accepted_files', 0)} files, strategy={decision.get('strategy')}")

                                    # 🚀 DOWNLOAD LOGIC: Use either callback OR main logic, never both!
                                    season_grabbed_count = 0

                                    if use_callback:
                                        # Downloads were already triggered by immediate_episode_download callback
                                        season_grabbed_count = len(final_season_episodes)
                                        if season_pack:
                                            season_grabbed_count += 1
                                        print(f"   📋 Season {season_num}: {season_grabbed_count} downloads handled by immediate callback")
                                    elif final_season_episodes or season_pack:
                                        # Use main download logic for full seasons/larger requests
                                        print(f"🚀 Starting downloads for Season {season_num} immediately...")

                                        try:
                                            async with aiohttp.ClientSession() as immediate_session:
                                                # Download individual episodes from this season using direct release GUIDs
                                                for episode in final_season_episodes:
                                                    guid = episode.get('guid')
                                                    title = episode.get('title', 'Unknown')
                                                    episode_id = episode.get('episode_id')
                                                    indexer_id = episode.get('indexerId') or episode.get('indexer_id')
                                                    indexer_name = episode.get('indexer')

                                                    if guid:
                                                        # Map indexer name to ID if needed (similar to season pack logic)
                                                        if indexer_id is None and indexer_name:
                                                            try:
                                                                async with immediate_session.get(f"{sonarr_url}/api/v3/indexer", headers=headers) as idx_resp:
                                                                    if idx_resp.status == 200:
                                                                        indexers = await idx_resp.json()
                                                                        for indexer in indexers:
                                                                            if indexer.get('name', '').lower() == indexer_name.lower():
                                                                                indexer_id = indexer.get('id')
                                                                                break
                                                                        if indexer_id is None and indexers:
                                                                            indexer_id = indexers[0].get('id')  # Fallback
                                                            except Exception:
                                                                pass

                                                        try:
                                                            # Use direct release download with full context
                                                            grab_payload = {
                                                                "guid": guid,
                                                                "indexerId": indexer_id,
                                                                "title": title,
                                                                "size": episode.get('size', 0),
                                                                "seriesId": series_id,
                                                                "episodeIds": [episode_id]
                                                            }

                                                            async with immediate_session.post(
                                                                f"{sonarr_url}/api/v3/release",
                                                                headers=headers,
                                                                json=grab_payload
                                                            ) as grab_resp:
                                                                if grab_resp.status in [200, 201]:
                                                                    print(f"   ✅ Started download: {title}")
                                                                    season_grabbed_count += 1
                                                                else:
                                                                    error_text = await grab_resp.text()
                                                                    print(f"   ⚠️ Failed to start download for {title}: {grab_resp.status}")
                                                                    print(f"   🐛 DEBUG: Error response: {error_text}")
                                                        except Exception as grab_err:
                                                            print(f"   ⚠️ Error starting download for {title}: {grab_err}")
                                                    else:
                                                        print(f"   ⚠️ Skipping {title}: missing GUID for direct download")

                                                # Download season pack if available
                                                if season_pack:
                                                    # Use the new cache synchronization function
                                                    try:
                                                        pack_success, pack_message = await download_season_pack_with_cache_sync(
                                                            session=immediate_session,
                                                            sonarr_url=sonarr_url,
                                                            headers=headers,
                                                            series_id=series_id,
                                                            season_num=season_num,
                                                            season_pack=season_pack
                                                        )

                                                        if pack_success:
                                                            print(f"   ✅ Season pack handled: {pack_message}")
                                                            season_grabbed_count += 1
                                                        else:
                                                            print(f"   ⚠️ Season pack failed: {pack_message}")
                                                    except Exception as pack_err:
                                                        print(f"   ⚠️ Error in season pack cache sync: {pack_err}")

                                                    # Log pack details for debugging
                                                    pack_title = season_pack.get('title', 'Unknown')
                                                    pack_guid = season_pack.get('guid')
                                                    print(f"   🐛 DEBUG: Pack '{pack_title}' with GUID: {pack_guid}")

                                        except Exception as season_download_err:
                                            print(f"   ⚠️ Season {season_num} download session failed: {season_download_err}")

                                        print(f"   🎯 Season {season_num}: {season_grabbed_count} downloads started")
                                    else:
                                        print(f"   ⚠️ Season {season_num}: No acceptable releases found")

                                    # Update total count for all download methods
                                    total_grabbed_count += season_grabbed_count

                                    # Still collect for overall logging and persistence (only BEST episodes, not all accepted)
                                    all_acceptable_episodes.extend(final_season_episodes)
                                    if season_pack:
                                        all_acceptable_packs.append(season_pack)

                                    # Brief pause between seasons for system stability
                                    if season_num < max(seasons_to_analyze.keys()):
                                        print(f"   ⏳ Brief pause before analyzing next season...")
                                        await asyncio.sleep(2)

                                # Create combined summary for logging
                                total_episodes = sum(d.get('stats', {}).get('episodes_total', 0) for d in all_decisions.values())
                                total_accepted = len(all_acceptable_episodes)
                                print(f"\n📊 Combined Results: {total_episodes} total episodes analyzed, {total_accepted} acceptable episodes + {len(all_acceptable_packs)} season packs found")
                                if telemetry_integrator:
                                    print(f"📊 Queue Status: {total_grabbed_count} items queued across {len(seasons_to_analyze)} season(s) (telemetry tracking enabled)")
                                else:
                                    print(f"🚀 Downloads Started: {total_grabbed_count} items queued across {len(seasons_to_analyze)} season(s)")
                                # Persist decision artifacts for each season
                                decisions_dir = Path('workspace') / 'preflight_decisions'
                                decisions_dir.mkdir(parents=True, exist_ok=True)

                                import json  # Local import for preflight decision serialization
                                for season_num, decision in all_decisions.items():
                                    artifact_path = decisions_dir / f"{cleaned_title.replace(' ','_')}_S{season_num:02d}.json"
                                    artifact_path.write_text(json.dumps(decision, indent=2), encoding='utf-8')
                                    print(f"📝 Season {season_num} decision saved: {artifact_path}")

                                # Summary of preflight-driven episode selection and grabbing
                                if total_grabbed_count > 0:
                                    print(f"✅ Preflight found and started downloads for {len(all_acceptable_episodes)} episodes + {len(all_acceptable_packs)} packs across {len(all_decisions)} season(s)")

                                    # Display preflight choices with sizes for logging purposes
                                    print("\n🔬 Preflight Episode Selections (already downloading):")
                                    total_size_gb = 0
                                    for i, episode in enumerate(all_acceptable_episodes, 1):
                                        title = episode.get('title', 'Unknown')
                                        size_bytes = episode.get('size', 0)
                                        size_gb = size_bytes / (1024**3) if size_bytes else 0
                                        total_size_gb += size_gb
                                        risk_score = episode.get('risk_score', 0)
                                        decision = episode.get('decision', 'Unknown')
                                        missing_ratio = episode.get('probe_missing_ratio', 0)
                                        # Ensure missing_ratio is safe for formatting
                                        safe_missing_ratio = missing_ratio if missing_ratio is not None else 0.0
                                        print(f"   #{i}. 📺 {title}")
                                        print(f"       💾 Size: {size_gb:.2f} GB ({size_bytes:,} bytes)")
                                        print(f"       ⚡ Risk: {risk_score:.4f} | Missing: {safe_missing_ratio:.1%} | Decision: {decision}")

                                    for i, pack in enumerate(all_acceptable_packs, len(all_acceptable_episodes) + 1):
                                        title = pack.get('title', 'Unknown')
                                        size_bytes = pack.get('size', 0)
                                        size_gb = size_bytes / (1024**3) if size_bytes else 0
                                        total_size_gb += size_gb
                                        risk_score = pack.get('risk_score', 0)
                                        decision = pack.get('decision', 'Unknown')
                                        missing_ratio = pack.get('probe_missing_ratio', 0)
                                        # Ensure missing_ratio is safe for formatting
                                        safe_missing_ratio = missing_ratio if missing_ratio is not None else 0.0
                                        print(f"   #{i}. 📦 {title}")
                                        print(f"       💾 Size: {size_gb:.2f} GB ({size_bytes:,} bytes)")
                                        print(f"       ⚡ Risk: {risk_score:.4f} | Missing: {safe_missing_ratio:.1%} | Decision: {decision}")

                                    print(f"\n📊 Preflight Summary: {len(all_acceptable_episodes)} episodes + {len(all_acceptable_packs)} packs | Total: {total_size_gb:.2f} GB")
                                    print("🎯 Downloads started immediately after each season analysis")
                                    print("   (No waiting for all analysis to complete - optimal efficiency!)")

                                    grabbed = total_grabbed_count  # Use our immediate download count
                                else:
                                    print("⚠️ No acceptable releases found in preflight analysis")
                                    grabbed = 0

                                # Note: Series monitoring is now handled within the preflight section above
                                if grabbed == 0:
                                    logger_instance.info("⚠️ No releases queued by preflight - enabling basic monitoring for future episodes")
                                    print("⚠️ No suitable releases found - enabling basic monitoring for future episodes")

                                    # CRITICAL FIX: Enable basic monitoring even if preflight fails
                                    # This prevents the "shows show as unmonitored" issue (Priority 5)
                                    try:
                                        series_id = sonarr_result.get("sonarr_id")
                                        if series_id:
                                            import aiohttp
                                            sonarr_url = get_setting('Sonarr','url', settings_dict=settings_dict, default='http://localhost:8989')
                                            sonarr_api_key = get_setting('Sonarr','api_key', settings_dict=settings_dict)
                                            headers = {"X-Api-Key": sonarr_api_key}

                                            async with aiohttp.ClientSession() as safety_session:
                                                # Get current series data
                                                async with safety_session.get(f"{sonarr_url}/api/v3/series/{series_id}", headers=headers) as get_resp:
                                                    if get_resp.status == 200:
                                                        series_data = await get_resp.json()
                                                        series_data["monitored"] = True  # Enable basic series monitoring

                                                        # Update series to ensure it's monitored for future releases
                                                        async with safety_session.put(f"{sonarr_url}/api/v3/series/{series_id}",
                                                                                     headers=headers, json=series_data) as update_resp:
                                                            if update_resp.status in [200, 202]:
                                                                logger_instance.info("   ✅ Enabled basic monitoring - will detect future releases")
                                                                print("   ✅ Basic monitoring enabled for future episodes/seasons")
                                                            else:
                                                                logger_instance.warning(f"   ⚠️ Failed to enable basic monitoring: {update_resp.status}")
                                                    else:
                                                        logger_instance.warning(f"   ⚠️ Could not enable basic monitoring: {get_resp.status}")
                                        else:
                                            logger_instance.warning("   ⚠️ No series ID available for monitoring safety net")
                                    except Exception as monitor_err:
                                        error_msg = str(monitor_err) if monitor_err else "Unknown error"
                                        logger_instance.warning(f"   ⚠️ Monitoring safety net failed: {error_msg}")
                                else:
                                    logger_instance.warning("Preflight skipped: missing Sonarr series id")
                            except Exception as preflight_err:
                                # Safely convert exception to string to avoid format string errors with None values
                                error_msg = str(preflight_err) if preflight_err else "Unknown error"
                                logger_instance.warning(f"Preflight analyzer integration failed: {error_msg}")

                        elif choice == '2':
                            print(f"⚡ Using Sonarr Auto-Grab for {cleaned_title}")
                            logger_instance.info(f"User selected Sonarr auto-grab for: {cleaned_title}")

                            # Enable immediate monitoring and search
                            try:
                                import aiohttp
                                sonarr_url = get_setting('Sonarr','url', settings_dict=settings_dict, default='http://localhost:8989')
                                sonarr_api_key = get_setting('Sonarr','api_key', settings_dict=settings_dict)
                                headers = {"X-Api-Key": sonarr_api_key}
                                series_id = sonarr_result.get('sonarr_id')

                                if series_id:
                                    async with aiohttp.ClientSession() as auto_session:
                                        # Get current series data
                                        async with auto_session.get(f"{sonarr_url}/api/v3/series/{series_id}", headers=headers) as get_resp:
                                            if get_resp.status == 200:
                                                series_data = await get_resp.json()
                                                series_data["monitored"] = True  # Enable series monitoring

                                                # Update series to enable monitoring
                                                async with auto_session.put(f"{sonarr_url}/api/v3/series/{series_id}",
                                                                           headers=headers, json=series_data) as update_resp:
                                                    if update_resp.status in [200, 202]:
                                                        print("✅ Enabled series monitoring")
                                                        logger_instance.info("   ✅ Enabled series monitoring for auto-grab")

                                                        # Get all episodes and enable monitoring based on request specificity
                                                        async with auto_session.get(f"{sonarr_url}/api/v3/episode",
                                                                                   headers=headers, params={"seriesId": series_id}) as ep_resp:
                                                            if ep_resp.status == 200:
                                                                episodes = await ep_resp.json()
                                                                episodes_to_monitor = []

                                                                if parsed_request['request_type'] == 'specific_episodes':
                                                                    # Monitor only specific episodes
                                                                    for req_ep in parsed_request.get('episodes', []):
                                                                        for ep in episodes:
                                                                            if (ep.get("seasonNumber") == req_ep.get('season') and
                                                                                ep.get("episodeNumber") == req_ep.get('episode')):
                                                                                episodes_to_monitor.append(ep["id"])
                                                                elif parsed_request['request_type'] == 'specific_season':
                                                                    # Monitor all episodes in specific seasons
                                                                    for season_num in parsed_request.get('seasons', []):
                                                                        for ep in episodes:
                                                                            if ep.get("seasonNumber") == season_num:
                                                                                episodes_to_monitor.append(ep["id"])
                                                                else:
                                                                    # Full series - monitor Season 1 first
                                                                    for ep in episodes:
                                                                        if ep.get("seasonNumber") == 1:
                                                                            episodes_to_monitor.append(ep["id"])

                                                                if episodes_to_monitor:
                                                                    # Enable monitoring for selected episodes
                                                                    sonarr = SonarrClient(sonarr_url, headers.get('X-Api-Key', ''))
                                                                    ok = await sonarr.set_episodes_monitor_state(auto_session, episodes_to_monitor, True)
                                                                    if ok:
                                                                        print(f"✅ Enabled monitoring for {len(episodes_to_monitor)} episodes")

                                                                        # Trigger immediate series search
                                                                        search_payload = {"name": "SeriesSearch", "seriesId": series_id}
                                                                        cmd = await sonarr.issue_command(auto_session, search_payload)
                                                                        if cmd and isinstance(cmd, dict) and cmd.get('id'):
                                                                            print("🔍 Triggered immediate series search - downloads should start soon!")
                                                                            logger_instance.info(f"Triggered immediate series search for {series_id}")
                                                                        else:
                                                                            print("⚠️ Search trigger failed, but monitoring is enabled")
                                                                    else:
                                                                        print("⚠️ Episode monitoring failed")
                                                                else:
                                                                    print("⚠️ No episodes found to monitor")
                                                    else:
                                                        print("⚠️ Failed to enable series monitoring")
                                            else:
                                                print("⚠️ Failed to get series data for auto-grab")
                                else:
                                    print("⚠️ No series ID available for auto-grab")
                            except Exception as auto_err:
                                error_msg = str(auto_err) if auto_err else "Unknown error"
                                logger_instance.warning(f"Auto-grab setup failed: {error_msg}")
                                print(f"⚠️ Auto-grab setup failed: {error_msg}")

                        else:  # choice == '3'
                            print(f"⏭️ Skipping downloads for {cleaned_title} - added to Sonarr but no downloads started")
                            logger_instance.info(f"User selected skip downloads for: {cleaned_title}")
                            # Series is already added to Sonarr but remains unmonitored

                    else:
                        print(f"❌ Failed to add TV show to Sonarr: {sonarr_result.get('reason', 'Unknown error')}")
                        logger_instance.warning(f"⚠️ Failed to add TV show to Sonarr: {sonarr_result.get('reason', 'unknown')}")
                        # Still count as success since metadata was processed

                    success_count += 1

                    # Build compact dashboard row (Sonarr status check)
                    if compact_output:
                        try:
                            import aiohttp
                            sonarr_url = get_setting("Sonarr", "url", settings_dict=settings_dict, default="http://localhost:8989")
                            sonarr_api_key = get_setting("Sonarr", "api_key", settings_dict=settings_dict)
                            headers = {"X-Api-Key": sonarr_api_key} if sonarr_api_key else {}
                            series_id = sonarr_result.get("sonarr_id")
                            season_no = parsed_request.get("season")
                            episode_no = parsed_request.get("episode")
                            ep_id = None
                            async with aiohttp.ClientSession() as s:
                                # Resolve episode id
                                if series_id and season_no and episode_no:
                                    async with s.get(f"{sonarr_url}/api/v3/episode", headers=headers, params={"seriesId": series_id}) as ep_resp:
                                        eps = await ep_resp.json() if ep_resp.status == 200 else []
                                    for ep in eps:
                                        if ep.get("seasonNumber") == season_no and ep.get("episodeNumber") == episode_no:
                                            ep_id = ep.get("id")
                                            break
                                # Check queue for this episode
                                queued = None
                                if ep_id:
                                    async with s.get(f"{sonarr_url}/api/v3/queue", headers=headers, params={"page":1, "pageSize":250}) as q_resp:
                                        qj = await q_resp.json() if q_resp.status == 200 else {}
                                    records = qj.get("records") if isinstance(qj, dict) else (qj or [])
                                    for rec in records:
                                        epi = rec.get("episode") or {}
                                        if epi.get("id") == ep_id:
                                            queued = rec
                                            break
                                # If not queued, check releases approved count quickly
                                status = "Unknown"
                                detail = ""
                                if queued:
                                    qqual = ((queued.get("quality") or {}).get("quality") or {}).get("name")
                                    size_mb = (queued.get("size") or 0) / (1024*1024)
                                    status = "Queued/Grabbing"
                                    detail = f"{qqual} {size_mb:.1f} MB"
                                elif ep_id:
                                    async with s.get(f"{sonarr_url}/api/v3/release", headers=headers, params={"episodeId": ep_id}) as rel_resp:
                                        rels = await rel_resp.json() if rel_resp.status == 200 else []
                                    approved = [r for r in rels if r.get("approved")]
                                    if approved:
                                        status = "Approved but not queued"
                                        top = max(approved, key=lambda r: r.get("size") or 0)
                                        qn = (((top.get("quality") or {}).get("quality") or {}).get("name"))
                                        size_mb = (top.get("size") or 0) / (1024*1024)
                                        detail = f"{qn} {size_mb:.1f} MB"
                                    else:
                                        status = "No approved candidates"
                                        # Summarize top rejection reason
                                        rejs = []
                                        for r in rels:
                                            for rr in (r.get("rejections") or []):
                                                rejs.append(rr)
                                        if rejs:
                                            from collections import Counter
                                            reason, cnt = Counter(rejs).most_common(1)[0]
                                            detail = reason
                                dashboard_rows.append({
                                    "title": parsed_request['display_title'],
                                    "status": status,
                                    "detail": detail
                                })
                        except Exception:
                            pass

                    # Store enhanced TV metadata with specificity info
                    tvdb_id = str(metadata.get('tvdb_id') or metadata.get('tmdb_id', 'unknown'))
                    title_year_id = f"{cleaned_title} ({year})"

                    # Store with enhanced metadata including specificity
                    enhanced_metadata = {
                        **metadata,
                        'raw_title_input': raw_title,
                        'content_type': 'tv_show',
                        'request_specificity': parsed_request['request_type'],
                        'parsed_request': parsed_request,
                        'sonarr_params': parsed_request['sonarr_params']
                    }

                    success = metadata_db.save_movie_metadata(  # Using correct method name
                        unique_id=title_year_id,
                        title=cleaned_title,
                        year=year if isinstance(year, int) else None,
                        tmdb_id=tvdb_id,
                        imdb_id=metadata.get('imdb_id'),
                        metadata=enhanced_metadata
                    )

                    if success:
                        logger_instance.info(f"Stored enhanced TV metadata for: {title_year_id}")
                    else:
                        logger_instance.warning(f"Failed to store TV metadata for: {title_year_id}")

                else:
                    print(f"❌ Failed to get TV metadata for: {parsed_request['show_title']}")
                    logger_instance.error(f"Failed to get TV metadata for: {parsed_request['show_title']}")

            except Exception as e:
                print(f"❌ Error processing {raw_title}: {e}")
                logger_instance.error(f"Error processing TV show {raw_title}: {e}")

    finally:
        metadata_db.close()

    # Print compact dashboard summary
    if compact_output and dashboard_rows:
        print(f"\n{'='*60}")
        print("📊 TV Download Summary (compact)")
        print(f"{'='*60}")
        for row in dashboard_rows:
            print(f"• {row['title']} → {row['status']}{(' — ' + row['detail']) if row.get('detail') else ''}")

    return success_count


# Telemetry helper functions for download verification
async def verify_all_tracked_downloads(telemetry_integrator, logger):
    """
    Verify all downloads tracked during this session actually started.
    This replaces guesswork with real verification.
    """
    if not telemetry_integrator:
        return

    active_count = telemetry_integrator.get_active_download_count()

    if active_count == 0:
        print("\n✅ No downloads to verify")
        return

    # Check if early monitoring is already running
    global _early_monitoring_started
    if _early_monitoring_started:
        print(f"\n✅ Early telemetry monitoring already active for {active_count} downloads")
        print("   Monitoring started when first download was added - no need to start again")
        success = True  # Assume success since monitoring is already running
    else:
        print(f"\n🔍 Verifying {active_count} downloads actually started...")
        print("   This replaces guesswork with real verification!")

        try:
            # Monitor downloads until completion (no timeout to prevent premature termination)
            success = await telemetry_integrator.monitor_downloads(interval=10, timeout=None)
        except Exception as e:
            print(f"⚠️  Error during download verification: {e}")
            logger.error(f"Telemetry verification error: {e}")
            success = False

    if success:
        print("🎉 All downloads verified and started successfully!")
        logger.info("Telemetry verification: All downloads confirmed")
        
        # Phase 1: Enhanced download verification with accurate correlation
        print("\n🔬 Running Phase 1 enhanced download verification...")
        print("   Checking actual downloads via Radarr API for accurate status...")
        
        try:
            # Confirm downloads via Radarr API (Phase 1 improvement)
            confirmed_downloads = await telemetry_integrator.confirm_downloads_via_radarr_api()
            
            if confirmed_downloads:
                print(f"\n✅ Phase 1 Results: {len(confirmed_downloads)} downloads confirmed by Radarr API")
                for radarr_id, details in confirmed_downloads.items():
                    scene_group = details.get('scene_group', 'Unknown')
                    quality = details.get('detected_quality', 'Unknown')
                    print(f"   📁 {details['movie_title']} → {scene_group} ({quality})")
                
                # Get enhanced status report
                status_report = telemetry_integrator.get_enhanced_status_report()
                success_rate = status_report.get('success_rate', 0.0)
                mapping_accuracy = status_report.get('mapping_accuracy', 0.0)
                
                print(f"\n📊 Phase 1 Accuracy Metrics:")
                print(f"   🎯 Actual success rate: {success_rate:.1%}")
                print(f"   🔗 ID correlation accuracy: {mapping_accuracy:.1%}")
                
                if success_rate >= 0.8:
                    print(f"   🎉 Excellent download success rate!")
                elif success_rate >= 0.6:
                    print(f"   👍 Good download success rate")
                else:
                    print(f"   ⚠️ Some downloads may need attention")
                    
                logger.info(f"Phase 1: Confirmed {len(confirmed_downloads)} downloads with {success_rate:.1%} success rate")
            else:
                print(f"   ⏳ No downloads confirmed yet - may still be processing")
                logger.info("Phase 1: No confirmed downloads found yet")
                
        except Exception as e:
            print(f"   ⚠️ Phase 1 verification error: {e}")
            logger.error(f"Phase 1 verification error: {e}")
                
    else:
        print("⚠️  Some downloads may not have started properly")
        print("   Check logs for details on specific failures")
        logger.warning("Telemetry verification: Some downloads may have failed to start")

# Global flag to track if early monitoring has started
_early_monitoring_started = False

async def start_early_telemetry_monitoring_if_needed(telemetry_integrator, logger):
    """
    Start the EXACT SAME telemetry monitoring as Script 1, but in a detached process.
    This uses the standalone telemetry monitor that copies Script 1's system detail for detail.
    """
    global _early_monitoring_started

    if _early_monitoring_started:
        return  # Already started

    if not telemetry_integrator:
        logger.warning("⚠️ Cannot start early monitoring - telemetry not available")
        return

    try:
        _early_monitoring_started = True
        logger.info("🚀 EARLY TELEMETRY: Starting detached telemetry monitor (same system as Script 1)")
        print("🚀 Starting detached telemetry monitoring NOW (same dashboard, independent process)")

        # Launch the standalone telemetry monitor that uses the EXACT SAME system
        import subprocess
        import sys
        from pathlib import Path

        monitor_script = Path(__file__).parent / 'standalone_telemetry_monitor.py'
        if monitor_script.exists():
            logs_dir = Path('logs')
            logs_dir.mkdir(parents=True, exist_ok=True)
            log_path = logs_dir / 'standalone_telemetry_monitor.log'

            print(f"🧭 Starting standalone telemetry monitor (logs: {log_path})...")

            with open(log_path, 'a', encoding='utf-8') as logf:
                # Write header to log file
                logf.write(f"\n=== STANDALONE TELEMETRY MONITOR STARTED ===\n")
                logf.write(f"Started by Script 1 when first download was detected\n")
                logf.write(f"Uses the EXACT SAME telemetry system as Script 1\n")
                logf.write(f"==================================================\n\n")
                logf.flush()

                # Start the standalone monitor
                subprocess.Popen([
                    sys.executable, str(monitor_script)
                ], cwd=str(Path(__file__).parent), stdout=logf, stderr=subprocess.STDOUT)

            logger.info("✅ Standalone telemetry monitor started - same system, detached process")
            print("✅ Telemetry monitoring active - will continue after Script 1 completes")
            print(f"   📄 Monitor logs: {log_path}")

        else:
            logger.error("❌ Standalone telemetry monitor script not found")
            print("❌ Standalone telemetry monitor script not found")
            _early_monitoring_started = False

    except Exception as e:
        logger.error(f"❌ Failed to start standalone telemetry monitoring: {e}")
        print(f"❌ Failed to start telemetry monitoring: {e}")
        _early_monitoring_started = False

async def track_movie_success_with_telemetry(result, metadata, telemetry_integrator, logger):
    """
    Enhanced movie tracking with Phase 1 correlation fixes for accurate status reporting.
    """
    if result.get('success'):
        title = f"{metadata.get('cleaned_title', '')} ({metadata.get('year', '')})"

        if result.get('reason') == 'already_exists':
            print(f"✅ Movie already exists in Radarr: {title}")

        elif result.get('reason') == 'successfully_added':
            # Extract info for enhanced telemetry tracking
            radarr_movie = result.get('radarr_movie', {})
            radarr_movie_id = radarr_movie.get('id') if radarr_movie else None
            # Fallback: many paths only return 'radarr_id'
            if not radarr_movie_id:
                radarr_movie_id = result.get('radarr_id')
            quality_profile = result.get('quality_profile_name', 'Unknown')

            if telemetry_integrator and radarr_movie_id:
                # Extract NZB filename from candidate details for better display
                candidate_details = metadata.get('selected_candidate', {})
                nzb_filename = candidate_details.get('title', None)  # The candidate title is the NZB filename
                
                # Phase 1: Enhanced tracking with movie ID correlation
                job_id = telemetry_integrator.track_movie_download(
                    title=title,
                    radarr_id=radarr_movie_id,
                    quality=quality_profile,
                    nzb_filename=nzb_filename
                )
                
                # ========== INTELLIGENT FALLBACK INTEGRATION ==========
                # Store candidate details for potential fallback
                candidate_details = metadata.get('selected_candidate', {})
                user_selection_index = metadata.get('user_selection_index')
                original_recommendation_index = metadata.get('original_recommendation_index')
                
                if telemetry_integrator.telemetry and candidate_details:
                    telemetry_integrator.telemetry.store_movie_candidate_info(
                        radarr_id=radarr_movie_id,
                        candidate_details=candidate_details,
                        user_selection_index=user_selection_index,
                        original_system_recommendation_index=original_recommendation_index
                    )
                    logger.info(f"📄 Candidate info stored for fallback protection: {title}")
                # =====================================================
                
                print(f"📊 Movie queued for download: {title}")
                print(f"   🔬 Enhanced tracking: {job_id[:8]}...")
                print(f"   🆔 Radarr ID: {radarr_movie_id}")
                print(f"   🛡️ Fallback protection: Enabled")
                logger.info(f"Phase 1: Enhanced telemetry job started: {job_id} for movie {radarr_movie_id}")
                logger.info(f"Movie ID {radarr_movie_id} tracked for accurate correlation")

                # START EARLY MONITORING: Begin telemetry monitoring immediately after first movie is tracked
                await start_early_telemetry_monitoring_if_needed(telemetry_integrator, logger)
            else:
                # Fallback to static message if telemetry not available or ID missing
                print(f"✅ Movie added to Radarr: {title}")
                if not radarr_movie_id:
                    logger.warning(f"No Radarr movie ID available for enhanced tracking: {title}")
                print(f"   📥 Waiting for your choice (preflight/auto-grab/skip) — no search triggered yet")

async def track_tv_show_success_with_telemetry(result, metadata, telemetry_integrator, logger):
    """
    Replace static "✅ TV show successfully added to Sonarr!" with real telemetry tracking.
    """
    if result.get('success'):
        title = metadata.get('cleaned_title') or metadata.get('title', 'Unknown Show')
        year = metadata.get('year', '')
        if year:
            title = f"{title} ({year})"

        if result.get('reason') == 'already_exists':
            print(f"✅ TV show already exists in Sonarr: {title}")

        elif result.get('reason') == 'successfully_added':
            # Extract info for telemetry tracking
            sonarr_series_id = result.get('sonarr_id')
            episode_id = result.get('episode_id')  # If specific episode
            quality_profile = result.get('quality_profile_name', 'Unknown')

            if telemetry_integrator and sonarr_series_id:
                # Track with telemetry for real verification
                job_id = telemetry_integrator.track_episode_download(
                    title=title,
                    sonarr_id=sonarr_series_id,
                    episode_id=episode_id,
                    quality=quality_profile
                )
                print(f"📊 TV show queued for download: {title}")
                print(f"   🔬 Real-time tracking: {job_id[:8]}...")
                if episode_id:
                    print(f"   📺 Specific episode tracking enabled")
                else:
                    print(f"   📺 Series-wide tracking enabled")
                logger.info(f"Telemetry job started: {job_id} for series {sonarr_series_id}")

                # START EARLY MONITORING: Begin telemetry monitoring immediately after first TV show is tracked
                await start_early_telemetry_monitoring_if_needed(telemetry_integrator, logger)
            else:
                # Fallback to static message if telemetry not available
                print(f"✅ TV show added to Sonarr: {title}")
                print(f"   📥 Automatic search enabled")


async def interactive_main():
        """Interactive main function with content type and item selection"""
        try:
            # Setup logging for standalone execution
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            standalone_logger = logging.getLogger("interactive_pipeline_01")
            standalone_logger.info("===== Starting Interactive Pipeline 01 Execution =====")

            # Load settings
            from utils.common_helpers import load_settings
            settings_dict = load_settings("_internal/config/settings.ini")
            standalone_logger.info("Settings loaded successfully")

            # Start Script 2 monitor automatically (organize-as-you-go)
            try:
                auto_start = _get('UI', 'auto_start_stage2_monitor', settings_dict=settings_dict, default=False)
                if not isinstance(auto_start, bool):
                    auto_start = str(auto_start).lower() in ("1","true","yes","on")
                if auto_start:
                    import subprocess, sys, os
                    script2 = Path(__file__).parent / '02_download_and_organize.py'
                    if script2.exists():
                        logs_dir = Path('_internal/logs'); logs_dir.mkdir(parents=True, exist_ok=True)
                        log_path = logs_dir / 'stage2_background.log'
                        print(f"🧭 Starting Stage 02 monitor in background (logs: {log_path})...")
                        logf = open(log_path, 'a', encoding='utf-8')
                        subprocess.Popen([sys.executable, str(script2), "--monitor"], cwd=str(Path(__file__).parent), stdout=logf, stderr=subprocess.STDOUT)
                    else:
                        standalone_logger.warning("Stage 02 script not found; skipping auto-start")
            except Exception as e:
                standalone_logger.warning(f"⚠️ Failed to evaluate auto-start for Stage 02: {e}")

            # Load display and search configuration settings
            from utils.common_helpers import get_setting as _get
            
            # Telemetry display mode configuration
            telemetry_verbose = _get('TELEMETRY', 'verbose_mode', settings_dict=settings_dict, default=False)
            if not isinstance(telemetry_verbose, bool):
                telemetry_verbose = str(telemetry_verbose).lower() in ("1", "true", "yes", "on")
            
            # Search configuration settings
            max_search_candidates = int(_get('SEARCH', 'max_candidates', settings_dict=settings_dict, default='50'))
            quality_fallback_enabled = _get('SEARCH', 'enable_quality_fallback', settings_dict=settings_dict, default=True)
            if not isinstance(quality_fallback_enabled, bool):
                quality_fallback_enabled = str(quality_fallback_enabled).lower() in ("1", "true", "yes", "on")
            
            standalone_logger.info(f"Configuration: max_candidates={max_search_candidates}, quality_fallback={quality_fallback_enabled}, telemetry_verbose={telemetry_verbose}")

            # Initialize telemetry for real-time download monitoring (EARLY INITIALIZATION)
            telemetry_integrator = None
            try:
                telemetry_integrator = TelemetryIntegrator(settings_dict, standalone_logger, verbose_mode=telemetry_verbose)
                await telemetry_integrator.__aenter__()
                standalone_logger.info("🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring")
                if telemetry_verbose:
                    print("🔬 Real-time download monitoring enabled (verbose mode) - will start monitoring as soon as first download begins")
                else:
                    print("🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins")
            except Exception as e:
                standalone_logger.warning(f"⚠️ Telemetry initialization failed: {e}")
                print("⚠️ Running without real-time monitoring (static messages only)")

            # Interactive content type selection
            content_type_choice = display_interactive_menu()

            if content_type_choice == 'quit':
                print(f"👋 Exiting...")
                return

            total_movies_processed = 0
            total_tv_shows_processed = 0

            # Process based on user choice
            if content_type_choice in ['movies', 'both']:
                # Load and select movies
                all_movies = read_content_requests('movies', settings_dict)

                if all_movies:
                    selected_movie_indices = display_content_selection(all_movies, 'movies')

                    if selected_movie_indices:
                        selected_movies = [all_movies[i] for i in selected_movie_indices]
                        movies_processed = await process_selected_movies(
                            selected_movies, settings_dict, standalone_logger, telemetry_integrator,
                            max_search_candidates=max_search_candidates,
                            quality_fallback_enabled=quality_fallback_enabled
                        )
                        total_movies_processed = movies_processed
                    else:
                        print(f"📭 No movies selected for processing")
                else:
                    print(f"📭 No movies found in requests file")

            if content_type_choice in ['tv_shows', 'both']:
                # Load and select TV shows with specificity support
                all_tv_shows = read_content_requests('tv_shows', settings_dict)

                if all_tv_shows:
                    selected_tv_indices = display_tv_show_selection_with_specificity(all_tv_shows)

                    if selected_tv_indices:
                        selected_tv_shows = [all_tv_shows[i] for i in selected_tv_indices]
                        tv_shows_processed = await process_selected_tv_shows(selected_tv_shows, settings_dict, standalone_logger, telemetry_integrator)
                        total_tv_shows_processed = tv_shows_processed
                    else:
                        print(f"📭 No TV shows selected for processing")
                else:
                    print(f"📭 No TV shows found in requests file")

            # Final summary
            print(f"\n{'='*60}")
            print(f"📊 Processing Complete!")
            print(f"{'='*60}")
            print(f"🎬 Movies processed: {total_movies_processed}")
            print(f"📺 TV shows processed: {total_tv_shows_processed}")
            print(f"📊 Total content processed: {total_movies_processed + total_tv_shows_processed}")

            # NEW: Verify all downloads with telemetry
            if telemetry_integrator:
                await verify_all_tracked_downloads(telemetry_integrator, standalone_logger)

            if total_movies_processed > 0:
                if telemetry_integrator:
                    print(f"\n🎬 Movies queued and verified with real-time monitoring")
                else:
                    print(f"\n🎬 Movies have been added to Radarr and should begin downloading automatically")
                print(f"📥 Check Radarr for download progress")

            if total_tv_shows_processed > 0:
                if telemetry_integrator:
                    print(f"\n📺 TV shows queued and verified with real-time monitoring")
                else:
                    print(f"\n📺 TV shows have been added to Sonarr and should begin downloading automatically")
                print(f"📥 Check Sonarr for download progress - specific episodes/seasons configured as requested")
                print(f"🎯 Episode-specific requests will trigger targeted downloads only")

            # Cleanup telemetry (but keep session alive if early monitoring is active)
            if telemetry_integrator:
                global _early_monitoring_started
                if _early_monitoring_started:
                    standalone_logger.info("🔬 Script 1 complete - telemetry session kept alive for early monitoring")
                    print("📊 Script 1 complete - telemetry monitoring continues in background")
                    print("   Dashboard will keep updating until downloads finish")
                    # DON'T call __aexit__ - keep the session alive for background monitoring
                else:
                    try:
                        await telemetry_integrator.__aexit__(None, None, None)
                        standalone_logger.info("🔬 Telemetry session completed")
                    except Exception:
                        pass

            standalone_logger.info("===== Interactive Pipeline 01 Execution Complete =====")

        except Exception as e:
            print(f"❌ Critical error in interactive execution: {e}")
            standalone_logger.error(f"Critical error in interactive execution: {e}")
            import traceback
            traceback.print_exc()

# Handle command line arguments or run interactively
if __name__ == "__main__":
    # Start terminal output logging
    with start_terminal_logging("01_intake_and_nzb_search"):
        import argparse

        # Check if we have any command line arguments
        if len(sys.argv) > 1:
            # Command line mode
            parser = argparse.ArgumentParser(description='PlexMovieAutomator - Intake and NZB Search')
            parser.add_argument('--movies-only', action='store_true',
                               help='Process only movies (non-interactive)')
            parser.add_argument('--tv-only', action='store_true',
                               help='Process only TV shows (non-interactive)')
            parser.add_argument('--all', action='store_true',
                               help='Process all content (non-interactive)')
            parser.add_argument('--interactive', action='store_true',
                               help='Force interactive mode')

            args = parser.parse_args()

            if args.interactive or not any([args.movies_only, args.tv_only, args.all]):
                # Run interactive mode
                asyncio.run(interactive_main())
            else:
                # Run non-interactive mode (original behavior)
                print(f"🤖 Running in non-interactive mode...")
                # You can implement batch processing here if needed
                asyncio.run(interactive_main())  # For now, still use interactive
        else:
            # No command line arguments - run interactive mode
            asyncio.run(interactive_main())