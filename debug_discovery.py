#!/usr/bin/env python3
"""Debug script to check movie discovery"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd() / '_internal'))
from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager

# Initialize manager
workspace_root = Path.cwd()
filesystem_manager = FilesystemFirstStateManager(workspace_root)

# Discover movies 
print("Discovering movies by stage...")
movies_by_stage = filesystem_manager.discover_movies_by_stage()

print('Movies discovered by stage:')
for stage, movies in movies_by_stage.items():
    if movies:
        print(f'{stage}: {len(movies)} movies')
        for movie in movies[:3]:  # Show first 3 movies per stage
            print(f'  - {movie.get("title", "Unknown")} ({movie.get("year", "")})')
            print(f'    Path: {movie.get("movie_directory", "No path")}')
    else:
        print(f'{stage}: 0 movies')

print("\nDiscovering TV episodes by stage...")
tv_episodes_by_stage = filesystem_manager.discover_tv_episodes_by_stage()

print('TV episodes discovered by stage:')
for stage, episodes in tv_episodes_by_stage.items():
    if episodes:
        print(f'{stage}: {len(episodes)} episodes')
        for episode in episodes[:3]:  # Show first 3 episodes per stage
            print(f'  - {episode.get("series_title", "Unknown")} S{episode.get("season_number", 0):02d}E{episode.get("episode_number", 0):02d}')
            print(f'    Path: {episode.get("episode_directory", "No path")}')
    else:
        print(f'{stage}: 0 episodes')