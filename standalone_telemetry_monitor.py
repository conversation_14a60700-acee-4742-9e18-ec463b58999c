#!/usr/bin/env python3
"""
Standalone Telemetry Monitor - Detached from Script 1
This is an EXACT COPY of the telemetry system from Script 1, just detached to run independently.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Fix encoding issues for Windows
if os.name == 'nt':  # Windows
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# EXACT SAME IMPORTS as Script 1
from _internal.utils.telemetry_integration import TelemetryIntegrator
from _internal.utils.common_helpers import load_settings

async def run_standalone_telemetry_monitor():
    """
    Run the EXACT SAME telemetry monitoring that Script 1 uses, but independently.
    This is copied detail for detail from Script 1's working telemetry system.
    """
    
    # EXACT SAME logger setup as Script 1
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    standalone_logger = logging.getLogger("standalone_telemetry_monitor")
    
    try:
        standalone_logger.info("===== Starting Standalone Telemetry Monitor =====")
        standalone_logger.info("This uses the EXACT SAME telemetry system as Script 1")
        
        # EXACT SAME settings loading as Script 1
        settings_dict = load_settings("_internal/config/settings.ini")
        standalone_logger.info("Settings loaded successfully")
        
        # EXACT SAME telemetry verbose mode detection as Script 1
        from _internal.utils.common_helpers import get_setting as _get
        telemetry_verbose = _get('TELEMETRY', 'verbose_mode', settings_dict=settings_dict, default=False)
        if not isinstance(telemetry_verbose, bool):
            telemetry_verbose = str(telemetry_verbose).lower() in ("1", "true", "yes", "on")
        
        standalone_logger.info(f"Telemetry verbose mode: {telemetry_verbose}")
        
        # EXACT SAME telemetry initialization as Script 1 (lines 6759-6768)
        telemetry_integrator = None
        try:
            telemetry_integrator = TelemetryIntegrator(settings_dict, standalone_logger, verbose_mode=telemetry_verbose)
            await telemetry_integrator.__aenter__()
            standalone_logger.info("🔬 Real-time telemetry initialized - ready for monitoring")
            if telemetry_verbose:
                print("Real-time download monitoring enabled (verbose mode)")
            else:
                print("Real-time download monitoring enabled (dashboard mode)")
        except Exception as e:
            standalone_logger.warning(f"⚠️ Telemetry initialization failed: {e}")
            print("Telemetry initialization failed")
            return False
        
        # EXACT SAME monitoring call as Script 1 (line 6506)
        try:
            standalone_logger.info("🚀 Starting telemetry monitoring (same system as Script 1)")
            print("Telemetry monitoring started - dashboard will update in real-time")

            # This is the EXACT SAME call that creates the beautiful dashboard
            success = await telemetry_integrator.monitor_downloads(interval=10, timeout=None)

            if success:
                standalone_logger.info("✅ Telemetry monitoring completed successfully")
                print("All downloads completed - telemetry monitoring finished")
            else:
                standalone_logger.warning("⚠️ Telemetry monitoring ended with issues")
                print("Telemetry monitoring ended")

        except Exception as e:
            standalone_logger.error(f"❌ Error during telemetry monitoring: {e}")
            print(f"Telemetry monitoring error: {e}")
            success = False
        
        # EXACT SAME cleanup as Script 1 would do
        if telemetry_integrator:
            try:
                await telemetry_integrator.__aexit__(None, None, None)
                standalone_logger.info("🔬 Telemetry session completed")
            except Exception:
                pass
        
        standalone_logger.info("===== Standalone Telemetry Monitor Complete =====")
        return success
        
    except Exception as e:
        standalone_logger.error(f"❌ Fatal error in standalone telemetry monitor: {e}")
        print(f"Fatal error: {e}")
        return False

if __name__ == "__main__":
    """
    Entry point for standalone telemetry monitoring.
    This runs the EXACT SAME telemetry system as Script 1, just independently.
    """
    try:
        success = asyncio.run(run_standalone_telemetry_monitor())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTelemetry monitoring interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)
