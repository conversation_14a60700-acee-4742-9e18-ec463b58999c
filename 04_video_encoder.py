#!/usr/bin/env python3
"""
HandBrake Video Re-encoding Script for Media Pipeline - Filesystem-First Architecture
=====================================================================================

This script automates video re-encoding using HandBrakeCLI as part of a media processing pipeline.
It uses filesystem-first state management with marker files as the single source of truth.

Features:
- Filesystem-based movie discovery and state management
- Marker files for tracking processing stages (.mkv_processing, .mkv_complete)
- Resolution-based encoding presets (1080p/4K)
- Dynamic bitrate calculation with quality floors
- Interactive user confirmation and parameter override
- Idempotent operations with auto-recovery from interruptions
- Metadata-only database for static information
- Audio passthrough preservation

Author: GitHub Copilot
Date: July 2025
"""

import os
import sys
import json
import logging
import subprocess
import shutil
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

# Add the _internal directory to the Python path for imports
sys.path.append(str(Path(__file__).parent / "_internal"))

try:
    from utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
except ImportError:
    print("Error: Could not import filesystem-first state management components.")
    print("Please ensure the filesystem_first_state_manager.py is available.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('_internal/logs/video_encoder.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class EncodingParameters:
    """Container for video encoding parameters"""
    preset: str = "slow"
    target_bitrate_kbps: int = 9000
    encoder_profile: str = "main"
    encoder_level: str = "5.1"
    turbo_first_pass: bool = False
    resolution: str = "1080p"
    handbrake_preset: str = "H.265 MKV 1080p30"
    # Audio settings
    audio_codec: str = "aac"
    audio_bitrate_kbps: int = 160
    audio_mixdown: str = "stereo"


@dataclass
class MovieInfo:
    """Container for movie information discovered from filesystem"""
    unique_id: str
    title: str
    year: str
    resolution: str
    movie_directory: Path
    main_movie_file: Optional[Path]
    processed_content: Optional[Path]
    current_stage: str
    duration_seconds: float = 7200.0  # Default 2 hours
    file_size_bytes: int = 0


@dataclass
class EpisodeInfo:
    """Container for TV episode information discovered from filesystem"""
    unique_id: str
    series_title: str
    season_number: int
    episode_number: int
    episode_title: str
    resolution: str
    episode_directory: Path
    main_episode_file: Optional[Path]
    processed_content: Optional[Path]
    current_stage: str
    duration_seconds: float = 2700.0  # Default 45 minutes for TV episode
    file_size_bytes: int = 0
    year: Optional[str] = None


class FilesystemFirstVideoEncoder:
    """Main class for handling video encoding operations using filesystem-first architecture"""
    
    def __init__(self, handbrake_path: Optional[str] = None, workspace_path: Optional[str] = None):
        """
        Initialize the video encoder with filesystem-first state management

        Args:
            handbrake_path: Path to HandBrakeCLI.exe
            workspace_path: Base workspace directory path
        """
        self.workspace_path = Path(workspace_path) if workspace_path else Path.cwd()

        # Initialize filesystem-first state manager
        self.fs_manager = FilesystemFirstStateManager(self.workspace_path)

        # Initialize metadata-only database
        self.metadata_db = MetadataOnlyDatabase(self.workspace_path)

        # Load configuration
        self.config = self.load_config()
        
        # Set HandBrakeCLI path
        if handbrake_path:
            self.handbrake_path = Path(handbrake_path)
        else:
            # Default to the tools directory
            self.handbrake_path = Path(__file__).parent / "_internal" / "tools" / "HandBrakeCLI.exe"
        
        # Verify HandBrakeCLI exists
        if not self.handbrake_path.exists():
            raise FileNotFoundError(f"HandBrakeCLI not found at: {self.handbrake_path}")
        
        # Define minimum bitrate floors (in kbps) from configuration
        encoding_settings = self.config.get('encoding_settings', {})
        self.min_bitrate_1080p = encoding_settings.get('min_bitrate_1080p_kbps', 9000)
        self.min_bitrate_4k = encoding_settings.get('min_bitrate_4k_kbps', 20000)
        self.default_4k_bitrate = encoding_settings.get('default_4k_bitrate_kbps', 20000)

        # Compression ratio for high-bitrate sources
        self.compression_ratio = encoding_settings.get('compression_ratio', 0.5)
        
        logger.info(f"Filesystem-first video encoder initialized")
        logger.info(f"HandBrakeCLI path: {self.handbrake_path}")
        logger.info(f"Workspace: {self.workspace_path}")

    def load_config(self) -> Dict[str, Any]:
        """Load configuration from video_encoder_config.json"""
        config_path = Path(__file__).parent / "_internal" / "config" / "video_encoder_config.json"

        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            logger.info(f"Loaded video encoder configuration from {config_path}")
            return config
        except Exception as e:
            logger.warning(f"Failed to load config from {config_path}: {e}")
            # Return default configuration
            return {
                "encoding_settings": {
                    "default_preset": "slow",
                    "turbo_first_pass": False
                },
                "audio_settings": {
                    "codec": "aac",
                    "bitrate_kbps": 160,
                    "mixdown": "stereo"
                }
            }

    def discover_movies_for_encoding(self) -> List[MovieInfo]:
        """
        Discover movies ready for video encoding using filesystem scanning
        
        Returns:
            List of MovieInfo objects ready for encoding
        """
        logger.info("Discovering movies ready for video encoding...")
        
        # Get all movies by stage from filesystem
        all_movies = self.fs_manager.discover_movies_by_stage()
        
        # Find movies ready for encoding
        encoding_candidates = []
        
        # 1. Movies with completed MKV processing (ready for encoding)
        # These are movies with .mkv_complete marker (detected as 'video_encoding_pending')
        mkv_complete_movies = all_movies.get('video_encoding_pending', [])
        for movie_data in mkv_complete_movies:
            movie_info = self._convert_to_movie_info(movie_data)
            if movie_info:
                encoding_candidates.append(movie_info)
        
        # 2. Movies with interrupted encoding (can be resumed)
        interrupted_movies = []
        for stage_name, movies in all_movies.items():
            for movie_data in movies:
                movie_dir = Path(movie_data.get('movie_directory', ''))
                if movie_dir.exists():
                    # Check for interrupted encoding (.mkv_processing without .mkv_complete)
                    if (movie_dir / '.mkv_processing').exists() and not (movie_dir / '.mkv_complete').exists():
                        movie_info = self._convert_to_movie_info(movie_data)
                        if movie_info:
                            movie_info.current_stage = 'mkv_processing_interrupted'
                            interrupted_movies.append(movie_info)
        
        if interrupted_movies:
            logger.warning(f"Found {len(interrupted_movies)} movies with interrupted encoding")
            print(f"\n⚠️  Found {len(interrupted_movies)} movies with interrupted encoding:")
            for movie in interrupted_movies:
                print(f"   • {movie.title} ({movie.year})")
            
            if self.prompt_yes_no("Reset interrupted movies for retry?", default=True):
                for movie in interrupted_movies:
                    # Clear processing marker to allow retry
                    self.fs_manager.clear_stage_marker(movie.movie_directory, 'mkv_processing')
                    encoding_candidates.append(movie)
                logger.info(f"Reset {len(interrupted_movies)} interrupted movies for retry")
        
        logger.info(f"Found {len(encoding_candidates)} movies ready for encoding")
        return encoding_candidates

    def discover_episodes_for_encoding(self) -> List[EpisodeInfo]:
        """
        Discover TV episodes ready for video encoding using filesystem scanning
        
        TV Show equivalent of discover_movies_for_encoding() with episode-specific handling:
        - Episode-based directory discovery
        - Season/series organization support
        - Same sophisticated processing as movies
        
        Returns:
            List of EpisodeInfo objects ready for encoding
        """
        logger.info("Discovering TV episodes ready for video encoding...")
        
        # Get all episodes by stage from filesystem (using TV-specific discovery)
        all_episodes = self.fs_manager.discover_tv_episodes_by_stage()
        
        # Find episodes ready for encoding
        encoding_candidates = []
        
        # 1. Episodes with completed MKV processing (ready for encoding)
        # These are episodes with .mkv_complete marker (detected as 'video_encoding_pending')
        mkv_complete_episodes = all_episodes.get('video_encoding_pending', [])
        for episode_data in mkv_complete_episodes:
            episode_info = self._convert_to_episode_info(episode_data)
            if episode_info:
                encoding_candidates.append(episode_info)
        
        # 2. Episodes with interrupted encoding (can be resumed)
        interrupted_episodes = []
        for stage_name, episodes in all_episodes.items():
            for episode_data in episodes:
                episode_dir = Path(episode_data.get('episode_directory', ''))
                if episode_dir.exists():
                    # Check for interrupted encoding (.mkv_processing without .mkv_complete)
                    if (episode_dir / '.mkv_processing').exists() and not (episode_dir / '.mkv_complete').exists():
                        episode_info = self._convert_to_episode_info(episode_data)
                        if episode_info:
                            episode_info.current_stage = 'mkv_processing_interrupted'
                            interrupted_episodes.append(episode_info)
        
        if interrupted_episodes:
            logger.warning(f"Found {len(interrupted_episodes)} episodes with interrupted encoding")
            print(f"\n⚠️  Found {len(interrupted_episodes)} episodes with interrupted encoding:")
            for episode in interrupted_episodes:
                print(f"   • {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}")
            
            if self.prompt_yes_no("Reset interrupted episodes for retry?", default=True):
                for episode in interrupted_episodes:
                    # Clear processing marker to allow retry
                    self.fs_manager.clear_stage_marker(episode.episode_directory, 'mkv_processing')
                    encoding_candidates.append(episode)
                logger.info(f"Reset {len(interrupted_episodes)} interrupted episodes for retry")
        
        logger.info(f"Found {len(encoding_candidates)} episodes ready for encoding")
        return encoding_candidates

    def _convert_to_movie_info(self, movie_data: Dict[str, Any]) -> Optional[MovieInfo]:
        """Convert filesystem discovery data to MovieInfo object"""
        try:
            movie_dir = Path(movie_data.get('movie_directory', ''))
            if not movie_dir.exists():
                return None
            
            # Generate unique ID
            unique_id = self.fs_manager.generate_movie_identifier(movie_data)
            
            # Find main movie file
            main_movie_file = None
            movie_files = list(movie_dir.glob("*.mkv"))
            if movie_files:
                # Prefer processed files if they exist
                processed_files = [f for f in movie_files if 'processed' in f.name.lower()]
                main_movie_file = processed_files[0] if processed_files else movie_files[0]
            
            # Check for processed content directory
            processed_content = None
            processed_dir = movie_dir / "_Processed_VideoAudio"
            if processed_dir.exists():
                processed_content = processed_dir
            
            # Get file size and estimate duration
            file_size_bytes = 0
            duration_seconds = 7200.0  # Default 2 hours
            
            if main_movie_file and main_movie_file.exists():
                file_size_bytes = main_movie_file.stat().st_size
                # Try to get actual duration using ffprobe if available, otherwise use MediaInfo logic
                actual_duration = self._get_actual_duration(main_movie_file)
                if actual_duration:
                    duration_seconds = actual_duration
                else:
                    # Improved duration estimate based on typical movie bitrates
                    # For processed files, assume higher bitrate (15-25 Mbps range)
                    if 'processed' in main_movie_file.name.lower():
                        # Processed files typically have higher bitrates
                        estimated_bitrate_mbps = 20  # Assume 20 Mbps for processed files
                    else:
                        # Raw files might be lower
                        estimated_bitrate_mbps = 15  # Assume 15 Mbps for raw files
                    
                    if file_size_bytes > 0:
                        duration_seconds = max((file_size_bytes * 8) / (estimated_bitrate_mbps * 1000 * 1000), 600)  # Min 10 minutes
            
            # Determine resolution from directory structure or metadata
            resolution = self._determine_resolution(movie_dir, movie_data)
            
            return MovieInfo(
                unique_id=unique_id,
                title=movie_data.get('cleaned_title', 'Unknown'),
                year=str(movie_data.get('year', 'Unknown')),
                resolution=resolution,
                movie_directory=movie_dir,
                main_movie_file=main_movie_file,
                processed_content=processed_content,
                current_stage=movie_data.get('current_stage', 'video_encoding_pending'),  # Use filesystem stage
                duration_seconds=duration_seconds,
                file_size_bytes=file_size_bytes
            )
            
        except Exception as e:
            logger.error(f"Error converting movie data: {e}")
            return None

    def _determine_resolution(self, movie_dir: Path, movie_data: Dict[str, Any]) -> str:
        """Determine movie resolution from directory structure or metadata"""
        # Check if movie is in a resolution-specific directory
        path_str = str(movie_dir).lower()
        if '4k' in path_str or '2160p' in path_str:
            return '4K'
        elif '1080p' in path_str:
            return '1080p'
        elif '720p' in path_str:
            return '720p'
        
        # Try to get from metadata database
        unique_id = self.fs_manager.generate_movie_identifier(movie_data)
        metadata = self.metadata_db.get_movie_metadata(unique_id)
        if metadata and metadata.get('metadata', {}).get('resolution'):
            return metadata['metadata']['resolution']
        
        # Default assumption
        return '1080p'

    def _convert_to_episode_info(self, episode_data: Dict[str, Any]) -> Optional[EpisodeInfo]:
        """Convert filesystem discovery data to EpisodeInfo object"""
        try:
            episode_dir = Path(episode_data.get('episode_directory', ''))
            if not episode_dir.exists():
                return None
            
            # Generate unique ID for episode
            unique_id = self.fs_manager.generate_episode_identifier(episode_data)
            
            # Extract episode information
            series_title = episode_data.get('series_title', 'Unknown')
            season_number = episode_data.get('season_number', 1)
            episode_number = episode_data.get('episode_number', 1)
            episode_title = episode_data.get('episode_title', '')
            resolution = episode_data.get('resolution', '1080p')
            year = episode_data.get('year')
            
            # Find main episode file
            main_episode_file = None
            episode_files = list(episode_dir.glob("*.mkv"))
            if episode_files:
                # Prefer processed files if they exist
                processed_files = [f for f in episode_files if 'processed' in f.name.lower()]
                main_episode_file = processed_files[0] if processed_files else episode_files[0]
            
            # Check for processed content directory
            processed_content = None
            processed_dir = episode_dir / "_Processed_VideoAudio"
            if processed_dir.exists():
                processed_content = processed_dir
            
            # Get file size and estimate episode duration
            file_size_bytes = 0
            duration_seconds = 2700.0  # Default 45 minutes for TV episode
            
            if main_episode_file and main_episode_file.exists():
                file_size_bytes = main_episode_file.stat().st_size
                # Try to get actual duration for episode
                actual_duration = self._get_actual_duration(main_episode_file)
                if actual_duration:
                    duration_seconds = actual_duration
                else:
                    # Improved duration estimate for TV episodes
                    # TV episodes typically have different bitrate characteristics than movies
                    if 'processed' in main_episode_file.name.lower():
                        # Processed episodes typically have higher bitrates
                        estimated_bitrate_mbps = 12  # Assume 12 Mbps for processed episodes
                    else:
                        # Raw episodes vary widely, estimate conservatively
                        estimated_bitrate_mbps = 8  # Assume 8 Mbps for raw episodes
                    
                    # Calculate duration: file_size_bits / bitrate_bps
                    file_size_bits = file_size_bytes * 8
                    bitrate_bps = estimated_bitrate_mbps * 1_000_000
                    duration_seconds = file_size_bits / bitrate_bps
                    
                    # Clamp to reasonable episode duration ranges (15 minutes to 2 hours)
                    duration_seconds = max(900, min(duration_seconds, 7200))
            
            # Determine current stage from filesystem markers
            current_stage = "video_encoding_pending"  # Default
            if (episode_dir / '.mkv_processing').exists():
                current_stage = "mkv_processing"
            elif (episode_dir / '.encoding_complete').exists():
                current_stage = "encoding_complete"
            elif (episode_dir / '.error').exists():
                current_stage = "error"
            
            episode_info = EpisodeInfo(
                unique_id=unique_id,
                series_title=series_title,
                season_number=season_number,
                episode_number=episode_number,
                episode_title=episode_title,
                resolution=resolution,
                episode_directory=episode_dir,
                main_episode_file=main_episode_file,
                processed_content=processed_content,
                current_stage=current_stage,
                duration_seconds=duration_seconds,
                file_size_bytes=file_size_bytes,
                year=year
            )
            
            return episode_info
            
        except Exception as e:
            logger.error(f"Error converting episode data: {e}")
            return None

    def _get_actual_duration(self, video_file: Path) -> Optional[float]:
        """
        Try to get actual video duration using ffprobe
        
        Args:
            video_file: Path to video file
            
        Returns:
            Duration in seconds, or None if unable to determine
        """
        try:
            import subprocess
            
            # Try ffprobe first
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-select_streams", "v:0",
                "-show_entries", "format=duration",
                "-of", "csv=p=0",
                str(video_file)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                duration = float(result.stdout.strip())
                logger.info(f"Got actual duration from ffprobe: {duration:.1f} seconds ({duration/60:.1f} minutes)")
                return duration
                
        except Exception as e:
            logger.debug(f"Could not get duration with ffprobe: {e}")
        
        # Try alternative method with MediaInfo if available
        try:
            cmd = [
                "mediainfo",
                "--Output=General;%Duration%",
                str(video_file)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                duration_ms = float(result.stdout.strip())
                duration_sec = duration_ms / 1000
                logger.info(f"Got actual duration from MediaInfo: {duration_sec:.1f} seconds ({duration_sec/60:.1f} minutes)")
                return duration_sec
                
        except Exception as e:
            logger.debug(f"Could not get duration with MediaInfo: {e}")
        
        return None

    def calculate_target_bitrate(self, movie: MovieInfo) -> int:
        """
        Calculate target bitrate based on source file and resolution
        
        Args:
            movie: MovieInfo object containing file information
            
        Returns:
            Target bitrate in kbps
        """
        try:
            # Get file size if not available
            if movie.file_size_bytes == 0 and movie.main_movie_file and movie.main_movie_file.exists():
                movie.file_size_bytes = movie.main_movie_file.stat().st_size
            
            if movie.file_size_bytes == 0 or movie.duration_seconds == 0:
                logger.warning(f"Missing file size or duration for {movie.title}, using defaults")
                return self.min_bitrate_4k if "4k" in movie.resolution.lower() or "2160p" in movie.resolution.lower() else self.min_bitrate_1080p
            
            # Calculate original average bitrate (in kbps)
            original_bitrate_kbps = (movie.file_size_bytes * 8) / movie.duration_seconds / 1000
            
            logger.info(f"Original bitrate for {movie.title}: {original_bitrate_kbps:.1f} kbps ({original_bitrate_kbps/1000:.1f} Mbps)")
            
            # Determine target based on resolution
            if "4k" in movie.resolution.lower() or "2160p" in movie.resolution.lower():
                # 4K: Default to 20 Mbps
                target_kbps = self.default_4k_bitrate
                logger.info(f"4K content: using default {target_kbps/1000} Mbps")
            else:
                # 1080p or lower: Use compression ratio with minimum floor
                if original_bitrate_kbps < self.min_bitrate_1080p:
                    target_kbps = self.min_bitrate_1080p
                    logger.info(f"Original below minimum, using floor: {target_kbps/1000} Mbps")
                else:
                    # Apply compression ratio but respect minimum
                    compressed_bitrate = int(original_bitrate_kbps * self.compression_ratio)
                    target_kbps = max(compressed_bitrate, self.min_bitrate_1080p)
                    logger.info(f"Compressed to {self.compression_ratio*100}% of original: {target_kbps/1000:.1f} Mbps")
            
            return target_kbps
            
        except Exception as e:
            logger.error(f"Error calculating bitrate for {movie.title}: {e}")
            # Return safe defaults
            return self.min_bitrate_4k if "4k" in movie.resolution.lower() or "2160p" in movie.resolution.lower() else self.min_bitrate_1080p

    def calculate_episode_target_bitrate(self, episode: EpisodeInfo) -> int:
        """
        Calculate target bitrate for TV episode based on source file and resolution
        
        TV Show equivalent of calculate_target_bitrate() with episode-specific optimizations:
        - Episode-appropriate bitrate floors (lower than movies due to shorter duration)
        - TV show compression ratios optimized for episode content
        - Series-specific quality considerations
        
        Args:
            episode: EpisodeInfo object containing file information
            
        Returns:
            Target bitrate in kbps for episode
        """
        try:
            # Get file size if not available
            if episode.file_size_bytes == 0 and episode.main_episode_file and episode.main_episode_file.exists():
                episode.file_size_bytes = episode.main_episode_file.stat().st_size
            
            if episode.file_size_bytes == 0 or episode.duration_seconds == 0:
                logger.warning(f"Missing file size or duration for {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}, using defaults")
                return self.min_bitrate_4k if "4k" in episode.resolution.lower() or "2160p" in episode.resolution.lower() else self.min_bitrate_1080p
            
            # Calculate original average bitrate (in kbps)
            original_bitrate_kbps = (episode.file_size_bytes * 8) / episode.duration_seconds / 1000
            
            logger.info(f"Original episode bitrate for {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}: {original_bitrate_kbps:.1f} kbps ({original_bitrate_kbps/1000:.1f} Mbps)")
            
            # Episode-specific bitrate calculation
            if "4k" in episode.resolution.lower() or "2160p" in episode.resolution.lower():
                # 4K Episodes: Default to 18 Mbps (slightly lower than movies due to shorter duration)
                target_kbps = 18000  # 18 Mbps for 4K episodes
                logger.info(f"4K episode: using default {target_kbps/1000} Mbps")
            else:
                # 1080p Episodes: Use compression ratio with episode-appropriate minimum floor
                episode_min_bitrate = 7000  # 7 Mbps minimum for 1080p episodes (lower than movies)
                
                if original_bitrate_kbps < episode_min_bitrate:
                    target_kbps = episode_min_bitrate
                    logger.info(f"Original below episode minimum, using floor: {target_kbps/1000} Mbps")
                else:
                    # Apply compression ratio but respect episode minimum
                    # Episodes can be compressed more aggressively than movies
                    episode_compression_ratio = 0.7  # 70% of original for episodes
                    compressed_bitrate = int(original_bitrate_kbps * episode_compression_ratio)
                    target_kbps = max(compressed_bitrate, episode_min_bitrate)
                    logger.info(f"Episode compressed to {episode_compression_ratio*100}% of original: {target_kbps/1000:.1f} Mbps")
            
            return target_kbps
            
        except Exception as e:
            logger.error(f"Error calculating episode bitrate for {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}: {e}")
            # Return safe defaults for episodes
            return self.min_bitrate_4k if "4k" in episode.resolution.lower() or "2160p" in episode.resolution.lower() else 7000  # 7 Mbps default for episodes

    def get_encoding_parameters(self, movie: MovieInfo) -> EncodingParameters:
        """
        Get encoding parameters based on movie resolution and characteristics

        Args:
            movie: MovieInfo object

        Returns:
            EncodingParameters object with appropriate settings
        """
        params = EncodingParameters()

        # Load defaults from configuration
        encoding_settings = self.config.get('encoding_settings', {})
        audio_settings = self.config.get('audio_settings', {})

        params.preset = encoding_settings.get('default_preset', 'slow')
        params.turbo_first_pass = encoding_settings.get('turbo_first_pass', False)
        params.encoder_profile = encoding_settings.get('default_profile', 'main')
        params.encoder_level = encoding_settings.get('default_level', '5.1')

        # Audio settings from configuration
        params.audio_codec = audio_settings.get('codec', 'aac')
        params.audio_bitrate_kbps = audio_settings.get('bitrate_kbps', 160)
        params.audio_mixdown = audio_settings.get('mixdown', 'stereo')

        # Determine resolution and preset
        handbrake_presets = self.config.get('handbrake_presets', {})
        if "4k" in movie.resolution.lower() or "2160p" in movie.resolution.lower():
            params.resolution = "4K"
            params.handbrake_preset = handbrake_presets.get('4k', 'H.265 MKV 2160p60 4K')
        else:
            params.resolution = "1080p"
            params.handbrake_preset = handbrake_presets.get('1080p', 'H.265 MKV 1080p30')

        # Calculate target bitrate
        params.target_bitrate_kbps = self.calculate_target_bitrate(movie)

        # Check for user preferences in metadata database
        metadata = self.metadata_db.get_movie_metadata(movie.unique_id)
        if metadata and metadata.get('metadata', {}):
            user_metadata = metadata['metadata']
            # Apply any user-specified encoding preferences
            if 'encoding_preset' in user_metadata:
                params.preset = user_metadata['encoding_preset']
        return params

    def get_episode_encoding_parameters(self, episode: EpisodeInfo) -> EncodingParameters:
        """
        Get encoding parameters for TV episode based on resolution and characteristics

        TV Show equivalent of get_encoding_parameters() with episode-specific optimizations:
        - Episode-appropriate quality presets
        - Season/series-based encoding strategies
        - TV show resolution handling
        - Episode-specific audio settings

        Args:
            episode: EpisodeInfo object

        Returns:
            EncodingParameters configured for the episode
        """
        # Calculate episode-specific target bitrate
        target_bitrate = self.calculate_episode_target_bitrate(episode)
        
        # Determine resolution and quality preset for episode
        if "4k" in episode.resolution.lower() or "2160p" in episode.resolution.lower():
            # 4K Episode settings
            params = EncodingParameters(
                preset="slow",  # Good quality/speed balance for episodes
                target_bitrate_kbps=target_bitrate,
                encoder_profile="main10",  # 10-bit for 4K episodes
                encoder_level="5.1",
                turbo_first_pass=True,  # Enable turbo for episodes (faster processing)
                resolution="4K",
                handbrake_preset="H.265 MKV 2160p60"
            )
        elif "1080p" in episode.resolution.lower():
            # 1080p Episode settings
            params = EncodingParameters(
                preset="slow",  # Balanced preset for episodes
                target_bitrate_kbps=target_bitrate,
                encoder_profile="main",
                encoder_level="4.1",
                turbo_first_pass=True,  # Episodes benefit from faster encoding
                resolution="1080p",
                handbrake_preset="H.265 MKV 1080p30"
            )
        else:
            # 720p or other resolution episode settings
            params = EncodingParameters(
                preset="medium",  # Faster preset for lower resolution episodes
                target_bitrate_kbps=target_bitrate,
                encoder_profile="main",
                encoder_level="3.1",
                turbo_first_pass=True,
                resolution="720p",
                handbrake_preset="H.265 MKV 720p30"
            )

        # Episode-specific audio settings (optimized for TV content)
        params.audio_codec = "aac"
        params.audio_bitrate_kbps = 128  # Slightly lower than movies for episodes
        params.audio_mixdown = "stereo"  # Most TV episodes are stereo

        logger.info(f"Episode encoding parameters for {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}: {params.resolution} @ {target_bitrate/1000:.1f} Mbps")
        
        return params

    def prompt_episode_user_confirmation(self, episode: EpisodeInfo, params: EncodingParameters) -> Tuple[bool, EncodingParameters]:
        """
        Prompt user for episode encoding confirmation and allow parameter overrides
        
        TV Show equivalent of prompt_user_confirmation() with episode-specific display:
        - Episode-specific information display
        - Season/episode identification
        - Same interactive parameter control as movies
        
        Args:
            episode: EpisodeInfo object
            params: Initial encoding parameters
            
        Returns:
            Tuple of (should_proceed, updated_parameters)
        """
        print(f"\n{'='*70}")
        print(f"Ready to encode: {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}")
        if episode.episode_title:
            print(f"Episode: {episode.episode_title}")
        print(f"Resolution: [{params.resolution}]")
        print(f"{'='*70}")
        if episode.main_episode_file:
            print(f"Input file: {episode.main_episode_file.name}")
        print(f"File size: {episode.file_size_bytes / (1024**3):.1f} GB")
        print(f"Duration: {episode.duration_seconds / 60:.1f} minutes")
        print(f"Current stage: {episode.current_stage}")
        print(f"\nProposed episode encoding settings:")
        print(f"  • Video codec: H.265 (x265), preset: {params.preset}")
        print(f"  • Target bitrate: {params.target_bitrate_kbps} kbps ({params.target_bitrate_kbps/1000:.1f} Mbps)")
        print(f"  • Encoder profile: {params.encoder_profile}, level: {params.encoder_level}")
        print(f"  • Two-pass encoding: {'Yes' if not params.turbo_first_pass else 'Yes (turbo first pass)'}")
        print(f"  • Turbo first pass: {'OFF' if not params.turbo_first_pass else 'ON'}")
        print(f"  • Audio: {params.audio_codec.upper()} @ {params.audio_bitrate_kbps} kbps ({params.audio_mixdown})")
        print(f"  • HandBrake preset: {params.handbrake_preset}")
        
        while True:
            choice = input(f"\nProceed with episode encoding? (Y)es/(E)dit/(S)kip/(Q)uit: ").upper().strip()
            
            if choice in ['Y', 'YES', '']:
                return True, params
            elif choice in ['S', 'SKIP']:
                return False, params
            elif choice in ['Q', 'QUIT']:
                sys.exit(0)
            elif choice in ['E', 'EDIT']:
                params = self.prompt_parameter_overrides(params)
                # Show updated settings
                print(f"\nUpdated episode settings:")
                print(f"  • Preset: {params.preset}")
                print(f"  • Target bitrate: {params.target_bitrate_kbps} kbps ({params.target_bitrate_kbps/1000:.1f} Mbps)")
                print(f"  • Profile/Level: {params.encoder_profile}@{params.encoder_level}")
                print(f"  • Turbo first pass: {'Yes' if params.turbo_first_pass else 'No'}")
                return True, params
            else:
                print("Invalid choice. Please enter Y, E, S, or Q.")

    def prompt_user_confirmation(self, movie: MovieInfo, params: EncodingParameters) -> Tuple[bool, EncodingParameters]:
        """
        Prompt user for confirmation and allow parameter overrides
        
        Args:
            movie: MovieInfo object
            params: Initial encoding parameters
            
        Returns:
            Tuple of (should_proceed, updated_parameters)
        """
        print(f"\n{'='*60}")
        print(f"Ready to encode: {movie.title} ({movie.year}) [{params.resolution}]")
        print(f"{'='*60}")
        if movie.main_movie_file:
            print(f"Input file: {movie.main_movie_file.name}")
        print(f"File size: {movie.file_size_bytes / (1024**3):.1f} GB")
        print(f"Duration: {movie.duration_seconds / 3600:.1f} hours")
        print(f"Current stage: {movie.current_stage}")
        print(f"\nProposed encoding settings:")
        print(f"  • Video codec: H.265 (x265), preset: {params.preset}")
        print(f"  • Target bitrate: {params.target_bitrate_kbps} kbps ({params.target_bitrate_kbps/1000:.1f} Mbps)")
        print(f"  • Encoder profile: {params.encoder_profile}, level: {params.encoder_level}")
        print(f"  • Two-pass encoding: {'Yes' if not params.turbo_first_pass else 'Yes (turbo first pass)'}")
        print(f"  • Turbo first pass: {'OFF' if not params.turbo_first_pass else 'ON'}")
        print(f"  • Audio: {params.audio_codec.upper()} @ {params.audio_bitrate_kbps} kbps ({params.audio_mixdown})")
        print(f"  • HandBrake preset: {params.handbrake_preset}")
        
        while True:
            choice = input(f"\nProceed with these settings? (Y)es/(E)dit/(S)kip/(Q)uit: ").upper().strip()
            
            if choice in ['Y', 'YES', '']:
                return True, params
            elif choice in ['S', 'SKIP']:
                return False, params
            elif choice in ['Q', 'QUIT']:
                sys.exit(0)
            elif choice in ['E', 'EDIT']:
                params = self.prompt_parameter_overrides(params)
                # Show updated settings
                print(f"\nUpdated settings:")
                print(f"  • Preset: {params.preset}")
                print(f"  • Target bitrate: {params.target_bitrate_kbps} kbps ({params.target_bitrate_kbps/1000:.1f} Mbps)")
                print(f"  • Profile/Level: {params.encoder_profile}@{params.encoder_level}")
                print(f"  • Turbo first pass: {'Yes' if params.turbo_first_pass else 'No'}")
                return True, params
            else:
                print("Invalid choice. Please enter Y, E, S, or Q.")

    def prompt_parameter_overrides(self, params: EncodingParameters) -> EncodingParameters:
        """
        Allow user to override encoding parameters
        
        Args:
            params: Current parameters
            
        Returns:
            Updated parameters
        """
        print(f"\nParameter Override Menu:")
        print(f"Current settings shown in [brackets]")
        
        # Turbo first pass
        turbo = input(f"Use turbo first pass? [{'Y' if params.turbo_first_pass else 'N'}]: ").upper().strip()
        if turbo in ['N', 'NO']:
            params.turbo_first_pass = False
        elif turbo in ['Y', 'YES']:
            params.turbo_first_pass = True
        
        # Target bitrate
        bitrate_input = input(f"Target bitrate in kbps [{params.target_bitrate_kbps}]: ").strip()
        if bitrate_input:
            try:
                new_bitrate = int(bitrate_input)
                if new_bitrate > 0:
                    params.target_bitrate_kbps = new_bitrate
                else:
                    print("Invalid bitrate, keeping current value")
            except ValueError:
                print("Invalid bitrate format, keeping current value")
        
        # Encoder preset
        preset_input = input(f"Encoder speed preset [{params.preset}] (ultrafast/fast/medium/slow/slower/veryslow): ").strip().lower()
        valid_presets = ['ultrafast', 'fast', 'medium', 'slow', 'slower', 'veryslow']
        if preset_input in valid_presets:
            params.preset = preset_input
        elif preset_input:
            print(f"Invalid preset '{preset_input}', keeping current value")
        
        # Encoder level
        level_input = input(f"Encoder level [{params.encoder_level}]: ").strip()
        if level_input:
            params.encoder_level = level_input
        
        # Encoder profile
        profile_input = input(f"Encoder profile [{params.encoder_profile}] (main/main10): ").strip().lower()
        if profile_input in ['main', 'main10']:
            params.encoder_profile = profile_input
        elif profile_input:
            print(f"Invalid profile '{profile_input}', keeping current value")
        
        return params

    def build_handbrake_command(self, movie: MovieInfo, params: EncodingParameters, output_path: Path) -> List[str]:
        """
        Build HandBrakeCLI command with specified parameters
        
        Args:
            movie: MovieInfo object
            params: Encoding parameters
            output_path: Output file path
            
        Returns:
            Command as list of strings
        """
        if not movie.main_movie_file:
            raise ValueError(f"No input file found for {movie.title}")
        
        cmd = [
            str(self.handbrake_path),
            "-i", str(movie.main_movie_file),
            "-o", str(output_path),

            # Video encoder settings (build from scratch for full control)
            "-e", "x265",
            "--encoder-preset", params.preset,
            "--encoder-profile", params.encoder_profile,
            "--encoder-level", params.encoder_level,

            # Container and format
            "-f", "mkv",

            # Set target bitrate with multi-pass encoding (don't use --quality with bitrate)
            "-b", str(params.target_bitrate_kbps),
            "--multi-pass",
        ]
        
        # Add turbo first pass if enabled
        if params.turbo_first_pass:
            cmd.extend(["-T"])
        
        # Audio handling - convert to AAC
        cmd.extend([
            "--audio", "1",  # Use first audio track
            "-E", params.audio_codec,  # Use AAC encoding
            "-B", str(params.audio_bitrate_kbps),  # Set bitrate
            "--mixdown", params.audio_mixdown  # Set mixdown to stereo
        ])
        
        # Additional quality settings
        cmd.extend([
            "--verbose", "1"  # Some output for progress monitoring
        ])
        
        return cmd

    def get_output_path(self, movie: MovieInfo, params: EncodingParameters) -> Path:
        """
        Determine output file path in the ready for mux directory
        
        Args:
            movie: MovieInfo object
            params: Encoding parameters
            
        Returns:
            Output file path
        """
        # Use the ready_for_mux directory from filesystem manager
        base_dir = self.fs_manager.stage_directories['ready_for_mux']
        resolution_dir = base_dir / params.resolution.lower()
        movie_dir = resolution_dir / f"{movie.title} ({movie.year})"
        
        # Create directories if they don't exist
        movie_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate output filename with .encoded suffix
        safe_title = "".join(c for c in movie.title if c.isalnum() or c in (' ', '-', '_')).strip()
        output_filename = f"{safe_title} ({movie.year}).encoded.mkv"
        
        return movie_dir / output_filename

    def get_episode_output_path(self, episode: EpisodeInfo, params: EncodingParameters) -> Path:
        """
        Get output path for encoded episode with season/series organization
        
        TV Show equivalent of get_output_path() with episode-specific organization:
        - Series/season directory structure
        - Episode-specific naming conventions
        - Same filesystem management as movies
        
        Args:
            episode: EpisodeInfo object
            params: Encoding parameters
            
        Returns:
            Output file path for episode
        """
        # Use the ready_for_mux directory from filesystem manager
        base_dir = self.fs_manager.stage_directories['ready_for_mux']
        
        # For TV shows, organize by series/season structure
        series_dir = base_dir / episode.series_title
        season_dir = series_dir / f"Season {episode.season_number:02d}"
        
        # Create directories if they don't exist
        season_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate episode output filename with .encoded suffix
        safe_series = "".join(c for c in episode.series_title if c.isalnum() or c in (' ', '-', '_')).strip()
        episode_filename = f"{safe_series}_S{episode.season_number:02d}E{episode.episode_number:02d}"
        
        if episode.episode_title:
            safe_episode_title = "".join(c for c in episode.episode_title if c.isalnum() or c in (' ', '-', '_')).strip()[:30]
            episode_filename += f"_{safe_episode_title}"
        
        episode_filename += ".encoded.mkv"
        
        return season_dir / episode_filename

    def run_handbrake_encoding(self, cmd: List[str], movie: MovieInfo) -> Tuple[bool, str]:
        """
        Execute HandBrakeCLI encoding command
        
        Args:
            cmd: Command to execute
            movie: MovieInfo object for logging
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            logger.info(f"Starting encoding for {movie.title}")
            
            # Show the command for debugging
            cmd_str = ' '.join(f'"{arg}"' if ' ' in arg else arg for arg in cmd)
            logger.info(f"HandBrake command: {cmd_str}")
            print(f"Running HandBrake command:")
            print(f"  {cmd_str}")
            
            start_time = time.time()
            
            # Run HandBrakeCLI
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                encoding='utf-8',
                errors='ignore',
                bufsize=1
            )
            
            output_lines = []
            
            # Monitor output for progress and errors
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output_lines.append(output.strip())
                    # Show encoding progress (HandBrake outputs percentage)
                    if "%" in output and "fps" in output:
                        print(f"\r{output.strip()}", end="", flush=True)
            
            print()  # New line after progress output
            
            # Get return code
            return_code = process.poll()
            encoding_time = time.time() - start_time
            
            if return_code == 0:
                logger.info(f"Encoding completed successfully for {movie.title} in {encoding_time/60:.1f} minutes")
                return True, ""
            else:
                error_msg = f"HandBrakeCLI failed with return code {return_code}"
                
                # Get last few lines of output for error context
                error_context = "\n".join(output_lines[-10:]) if output_lines else "No output captured"
                full_error = f"{error_msg}\nLast output:\n{error_context}"
                
                # Show the error output for debugging
                print(f"\nHandBrake Error Output:")
                print(f"Return code: {return_code}")
                if output_lines:
                    print("Last 10 lines of output:")
                    for line in output_lines[-10:]:
                        print(f"  {line}")
                else:
                    print("No output captured")
                
                logger.error(f"Encoding failed for {movie.title}: {full_error}")
                return False, full_error
                
        except Exception as e:
            error_msg = f"Exception during encoding: {str(e)}"
            logger.error(f"Encoding error for {movie.title}: {error_msg}")
            return False, error_msg

    def verify_output_file(self, output_path: Path, expected_duration: float) -> bool:
        """
        Verify that the output file was created and is valid
        
        Args:
            output_path: Path to output file
            expected_duration: Expected duration in seconds
            
        Returns:
            True if file appears valid
        """
        try:
            if not output_path.exists():
                logger.error(f"Output file not created: {output_path}")
                return False
            
            file_size = output_path.stat().st_size
            
            # Check if file is suspiciously small (less than 1MB)
            if file_size < 1024 * 1024:
                logger.error(f"Output file suspiciously small: {file_size} bytes")
                return False
            
            # Calculate expected minimum size based on duration and minimum bitrate
            # Using a very conservative estimate
            min_expected_size = (expected_duration * 1000 * 1000) / 8  # 1 Mbps minimum
            
            if file_size < min_expected_size:
                logger.warning(f"Output file smaller than expected: {file_size} vs {min_expected_size} bytes")
                # Don't fail for this, just warn
            
            logger.info(f"Output file verification passed: {file_size / (1024**2):.1f} MB")
            return True
            
        except Exception as e:
            logger.error(f"Error verifying output file: {e}")
            return False

    def set_encoding_markers(self, movie: MovieInfo, stage: str, data: Optional[Dict[str, Any]] = None):
        """
        Set marker files for encoding stages using filesystem-first state management
        
        Args:
            movie: MovieInfo object
            stage: Stage marker to set ('mkv_processing', 'mkv_complete', 'error')
            data: Optional data to store with marker
        """
        try:
            self.fs_manager.set_stage_marker(movie.movie_directory, stage, data)
            logger.debug(f"Set {stage} marker for {movie.title}")
        except Exception as e:
            logger.error(f"Failed to set {stage} marker for {movie.title}: {e}")

    def clear_encoding_markers(self, movie: MovieInfo, stage: str):
        """
        Clear marker files for encoding stages
        
        Args:
            movie: MovieInfo object
            stage: Stage marker to clear
        """
        try:
            self.fs_manager.clear_stage_marker(movie.movie_directory, stage)
            logger.debug(f"Cleared {stage} marker for {movie.title}")
        except Exception as e:
            logger.error(f"Failed to clear {stage} marker for {movie.title}: {e}")

    def set_episode_encoding_markers(self, episode: EpisodeInfo, stage: str, data: dict = None):
        """
        Set filesystem markers for episode encoding stages
        
        TV Show equivalent of set_encoding_markers() with episode-specific handling:
        - Episode directory marker management
        - Same marker-based state tracking as movies
        - Episode-specific data storage
        
        Args:
            episode: EpisodeInfo object
            stage: Stage name (e.g., 'encoding_started', 'encoding_complete', 'error')
            data: Optional data to store with the marker
        """
        try:
            self.fs_manager.set_stage_marker(episode.episode_directory, stage, data)
            logger.debug(f"Set episode {stage} marker for {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}")
        except Exception as e:
            logger.error(f"Error setting episode marker {stage}: {e}")

    def clear_episode_encoding_markers(self, episode: EpisodeInfo, stage: str):
        """
        Clear filesystem markers for episode encoding stages
        
        Args:
            episode: EpisodeInfo object
            stage: Stage name to clear
        """
        try:
            self.fs_manager.clear_stage_marker(episode.episode_directory, stage)
            logger.debug(f"Cleared episode {stage} marker for {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}")
        except Exception as e:
            logger.error(f"Error clearing episode marker {stage}: {e}")

    def build_episode_handbrake_command(self, episode: EpisodeInfo, params: EncodingParameters, output_path: Path) -> List[str]:
        """
        Build HandBrakeCLI command for episode encoding
        
        TV Show equivalent of build_handbrake_command() with episode-specific optimizations:
        - Episode input file handling
        - Same sophisticated encoding parameters as movies
        - Episode-appropriate audio/video settings
        
        Args:
            episode: EpisodeInfo object
            params: Encoding parameters
            output_path: Output file path
            
        Returns:
            HandBrakeCLI command as list of strings
        """
        if not episode.main_episode_file or not episode.main_episode_file.exists():
            raise ValueError(f"Episode input file not found: {episode.main_episode_file}")
        
        cmd = [
            str(self.handbrake_path),
            "-i", str(episode.main_episode_file),
            "-o", str(output_path),
            
            # Video encoding settings (same sophistication as movies)
            "--encoder", "x265",
            "--encoder-preset", params.preset,
            "--encoder-profile", params.encoder_profile,
            "--encoder-level", params.encoder_level,
            "--vb", str(params.target_bitrate_kbps),
            "--two-pass",
            "--optimize" if not params.turbo_first_pass else "--no-optimize",
            
            # Audio settings (episode-appropriate)
            "--audio", "1",  # First audio track
            "--aencoder", params.audio_codec,
            "--ab", str(params.audio_bitrate_kbps),
            "--mixdown", params.audio_mixdown,
            
            # Subtitle handling (same as movies)
            "--subtitle", "scan",
            "--subtitle-forced",
            "--subtitle-burn",
            
            # Container and format
            "--format", "av_mkv",
            
            # Quality and optimization
            "--decomb",
            "--loose-anamorphic",
            "--verbose=1"
        ]
        
        # Episode-specific turbo first pass handling
        if params.turbo_first_pass:
            cmd.extend(["--turbo"])
        
        logger.info(f"Built episode HandBrake command for {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}")
        return cmd

    def set_encoding_markers_in_dir(self, target_dir: Path, stage: str, data: dict = None):
        """
        Set marker files in a specific directory (for output directories)
        
        Args:
            target_dir: Directory to place marker in
            stage: Stage marker to set
            data: Optional data to store with marker
        """
        try:
            self.fs_manager.set_stage_marker(target_dir, stage, data)
            logger.debug(f"Set {stage} marker in {target_dir}")
        except Exception as e:
            logger.error(f"Failed to set {stage} marker in {target_dir}: {e}")

    def copy_largest_audio_file(self, movie: MovieInfo, output_movie_dir: Path):
        """
        Copy the largest audio file from stage 3 _Processed_Audio to stage 4 movie directory

        Args:
            movie: MovieInfo object
            output_movie_dir: Stage 4 movie directory where encoded video is located
        """
        try:
            # Find the stage 3 directory for this movie
            stage3_base = Path("workspace") / "3_mkv_cleaned_subtitles_extracted"
            resolution = "1080p"  # Default
            if "4k" in str(output_movie_dir).lower():
                resolution = "4k"
            elif "720p" in str(output_movie_dir).lower():
                resolution = "720p"

            # Construct the movie directory name: "Title (Year)"
            movie_dir_name = f"{movie.title} ({movie.year})"
            stage3_movie_dir = stage3_base / resolution / movie_dir_name
            processed_audio_dir = stage3_movie_dir / "_Processed_Audio"

            logger.info(f"🔍 Looking for audio files in: {processed_audio_dir}")

            if not processed_audio_dir.exists():
                logger.warning(f"No _Processed_Audio directory found at: {processed_audio_dir}")
                return

            # Find all audio files
            audio_files = []
            for audio_file in processed_audio_dir.iterdir():
                if audio_file.is_file() and audio_file.suffix.lower() in ['.thd', '.ac3', '.dts', '.flac', '.wav', '.aac']:
                    audio_files.append(audio_file)

            if not audio_files:
                logger.warning(f"No audio files found in: {processed_audio_dir}")
                return

            # Find the largest audio file
            largest_audio_file = max(audio_files, key=lambda f: f.stat().st_size)

            # Copy it to the stage 4 movie directory
            dest_audio_file = output_movie_dir / largest_audio_file.name
            shutil.copy2(str(largest_audio_file), str(dest_audio_file))

            logger.info(f"✅ Copied largest audio file: {largest_audio_file.name} ({largest_audio_file.stat().st_size / (1024**2):.1f} MB)")
            logger.info(f"   From: {processed_audio_dir}")
            logger.info(f"   To: {output_movie_dir}")

        except Exception as e:
            logger.error(f"Failed to copy largest audio file: {e}")
            raise

    def cleanup_stage3_after_encoding(self, movie: MovieInfo):
        """
        Clean up stage 3 files after successful encoding:
        1. Delete the .processed.mkv file that was used for encoding
        2. Delete the entire _Processed_Audio folder

        Args:
            movie: MovieInfo object
        """
        try:
            # Find the stage 3 directory for this movie
            stage3_base = Path("workspace") / "3_mkv_cleaned_subtitles_extracted"
            resolution = "1080p"  # Default
            if "4k" in str(movie.movie_directory).lower():
                resolution = "4k"
            elif "720p" in str(movie.movie_directory).lower():
                resolution = "720p"

            # Construct the movie directory name: "Title (Year)"
            movie_dir_name = f"{movie.title} ({movie.year})"
            stage3_movie_dir = stage3_base / resolution / movie_dir_name

            if not stage3_movie_dir.exists():
                logger.warning(f"Stage 3 movie directory not found: {stage3_movie_dir}")
                return

            # 1. Delete the .processed.mkv file
            processed_mkv_file = stage3_movie_dir / f"{movie.title} ({movie.year}).processed.mkv"
            if processed_mkv_file.exists():
                processed_mkv_file.unlink()
                logger.info(f"🗑️ Deleted processed MKV file: {processed_mkv_file.name}")
            else:
                logger.warning(f"Processed MKV file not found: {processed_mkv_file}")

            # 2. Delete the entire _Processed_Audio folder
            processed_audio_dir = stage3_movie_dir / "_Processed_Audio"
            if processed_audio_dir.exists():
                shutil.rmtree(processed_audio_dir)
                logger.info(f"🗑️ Deleted processed audio folder: {processed_audio_dir}")
            else:
                logger.warning(f"Processed audio folder not found: {processed_audio_dir}")

            logger.info(f"✅ Stage 3 cleanup completed for {movie.title}")

        except Exception as e:
            logger.error(f"Failed to cleanup stage 3 files for {movie.title}: {e}")
            raise

    def save_encoding_metadata(self, movie: MovieInfo, params: EncodingParameters, output_path: Path):
        """
        Save encoding metadata to the metadata-only database
        
        Args:
            movie: MovieInfo object
            params: Encoding parameters used
            output_path: Path to encoded output file
        """
        try:
            # Prepare metadata
            encoding_metadata = {
                'encoding_preset': params.preset,
                'target_bitrate_kbps': params.target_bitrate_kbps,
                'encoder_profile': params.encoder_profile,
                'encoder_level': params.encoder_level,
                'turbo_first_pass': params.turbo_first_pass,
                'handbrake_preset': params.handbrake_preset,
                'encoded_file_path': str(output_path),
                'encoding_timestamp': datetime.now().isoformat(),
                'resolution': movie.resolution
            }
            
            # Get existing metadata if any
            existing_metadata = self.metadata_db.get_movie_metadata(movie.unique_id)
            if existing_metadata:
                # Update existing metadata
                current_metadata = existing_metadata.get('metadata', {})
                current_metadata.update(encoding_metadata)
                
                self.metadata_db.save_movie_metadata(
                    unique_id=movie.unique_id,
                    title=movie.title,
                    year=int(movie.year) if movie.year.isdigit() else None,
                    metadata=current_metadata
                )
            else:
                # Create new metadata entry
                self.metadata_db.save_movie_metadata(
                    unique_id=movie.unique_id,
                    title=movie.title,
                    year=int(movie.year) if movie.year.isdigit() else None,
                    metadata=encoding_metadata
                )
            
            logger.info(f"Saved encoding metadata for {movie.title}")
            
        except Exception as e:
            logger.error(f"Failed to save encoding metadata for {movie.title}: {e}")

    def process_movie(self, movie: MovieInfo) -> bool:
        """
        Process a single movie through the encoding pipeline using filesystem-first approach
        
        Args:
            movie: MovieInfo object to process
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Processing movie: {movie.title} ({movie.year})")
            
            # FILESYSTEM-FIRST: Don't set processing markers in source directory
            # The .mkv_complete marker in folder 3 is sufficient for tracking
            
            # Get encoding parameters
            params = self.get_encoding_parameters(movie)
            
            # Get user confirmation and potential overrides
            should_proceed, params = self.prompt_user_confirmation(movie, params)
            
            if not should_proceed:
                logger.info(f"Skipping {movie.title} at user request")
                # Clear processing marker since we're not proceeding
                self.clear_encoding_markers(movie, 'mkv_processing')
                return False
            
            # Determine output path
            output_path = self.get_output_path(movie, params)
            logger.info(f"Output path: {output_path}")
            
            # Build HandBrake command
            cmd = self.build_handbrake_command(movie, params, output_path)
            
            # Run encoding
            success, error_msg = self.run_handbrake_encoding(cmd, movie)
            
            if success:
                # Verify output file
                if self.verify_output_file(output_path, movie.duration_seconds):
                    # FILESYSTEM-FIRST: Set .encoded marker in OUTPUT directory
                    output_movie_dir = output_path.parent  # Destination directory
                    self.set_encoding_markers_in_dir(output_movie_dir, 'encoded', {
                        'completion_time': datetime.now().isoformat(),
                        'output_path': str(output_path),
                        'encoding_parameters': {
                            'preset': params.preset,
                            'bitrate_kbps': params.target_bitrate_kbps,
                            'profile': params.encoder_profile,
                            'level': params.encoder_level,
                            'audio_codec': params.audio_codec,
                            'audio_bitrate_kbps': params.audio_bitrate_kbps,
                            'audio_mixdown': params.audio_mixdown
                        }
                    })

                    # Also set .subtitle_processing_pending marker to indicate subtitles need processing
                    self.set_encoding_markers_in_dir(output_movie_dir, 'subtitle_processing_pending', {
                        'created_time': datetime.now().isoformat(),
                        'ready_for_subtitle_processing': True
                    })

                    # Clear old markers from SOURCE directory
                    self.clear_encoding_markers(movie, 'mkv_processing')
                    self.clear_encoding_markers(movie, 'mkv_complete')  # Clear any old markers

                    # Clean up after successful encoding
                    try:
                        from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager
                        fs_manager = FilesystemFirstStateManager(Path.cwd())
                        cleanup_success = fs_manager.cleanup_stage_after_completion(output_movie_dir, "video_encoding")
                        if cleanup_success:
                            logger.info(f"✅ Cleanup completed after video encoding")
                    except Exception as e:
                        logger.warning(f"⚠️ Cleanup failed but processing continues: {e}")

                    # Copy largest audio file from stage 3 to stage 4
                    try:
                        self.copy_largest_audio_file(movie, output_movie_dir)
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to copy largest audio file: {e}, but encoding continues")

                    # Clean up stage 3 files after successful encoding
                    try:
                        self.cleanup_stage3_after_encoding(movie)
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to cleanup stage 3 files: {e}, but encoding continues")

                    # Save metadata
                    self.save_encoding_metadata(movie, params, output_path)

                    logger.info(f"Successfully processed {movie.title}")
                    print(f"✓ Encoding complete for {movie.title}")
                    print(f"  Output: {output_path}")
                    return True
                else:
                    # Verification failed - set error marker
                    error_msg = "Output file verification failed"
                    self.set_encoding_markers(movie, 'error', {
                        'error_time': datetime.now().isoformat(),
                        'error_message': error_msg,
                        'stage': 'video_encoding_verification'
                    })
                    self.clear_encoding_markers(movie, 'mkv_processing')
                    logger.error(f"Verification failed for {movie.title}")
                    return False
            else:
                # Encoding failed - set error marker
                self.set_encoding_markers(movie, 'error', {
                    'error_time': datetime.now().isoformat(),
                    'error_message': error_msg,
                    'stage': 'video_encoding_handbrake'
                })
                self.clear_encoding_markers(movie, 'mkv_processing')
                logger.error(f"Encoding failed for {movie.title}: {error_msg}")
                
                # Ask user if they want to retry with different settings
                if self.prompt_yes_no("Encoding failed. Try again with different settings?", default=False):
                    # Clear error marker to allow retry
                    self.clear_encoding_markers(movie, 'error')
                
                return False
                
        except Exception as e:
            error_msg = f"Unexpected error processing {movie.title}: {str(e)}"
            logger.error(error_msg)
            
            # Set error marker
            self.set_encoding_markers(movie, 'error', {
                'error_time': datetime.now().isoformat(),
                'error_message': error_msg,
                'stage': 'video_encoding_exception'
            })
            self.clear_encoding_markers(movie, 'mkv_processing')
            return False

    def prompt_yes_no(self, question: str, default: bool = True) -> bool:
        """
        Prompt user for yes/no response
        
        Args:
            question: Question to ask
            default: Default response if user just presses Enter
            
        Returns:
            True for yes, False for no
        """
        default_text = "Y/n" if default else "y/N"
        response = input(f"{question} [{default_text}]: ").upper().strip()
        
        if response == "":
            return default
        elif response in ["Y", "YES"]:
            return True
        elif response in ["N", "NO"]:
            return False
        else:
            print("Please answer yes or no.")
            return self.prompt_yes_no(question, default)

    def run_batch_processing(self, auto_confirm: bool = False):
        """
        Run batch processing of all movies ready for encoding using filesystem-first discovery
        
        Args:
            auto_confirm: If True, skip user confirmation prompts
        """
        logger.info("Starting filesystem-first video encoding batch processing")
        
        # Get movies to process using filesystem discovery
        movies = self.discover_movies_for_encoding()
        
        if not movies:
            print("No movies found ready for encoding.")
            logger.info("No movies ready for encoding")
            return
        
        print(f"\nFound {len(movies)} movies ready for encoding:")
        for i, movie in enumerate(movies, 1):
            print(f"  {i}. {movie.title} ({movie.year}) - {movie.resolution} [{movie.current_stage}]")
        
        if not auto_confirm:
            if not self.prompt_yes_no(f"\nProcess all {len(movies)} movies?", default=True):
                print("Batch processing cancelled.")
                return
        
        # Process each movie
        successful = 0
        failed = 0
        skipped = 0
        
        for i, movie in enumerate(movies, 1):
            print(f"\n{'='*60}")
            print(f"Processing movie {i}/{len(movies)}")
            print(f"{'='*60}")
            
            try:
                if auto_confirm:
                    # Skip user confirmation, use default parameters
                    params = self.get_encoding_parameters(movie)
                    output_path = self.get_output_path(movie, params)
                    
                    # Set processing marker
                    self.set_encoding_markers(movie, 'mkv_processing', {
                        'start_time': datetime.now().isoformat(),
                        'auto_confirm': True
                    })
                    
                    # Build and run command
                    cmd = self.build_handbrake_command(movie, params, output_path)
                    success, error_msg = self.run_handbrake_encoding(cmd, movie)
                    
                    if success and self.verify_output_file(output_path, movie.duration_seconds):
                        # Set .encoded marker and save metadata
                        self.set_encoding_markers(movie, 'encoded', {
                            'completion_time': datetime.now().isoformat(),
                            'output_path': str(output_path),
                            'encoding_parameters': {
                                'preset': params.preset,
                                'bitrate_kbps': params.target_bitrate_kbps,
                                'audio_codec': params.audio_codec,
                                'audio_bitrate_kbps': params.audio_bitrate_kbps
                            }
                        })

                        # Also set .subtitle_processing_pending marker
                        self.set_encoding_markers(movie, 'subtitle_processing_pending', {
                            'created_time': datetime.now().isoformat(),
                            'ready_for_subtitle_processing': True
                        })
                        self.clear_encoding_markers(movie, 'mkv_processing')
                        self.save_encoding_metadata(movie, params, output_path)
                        successful += 1
                        logger.info(f"Auto-processed {movie.title} successfully")
                    else:
                        # Set error marker
                        self.set_encoding_markers(movie, 'error', {
                            'error_time': datetime.now().isoformat(),
                            'error_message': error_msg or "Verification failed",
                            'auto_confirm': True
                        })
                        self.clear_encoding_markers(movie, 'mkv_processing')
                        failed += 1
                        logger.error(f"Auto-processing failed for {movie.title}")
                else:
                    # Interactive processing
                    result = self.process_movie(movie)
                    if result:
                        successful += 1
                    else:
                        # Check if it was skipped (no error marker) or failed (has error marker)
                        if (movie.movie_directory / '.error').exists():
                            failed += 1
                        else:
                            skipped += 1
                        
            except KeyboardInterrupt:
                print("\nBatch processing interrupted by user.")
                logger.info("Batch processing interrupted")
                break
            except Exception as e:
                logger.error(f"Unexpected error processing {movie.title}: {e}")
                # Set error marker for unexpected errors
                self.set_encoding_markers(movie, 'error', {
                    'error_time': datetime.now().isoformat(),
                    'error_message': f"Unexpected error: {str(e)}",
                    'stage': 'batch_processing'
                })
                failed += 1
        
        # Print summary
        print(f"\n{'='*60}")
        print(f"Batch Processing Complete")
        print(f"{'='*60}")
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        if not auto_confirm:
            print(f"Skipped: {skipped}")
        print(f"Total: {len(movies)}")
        
        logger.info(f"Batch processing complete: {successful} successful, {failed} failed, {skipped} skipped")

    def run_episode_batch_processing(self, auto_confirm: bool = False):
        """
        Run batch processing of all TV episodes ready for encoding using filesystem-first discovery
        
        TV Show equivalent of run_batch_processing() with episode-specific handling:
        - Episode-based discovery and processing
        - Season/series batch organization
        - Same sophisticated processing workflow as movies
        - Episode-specific user interaction and confirmation
        
        Args:
            auto_confirm: If True, skip user confirmation prompts
        """
        logger.info("Starting filesystem-first TV episode encoding batch processing")
        
        # Get episodes to process using TV-specific filesystem discovery
        episodes = self.discover_episodes_for_encoding()
        
        if not episodes:
            print("No TV episodes found ready for encoding.")
            logger.info("No episodes ready for encoding")
            return
        
        # Group episodes by series for better display
        episodes_by_series = {}
        for episode in episodes:
            series_key = episode.series_title
            if series_key not in episodes_by_series:
                episodes_by_series[series_key] = []
            episodes_by_series[series_key].append(episode)
        
        print(f"\nFound {len(episodes)} episodes ready for encoding:")
        episode_num = 1
        for series_title, series_episodes in episodes_by_series.items():
            print(f"\n📺 {series_title}:")
            for episode in sorted(series_episodes, key=lambda x: (x.season_number, x.episode_number)):
                print(f"  {episode_num}. S{episode.season_number:02d}E{episode.episode_number:02d} - {episode.episode_title[:30]}... [{episode.current_stage}]")
                episode_num += 1
        
        if not auto_confirm:
            if not self.prompt_yes_no(f"\nProcess all {len(episodes)} episodes?", default=True):
                print("Episode batch processing cancelled.")
                return
        
        # Process each episode
        successful = 0
        failed = 0
        skipped = 0
        
        for i, episode in enumerate(episodes, 1):
            print(f"\n{'='*70}")
            print(f"Processing episode {i}/{len(episodes)}")
            print(f"📺 {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}")
            if episode.episode_title:
                print(f"   \"{episode.episode_title}\"")
            print(f"{'='*70}")
            
            try:
                if auto_confirm:
                    # Skip user confirmation, use default parameters
                    params = self.get_episode_encoding_parameters(episode)
                    output_path = self.get_episode_output_path(episode, params)
                    
                    # Set processing marker for episode
                    self.set_episode_encoding_markers(episode, 'encoding_started', {
                        'start_time': datetime.now().isoformat(),
                        'parameters': {
                            'preset': params.preset,
                            'target_bitrate_kbps': params.target_bitrate_kbps,
                            'resolution': params.resolution
                        },
                        'output_path': str(output_path)
                    })
                    
                    print(f"Auto-encoding episode: {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}")
                    print(f"Target bitrate: {params.target_bitrate_kbps/1000:.1f} Mbps")
                    print(f"Output: {output_path}")
                else:
                    # Interactive mode with user confirmation
                    params = self.get_episode_encoding_parameters(episode)
                    should_proceed, params = self.prompt_episode_user_confirmation(episode, params)
                    
                    if not should_proceed:
                        print(f"Skipped episode: {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}")
                        skipped += 1
                        continue
                    
                    output_path = self.get_episode_output_path(episode, params)
                    
                    # Set processing marker for episode
                    self.set_episode_encoding_markers(episode, 'encoding_started', {
                        'start_time': datetime.now().isoformat(),
                        'parameters': {
                            'preset': params.preset,
                            'target_bitrate_kbps': params.target_bitrate_kbps,
                            'resolution': params.resolution
                        },
                        'output_path': str(output_path)
                    })
                
                # Build HandBrake command for episode
                cmd = self.build_episode_handbrake_command(episode, params, output_path)
                
                # Execute episode encoding
                success, error_msg = self.run_handbrake_encoding(cmd, episode)
                
                if success:
                    # Set completion marker for episode
                    self.set_episode_encoding_markers(episode, 'encoding_complete', {
                        'completion_time': datetime.now().isoformat(),
                        'output_file': str(output_path),
                        'parameters_used': {
                            'preset': params.preset,
                            'target_bitrate_kbps': params.target_bitrate_kbps,
                            'resolution': params.resolution
                        }
                    })
                    
                    print(f"✅ Successfully encoded episode: {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}")
                    successful += 1
                else:
                    # Set error marker for episode
                    self.set_episode_encoding_markers(episode, 'error', {
                        'error_time': datetime.now().isoformat(),
                        'error_message': error_msg,
                        'stage': 'episode_encoding'
                    })
                    
                    print(f"❌ Failed to encode episode: {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}")
                    print(f"   Error: {error_msg}")
                    failed += 1
                    
            except Exception as e:
                logger.error(f"Unexpected error encoding episode {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d}: {e}")
                
                # Set error marker for unexpected errors
                self.set_episode_encoding_markers(episode, 'error', {
                    'error_time': datetime.now().isoformat(),
                    'error_message': f"Unexpected error: {str(e)}",
                    'stage': 'episode_batch_processing'
                })
                failed += 1
        
        # Print episode batch summary
        print(f"\n{'='*70}")
        print(f"Episode Batch Processing Complete")
        print(f"{'='*70}")
        print(f"Successful episodes: {successful}")
        print(f"Failed episodes: {failed}")
        if not auto_confirm:
            print(f"Skipped episodes: {skipped}")
        print(f"Total episodes: {len(episodes)}")
        
        logger.info(f"Episode batch processing complete: {successful} successful, {failed} failed, {skipped} skipped")

    def cleanup_stale_markers(self):
        """
        Clean up stale or conflicting marker files across all movies
        """
        logger.info("Cleaning up stale encoding markers...")
        
        all_movies = self.fs_manager.discover_movies_by_stage()
        cleaned_count = 0
        
        for stage_name, movies in all_movies.items():
            for movie_data in movies:
                movie_dir = Path(movie_data.get('movie_directory', ''))
                if movie_dir.exists():
                    try:
                        self.fs_manager.cleanup_stale_markers(movie_dir)
                        cleaned_count += 1
                    except Exception as e:
                        logger.warning(f"Failed to cleanup markers for {movie_dir.name}: {e}")
        
        logger.info(f"Cleaned up markers for {cleaned_count} movies")

    def show_encoding_status(self):
        """
        Show current encoding status of all movies using filesystem-first discovery
        """
        print(f"\n{'='*60}")
        print(f"Video Encoding Status Dashboard")
        print(f"{'='*60}")
        
        all_movies = self.fs_manager.discover_movies_by_stage()
        
        # Count movies by encoding stage
        ready_for_encoding = len(all_movies.get('video_encoding_pending', []))
        currently_encoding = 0
        completed_encoding = 0
        failed_encoding = 0
        
        # Count interrupted/error states
        for stage_name, movies in all_movies.items():
            for movie_data in movies:
                movie_dir = Path(movie_data.get('movie_directory', ''))
                if movie_dir.exists():
                    if (movie_dir / '.mkv_processing').exists() and not (movie_dir / '.mkv_complete').exists():
                        currently_encoding += 1
                    elif (movie_dir / '.mkv_complete').exists():
                        completed_encoding += 1
                    elif (movie_dir / '.error').exists():
                        # Check if error is encoding related
                        error_data = self.fs_manager.get_stage_marker_data(movie_dir, 'error')
                        if error_data and 'video_encoding' in error_data.get('stage', ''):
                            failed_encoding += 1
        
        print(f"Ready for encoding: {ready_for_encoding}")
        print(f"Currently encoding: {currently_encoding}")
        print(f"Completed encoding: {completed_encoding}")
        print(f"Failed encoding: {failed_encoding}")
        
        # Show details for movies needing attention
        if currently_encoding > 0:
            print(f"\n📹 Movies currently encoding:")
            for stage_name, movies in all_movies.items():
                for movie_data in movies:
                    movie_dir = Path(movie_data.get('movie_directory', ''))
                    if movie_dir.exists() and (movie_dir / '.mkv_processing').exists():
                        title = movie_data.get('cleaned_title', 'Unknown')
                        year = movie_data.get('year', 'Unknown')
                        print(f"   • {title} ({year})")
        
        if failed_encoding > 0:
            print(f"\n❌ Movies with encoding errors:")
            for stage_name, movies in all_movies.items():
                for movie_data in movies:
                    movie_dir = Path(movie_data.get('movie_directory', ''))
                    if movie_dir.exists() and (movie_dir / '.error').exists():
                        error_data = self.fs_manager.get_stage_marker_data(movie_dir, 'error')
                        if error_data and 'video_encoding' in error_data.get('stage', ''):
                            title = movie_data.get('cleaned_title', 'Unknown')
                            year = movie_data.get('year', 'Unknown')
                            error_msg = error_data.get('error_message', 'Unknown error')
                            print(f"   • {title} ({year}): {error_msg}")


def display_interactive_menu():
    """
    Display the main interactive menu for content type selection.

    Returns:
        str: Selected content type ('movies', 'tv_shows', 'both', 'quit')
    """
    print(f"\n{'='*60}")
    print(f"🎬📺 PlexMovieAutomator - Interactive Content Selection")
    print(f"{'='*60}")
    print(f"\nWhat type of content would you like to process?")
    print(f"  1. Movies only")
    print(f"  2. TV Shows only")
    print(f"  3. Both Movies and TV Shows")
    print(f"  4. Quit")

    while True:
        try:
            choice = input(f"\nEnter your choice [1-4]: ").strip()

            if choice == '1':
                return 'movies'
            elif choice == '2':
                return 'tv_shows'
            elif choice == '3':
                return 'both'
            elif choice == '4':
                return 'quit'
            else:
                print(f"Please enter a number between 1 and 4")

        except KeyboardInterrupt:
            print(f"\n👋 Exiting...")
            return 'quit'


def main():
    """Main entry point for the filesystem-first video encoder script"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Filesystem-First HandBrake Video Re-encoding Script")
    parser.add_argument("--handbrake-path", help="Path to HandBrakeCLI.exe")
    parser.add_argument("--workspace", help="Workspace base directory", default=".")
    parser.add_argument("--auto-confirm", action="store_true", help="Auto-confirm all encoding with default parameters")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without actually encoding")
    parser.add_argument("--status", action="store_true", help="Show encoding status dashboard")
    parser.add_argument("--cleanup", action="store_true", help="Clean up stale marker files")
    parser.add_argument("--movies-only", action="store_true", help="Process only movies (command-line mode)")
    parser.add_argument("--tv-only", action="store_true", help="Process only TV shows (command-line mode)")
    parser.add_argument("--all", action="store_true", help="Process both movies and TV shows (command-line mode)")
    # Legacy support
    parser.add_argument("--tv-shows", action="store_true", help="Legacy: Process TV episodes (use --tv-only instead)")
    parser.add_argument("--episodes", action="store_true", help="Legacy: Alias for --tv-shows (use --tv-only instead)")
    parser.add_argument("--interactive", action="store_true", help="Force interactive mode (default behavior)")
    
    args = parser.parse_args()
    
    print("🎬 Video Encoder - Pipeline 04")
    print("   Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)")
    
    try:
        # Initialize filesystem-first encoder
        encoder = FilesystemFirstVideoEncoder(
            handbrake_path=args.handbrake_path,
            workspace_path=args.workspace
        )
        
        if args.cleanup:
            print("Cleaning up stale marker files...")
            encoder.cleanup_stale_markers()
            print("Cleanup complete.")
            return
        
        if args.status:
            encoder.show_encoding_status()
            return

        # Default to interactive mode unless command-line content arguments are specified
        if args.movies_only or args.tv_only or args.all or args.tv_shows or args.episodes:
            # Command line mode - user specified content type arguments
            if args.movies_only:
                content_type_choice = 'movies'
            elif args.tv_only or args.tv_shows or args.episodes:
                content_type_choice = 'tv_shows'
            else:  # args.all
                content_type_choice = 'both'
        else:
            # Interactive mode (default behavior)
            content_type_choice = display_interactive_menu()
            
            if content_type_choice == 'quit':
                print("👋 Exiting...")
                return
        
        if args.dry_run:
            print("DRY RUN MODE - No actual encoding will be performed")
            
            if content_type_choice in ['tv_shows', 'both']:
                # TV Episodes dry run
                episodes = encoder.discover_episodes_for_encoding()
                if episodes:
                    print(f"\nWould process {len(episodes)} TV episodes:")
                    for episode in episodes:
                        params = encoder.get_episode_encoding_parameters(episode)
                        output_path = encoder.get_episode_output_path(episode, params)
                        print(f"  • {episode.series_title} S{episode.season_number:02d}E{episode.episode_number:02d} - {params.resolution}")
                        print(f"    Target: {params.target_bitrate_kbps/1000:.1f} Mbps")
                        print(f"    Output: {output_path}")
                        print(f"    Current stage: {episode.current_stage}")
                else:
                    print("No TV episodes found ready for encoding.")
            
            if content_type_choice in ['movies', 'both']:
                # Movies dry run
                movies = encoder.discover_movies_for_encoding()
                if movies:
                    print(f"\nWould process {len(movies)} movies:")
                    for movie in movies:
                        params = encoder.get_encoding_parameters(movie)
                        output_path = encoder.get_output_path(movie, params)
                        print(f"  • {movie.title} ({movie.year}) - {params.resolution}")
                        print(f"    Target: {params.target_bitrate_kbps/1000:.1f} Mbps")
                        print(f"    Output: {output_path}")
                        print(f"    Current stage: {movie.current_stage}")
                else:
                    print("No movies found ready for encoding.")
        else:
            # Run batch processing with filesystem-first architecture
            if content_type_choice in ['tv_shows', 'both']:
                print("📺 Starting TV Episode Video Encoding...")
                encoder.run_episode_batch_processing(auto_confirm=args.auto_confirm)
            
            if content_type_choice in ['movies', 'both']:
                print("🎬 Starting Movie Video Encoding...")
                encoder.run_batch_processing(auto_confirm=args.auto_confirm)
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Please ensure HandBrakeCLI.exe is available in the tools directory or specify --handbrake-path")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"An unexpected error occurred: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
