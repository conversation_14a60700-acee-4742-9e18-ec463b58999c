=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-15 02:50:25
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-15_02-50-25-AM.txt
==================================================

[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-15_02-50-25-AM.txt
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-15 02:50:25
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDERR] [+0:00:00] 2025-09-15 02:50:25,967 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDERR] [+0:00:00] 2025-09-15 02:50:25,968 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-15 02:50:25] [STDERR] [+0:00:00] 2025-09-15 02:50:25,968 - interactive_pipeline_01 - WARNING - ⚠️ Failed to evaluate auto-start for Stage 02: cannot access local variable '_get' where it is not associated with a value
[2025-09-15 02:50:25] [STDERR] [+0:00:00] 2025-09-15 02:50:25,969 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-15 02:50:25] [STDERR] [+0:00:00] 2025-09-15 02:50:25,969 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-15 02:50:25] [STDERR] [+0:00:00] 2025-09-15 02:50:25,969 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-15 02:50:25] [STDERR] [+0:00:00] 2025-09-15 02:50:25,970 - interactive_pipeline_01 - INFO -    📊 Loaded 81 existing movie records
[2025-09-15 02:50:25] [STDERR] [+0:00:00] 2025-09-15 02:50:25,970 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:25] [STDOUT] [+0:00:00]   4. Quit
[2025-09-15 02:50:25] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 📁 Loaded 6 movies from C:\Users\<USER>\Videos\PlexAutomator\new_movie_requests.txt
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 🎬 Movies Available for Processing:
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]    1. 13 Going on 30 (2004)
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]    2. Don't Breathe (2016)
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]    3. Top Gun: Maverick (2022)
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]    4. There Will Be Blood (2007)
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]    5. Star Trek Into Darkness (2013)
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]    6. The Dark Knight (2008)
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 📝 Selection Options:
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]   • Single: Enter number (e.g., '3')
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]   • All: Enter 'all' or 'a'
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]   • None: Enter 'none' or 'n' to skip
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:26] [STDOUT] [+0:00:00]   • Quit: Enter 'quit' or 'q'
[2025-09-15 02:50:26] [STDOUT] [+0:00:00] 
[2025-09-15 02:50:29] [STDOUT] [+0:00:03] ✅ Selected 1 movies:
[2025-09-15 02:50:29] [STDOUT] [+0:00:03] 
[2025-09-15 02:50:29] [STDOUT] [+0:00:03]     1. 13 Going on 30 (2004)
[2025-09-15 02:50:29] [STDOUT] [+0:00:03] 
[2025-09-15 02:50:31] [STDOUT] [+0:00:05] 
[2025-09-15 02:50:31] [STDOUT] [+0:00:05] 🎬 Processing 1 selected movies...
[2025-09-15 02:50:31] [STDOUT] [+0:00:05] 
[2025-09-15 02:50:31] [STDOUT] [+0:00:05] ============================================================
[2025-09-15 02:50:31] [STDOUT] [+0:00:05] 
[2025-09-15 02:50:31] [STDERR] [+0:00:05] 2025-09-15 02:50:31,018 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-15 02:50:31] [STDOUT] [+0:00:05] 
[2025-09-15 02:50:31] [STDOUT] [+0:00:05] 📍 Progress: 1/1
[2025-09-15 02:50:31] [STDOUT] [+0:00:05] 
[2025-09-15 02:50:31] [STDOUT] [+0:00:05] 🎬 Processing: 13 Going on 30 (2004)
[2025-09-15 02:50:31] [STDOUT] [+0:00:05] 
[2025-09-15 02:50:31] [STDERR] [+0:00:05] 2025-09-15 02:50:31,019 - interactive_pipeline_01 - INFO - Processing movie: 13 Going on 30 (2004)
[2025-09-15 02:50:31] [STDERR] [+0:00:05] 2025-09-15 02:50:31,233 - utils.metadata_apis - INFO - Generated 2 search variants: ['13 Going on 30', 'the 13 Going on 30']
[2025-09-15 02:50:31] [STDERR] [+0:00:05] 2025-09-15 02:50:31,369 - utils.metadata_apis - INFO - TMDb search for '13 Going on 30' (Year: 2004) found 1 results.
[2025-09-15 02:50:31] [STDERR] [+0:00:05] 2025-09-15 02:50:31,369 - utils.metadata_apis - INFO - TMDb search '13 Going on 30' with year 2004: 1 results
[2025-09-15 02:50:31] [STDERR] [+0:00:05] 2025-09-15 02:50:31,488 - utils.metadata_apis - INFO - TMDb search for '13 Going on 30' (Year: None) found 1 results.
[2025-09-15 02:50:31] [STDERR] [+0:00:05] 2025-09-15 02:50:31,489 - utils.metadata_apis - INFO - TMDb search '13 Going on 30' without year: 1 results
[2025-09-15 02:50:31] [STDERR] [+0:00:05] 2025-09-15 02:50:31,608 - utils.metadata_apis - INFO - TMDb search for 'the 13 Going on 30' (Year: 2004) found 0 results.
[2025-09-15 02:50:31] [STDERR] [+0:00:05] 2025-09-15 02:50:31,727 - utils.metadata_apis - INFO - TMDb search for 'the 13 Going on 30' (Year: None) found 0 results.
[2025-09-15 02:50:31] [STDERR] [+0:00:05] 2025-09-15 02:50:31,727 - utils.metadata_apis - INFO - Total unique results from all variants: 1
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,315 - utils.metadata_apis - INFO - Auto-approved match: High confidence match (100.0%) - auto-approved
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,315 - utils.metadata_apis - INFO - Selected TMDb match for '13 Going on 30 (2004)': ID=10096, Title='13 Going on 30', Confidence=100.0%
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,423 - utils.metadata_apis - INFO - Fetched details for TMDb ID 10096: 13 Going on 30
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] ✅ Found metadata: 13 Going on 30 (2004)
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,424 - interactive_pipeline_01 - INFO - Successfully found metadata for: 13 Going on 30 (2004)
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,427 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2004: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,427 - interactive_pipeline_01 - INFO - Searching Radarr for: 13 Going on 30 2004
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,508 - interactive_pipeline_01 - INFO - Found match: 13 Going on 30 (2004)
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,510 - interactive_pipeline_01 - INFO - 🔍 Checking for duplicates: TMDB ID 10096 in 0 existing movies
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,510 - interactive_pipeline_01 - INFO - ✅ NO DUPLICATE: Movie TMDB 10096 not found in Radarr - safe to add
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,513 - interactive_pipeline_01 - INFO - 🔁 Using existing Radarr root folder: E:\
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,513 - interactive_pipeline_01 - INFO - 📋 ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,513 - interactive_pipeline_01 - INFO - 🎬 Adding movie to Radarr with 1 quality profile(s): 13 Going on 30 2004
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,513 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 4 (searchForMovie=False)...
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,546 - interactive_pipeline_01 - INFO -    ✅ Successfully added: 13 Going on 30 2004 (ID: 673, Profile: 4)
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 📥 Queued "13 Going on 30 (2004)" for download...
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,548 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-15T02:50:32.548089", "event": "download_queued", "job_id": "radarr_673", "title": "13 Going on 30 (2004)", "source": "radarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "radarr_id": 673, "quality": "Unknown"}
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,548 - interactive_pipeline_01 - INFO - 📋 Enhanced tracking: 13 Going on 30 (2004)
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,548 - interactive_pipeline_01 - INFO -    🆔 Radarr ID: 673
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,548 - interactive_pipeline_01 - INFO -    📊 Job ID: radarr_673
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 📊 Movie queued for download: 13 Going on 30 (2004)
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06]    🔬 Enhanced tracking: radarr_6...
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06]    🆔 Radarr ID: 673
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06]    🛡️ Fallback protection: Enabled
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,551 - interactive_pipeline_01 - INFO - Phase 1: Enhanced telemetry job started: radarr_673 for movie 673
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,551 - interactive_pipeline_01 - INFO - Movie ID 673 tracked for accurate correlation
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,551 - interactive_pipeline_01 - INFO - 🚀 EARLY TELEMETRY: Starting detached telemetry monitor (same system as Script 1)
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 🚀 Starting detached telemetry monitoring NOW (same dashboard, independent process)
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 🧭 Starting standalone telemetry monitor (logs: logs\standalone_telemetry_monitor.log)...
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDERR] [+0:00:06] 2025-09-15 02:50:32,554 - interactive_pipeline_01 - INFO - ✅ Standalone telemetry monitor started - same system, detached process
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] ✅ Telemetry monitoring active - will continue after Script 1 completes
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06]    📄 Monitor logs: logs\standalone_telemetry_monitor.log
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 🤔 Download Strategy Choice for: 13 Going on 30
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] Choose how you want to handle downloads for this movie:
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 2. ⚡ Radarr Auto-Grab - Let Radarr immediately search and grab based on quality profiles
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 3. ⏭️  Skip - Add to Radarr but don't start any downloads yet
[2025-09-15 02:50:32] [STDOUT] [+0:00:06] 
[2025-09-15 02:50:38] [STDERR] [+0:00:12] 2025-09-15 02:50:38,484 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2004: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 🐛 DEBUG: sanitized movie_title = '13_Going_on_30'
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 🐛 DEBUG: final out_path = 'workspace\preflight_decisions\13_Going_on_30.json'
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDERR] [+0:00:12] 2025-09-15 02:50:38,484 - interactive_pipeline_01 - INFO - ✅ Found existing preflight decision: workspace\preflight_decisions\13_Going_on_30.json
[2025-09-15 02:50:38] [STDERR] [+0:00:12] 2025-09-15 02:50:38,485 - interactive_pipeline_01 - INFO - 📝 Loaded existing movie preflight decision for: 13_Going_on_30
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] ✅ Using existing preflight decision for: 13 Going on 30
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 📊 Preflight Results: 22 analyzed, 22 acceptable, 0 errors
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 📊 Combined Results: 1 total movie analyzed, 22 acceptable releases found
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 🔬 Movie Preflight Analysis Results:
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #1. 🎬 13.Going.On.30.2004.720p.BluRay.X264-x0r[EXTRA-Deleted Scenes]
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 0.28 GB (300,389,461 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0958 | Missing: 0.5% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #2. 🎬 13.Going.On.30.2004.1080p.BluRay.x265
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 1.73 GB (1,860,169,468 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0153 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #3. 🎬 13.Going.On.30.2004.BDRip.1080p.x265-FLC.22
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 2.27 GB (2,436,351,791 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0218 | Missing: 0.3% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #4. 🎬 13.Going.On.30.2004.BDRip.1080p.X265-FLC
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 2.27 GB (2,436,930,115 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0005 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #5. 🎬 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 2.34 GB (2,517,640,816 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0053 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #6. 🎬 13.Going.On.30.2004.720p.BluRay.x264-x0r
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 2.63 GB (2,828,226,322 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0953 | Missing: 0.3% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #7. 🎬 13.Going.on.30.(2004).(1080p.BluRay.x265.10bit.HEVC.AC3.5.1.-.H4XO)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 4.56 GB (4,897,553,218 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0078 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #8. 🎬 30.ueber.Nacht.2004.German.720p.BluRay.x264-DETAiLS
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 4.59 GB (4,923,901,037 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0207 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #9. 🎬 13.Going.on.30.2004.1080p.HULU.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 4.64 GB (4,984,523,381 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0094 | Missing: 0.3% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #10. 🎬 13.Going.on.30.2004.720p.BluRay.x264-METiS
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 4.78 GB (5,136,435,650 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.1548 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #11. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-OFT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 5.12 GB (5,493,911,649 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0032 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #12. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-nikt0
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 5.12 GB (5,494,056,722 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0960 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #13. 🎬 13 Going on 30 2004 1080p NF WEB-DL DUAL DD5.1 H.264-BdC
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 5.46 GB (5,859,882,678 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0892 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #14. 🎬 13.Going.On.30.2004.720p.BluRay.DD5.1.x264-CRiSC
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 8.10 GB (8,699,365,146 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0002 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #15. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-CtrlHD
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 8.61 GB (9,247,582,818 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0965 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #16. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-METiS
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 9.02 GB (9,688,109,054 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0025 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #17. 🎬 13 Going on 30 2004.1080p.AC3-NoGroup
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 9.09 GB (9,762,208,782 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.2045 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #18. 🎬 13.Going.on.30.2004.1080p.AMZN.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 10.50 GB (11,273,989,818 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0061 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #19. 🎬 13.Going.On.30.2004.1080p.BluRay.x264-MonteDiaz
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 10.77 GB (11,566,818,530 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0004 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #20. 🎬 13.Going.on.30.2004.1080p.Blu-ray.Remux.AVC.Dolby.TrueHD.5.1-unc0mpressed
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 21.12 GB (22,675,404,920 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0335 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #21. 🎬 13.Going.on.30.2004.1080p.BluRay.REMUX.AVC.TrueHD.5.1-EPSiLON
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 22.84 GB (24,525,709,753 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0819 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    #22. 🎬 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        💾 Size: 22.93 GB (24,616,529,445 bytes)
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]        ⚡ Risk: 0.0013 | Missing: 0.3% | Decision: ACCEPT
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 🔬 Preflight selection (best candidate):
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    • 22.93 GB  |  ACCEPT  |  risk: 0.0013  |  missing: 0.3%
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:38] [STDOUT] [+0:00:12]    • Release: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-15 02:50:38] [STDOUT] [+0:00:12] 
[2025-09-15 02:50:40] [STDOUT] [+0:00:14] 🔍 DEBUG: Checking candidate storage conditions...
[2025-09-15 02:50:40] [STDOUT] [+0:00:14] 
[2025-09-15 02:50:40] [STDOUT] [+0:00:14]    telemetry_integrator: True
[2025-09-15 02:50:40] [STDOUT] [+0:00:14] 
[2025-09-15 02:50:40] [STDOUT] [+0:00:14]    telemetry_integrator.telemetry: True
[2025-09-15 02:50:40] [STDOUT] [+0:00:14] 
[2025-09-15 02:50:40] [STDOUT] [+0:00:14]    best: True
[2025-09-15 02:50:40] [STDOUT] [+0:00:14] 
[2025-09-15 02:50:40] [STDOUT] [+0:00:14]    all_candidates: True
[2025-09-15 02:50:40] [STDOUT] [+0:00:14] 
[2025-09-15 02:50:40] [STDOUT] [+0:00:14] 💾 Storing candidate information for fallback system...
[2025-09-15 02:50:40] [STDOUT] [+0:00:14] 
[2025-09-15 02:50:40] [STDERR] [+0:00:14] 2025-09-15 02:50:40,482 - interactive_pipeline_01 - INFO - 📄 Stored candidate info for Radarr ID 673
[2025-09-15 02:50:40] [STDERR] [+0:00:14] 2025-09-15 02:50:40,482 - interactive_pipeline_01 - INFO -    🎯 Candidate: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-15 02:50:40] [STDERR] [+0:00:14] 2025-09-15 02:50:40,482 - interactive_pipeline_01 - INFO -    👤 User selection index: 21
[2025-09-15 02:50:40] [STDOUT] [+0:00:14] ✅ Stored candidate #22 of 22 acceptable candidates for fallback
[2025-09-15 02:50:40] [STDOUT] [+0:00:14] 
[2025-09-15 02:50:40] [STDERR] [+0:00:14] 2025-09-15 02:50:40,505 - interactive_pipeline_01 - INFO - 🎬 Movie: Mapped indexer 'NZBFinder (Prowlarr)' → ID: 2
[2025-09-15 02:50:40] [STDERR] [+0:00:14] 2025-09-15 02:50:40,505 - interactive_pipeline_01 - INFO - 🎬 Movie: Download payload: {'guid': 'https://nzbfinder.ws/details/7286961e-8aea-49c2-aa4c-28becf64f811', 'indexerId': 2}
[2025-09-15 02:50:40] [STDERR] [+0:00:14] 2025-09-15 02:50:40,506 - interactive_pipeline_01 - INFO - 🎬 Movie: Checking if '13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR' exists in cache...
[2025-09-15 02:50:41] [STDERR] [+0:00:15] 2025-09-15 02:50:41,834 - interactive_pipeline_01 - INFO - 🎬 Movie: ✅ Found release in cache, proceeding with download
[2025-09-15 02:50:44] [STDERR] [+0:00:19] 2025-09-15 02:50:44,998 - interactive_pipeline_01 - INFO - 🎬 Movie: ✅ Direct download successful: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-15 02:50:44] [STDOUT] [+0:00:19] ✅ Movie download started: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-15 02:50:44] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:44] [STDOUT] [+0:00:19]    📥 Size: 22.93 GB | Risk: 0.0013 | Decision: ACCEPT
[2025-09-15 02:50:44] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:44] [STDERR] [+0:00:19] 2025-09-15 02:50:44,999 - interactive_pipeline_01 - INFO - 📝 Updated NZB filename for radarr_673: '13 Going on 30 (2004)' → '13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR'
[2025-09-15 02:50:44] [STDERR] [+0:00:19] 2025-09-15 02:50:44,999 - interactive_pipeline_01 - INFO - 📝 Updated telemetry with NZB filename: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-15 02:50:44] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:44] [STDOUT] [+0:00:19] ✅ Preflight found and started download for 1 movie
[2025-09-15 02:50:44] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 🔬 Preflight Movie Selection (downloading):
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19]    #1. 🎬 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19]        💾 Size: 22.93 GB (24,616,529,445 bytes)
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19]        ⚡ Risk: 0.0013 | Missing: 0.3% | Decision: ACCEPT
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 📊 Movie Preflight Summary: 1 movie | Total: 22.93 GB
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 🎯 Download started immediately after analysis
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDERR] [+0:00:19] 2025-09-15 02:50:45,003 - interactive_pipeline_01 - INFO - Stored metadata for: 13 Going on 30 (2004)
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] ============================================================
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 📊 Processing Complete!
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] ============================================================
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 🎬 Movies processed: 1
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 📺 TV shows processed: 0
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 📊 Total content processed: 1
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] ✅ Early telemetry monitoring already active for 1 downloads
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19]    Monitoring started when first download was added - no need to start again
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 🎉 All downloads verified and started successfully!
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDERR] [+0:00:19] 2025-09-15 02:50:45,004 - interactive_pipeline_01 - INFO - Telemetry verification: All downloads confirmed
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 🔬 Running Phase 1 enhanced download verification...
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19]    Checking actual downloads via Radarr API for accurate status...
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19]    ⏳ No downloads confirmed yet - may still be processing
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDERR] [+0:00:19] 2025-09-15 02:50:45,053 - interactive_pipeline_01 - INFO - Phase 1: No confirmed downloads found yet
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 🎬 Movies queued and verified with real-time monitoring
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 📥 Check Radarr for download progress
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDERR] [+0:00:19] 2025-09-15 02:50:45,054 - interactive_pipeline_01 - INFO - 🔬 Script 1 complete - telemetry session kept alive for early monitoring
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 📊 Script 1 complete - telemetry monitoring continues in background
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19]    Dashboard will keep updating until downloads finish
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDERR] [+0:00:19] 2025-09-15 02:50:45,054 - interactive_pipeline_01 - INFO - ===== Interactive Pipeline 01 Execution Complete =====
[2025-09-15 02:50:45] [STDERR] [+0:00:19] 2025-09-15 02:50:45,054 - asyncio - ERROR - Unclosed client session
[2025-09-15 02:50:45] [STDERR] [+0:00:19] client_session: <aiohttp.client.ClientSession object at 0x0000022ABBA4E120>
[2025-09-15 02:50:45] [STDERR] [+0:00:19] 2025-09-15 02:50:45,054 - asyncio - ERROR - Unclosed connector
[2025-09-15 02:50:45] [STDERR] [+0:00:19] connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000022ABBB0FB90>, 16686.7593338)])']
[2025-09-15 02:50:45] [STDERR] [+0:00:19] connector: <aiohttp.connector.TCPConnector object at 0x0000022ABBA4E270>
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] ------------------------------------------------------------
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 🏁 Terminal logging ended for 01_intake_and_nzb_search
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 🕐 Ended at: 2025-09-15 02:50:45
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] ⏱️ Total duration: 0:00:19.090916
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-15_02-50-25-AM.txt
[2025-09-15 02:50:45] [STDOUT] [+0:00:19] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 01_intake_and_nzb_search
Ended: 2025-09-15 02:50:45
Duration: 0:00:19.090916
==================================================
