#!/usr/bin/env python3
"""
Test script to verify early telemetry monitoring functionality.

This script simulates the early telemetry trigger to ensure it works correctly
without running the full intake process.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add internal modules to path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

from utils.telemetry_integration import TelemetryIntegrator
from utils.common_helpers import load_settings

# Import the early monitoring function
sys.path.insert(0, str(Path(__file__).parent))

# We'll define a simple test version since importing from the main script is complex
_early_monitoring_started = False

async def start_early_telemetry_monitoring_if_needed(telemetry_integrator, logger):
    """Test version of the early monitoring function."""
    global _early_monitoring_started

    if _early_monitoring_started:
        logger.info("Early monitoring already started - skipping")
        return

    if not telemetry_integrator or not telemetry_integrator.telemetry:
        logger.warning("Cannot start early monitoring - telemetry not available")
        return

    try:
        _early_monitoring_started = True
        logger.info("🚀 EARLY TELEMETRY MONITORING: Starting monitoring immediately")
        print("🚀 Starting real-time download monitoring NOW")

        # Start monitoring in the background
        import asyncio
        asyncio.create_task(telemetry_integrator.telemetry.start_monitoring(interval=10))

        logger.info("✅ Early telemetry monitoring started")
        print("✅ Download monitoring active")

    except Exception as e:
        logger.error(f"❌ Failed to start early telemetry monitoring: {e}")
        _early_monitoring_started = False

async def test_early_telemetry():
    """Test the early telemetry monitoring functionality."""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("test_early_telemetry")
    
    print("🧪 Testing Early Telemetry Monitoring")
    print("=" * 50)
    
    try:
        # Load settings
        settings_dict = load_settings("_internal/config/settings.ini")
        logger.info("Settings loaded successfully")
        
        # Initialize telemetry integrator
        telemetry_integrator = TelemetryIntegrator(settings_dict, logger, verbose_mode=True)
        await telemetry_integrator.__aenter__()
        logger.info("Telemetry integrator initialized")
        
        # Test 1: Check initial state
        print(f"\n📊 Test 1: Initial state")
        print(f"   Early monitoring started: {_early_monitoring_started}")
        print(f"   Active downloads: {telemetry_integrator.get_active_download_count()}")
        
        # Test 2: Add a mock movie download
        print(f"\n📊 Test 2: Adding mock movie download")
        job_id = telemetry_integrator.track_movie_download(
            title="Test Movie (2023)",
            radarr_id=999,
            quality="HD-1080p"
        )
        print(f"   Job ID: {job_id[:8]}...")
        print(f"   Active downloads after tracking: {telemetry_integrator.get_active_download_count()}")
        
        # Test 3: Trigger early monitoring
        print(f"\n📊 Test 3: Triggering early monitoring")
        await start_early_telemetry_monitoring_if_needed(telemetry_integrator, logger)
        print(f"   Early monitoring started: {_early_monitoring_started}")
        
        # Test 4: Try to trigger again (should be ignored)
        print(f"\n📊 Test 4: Attempting to trigger again (should be ignored)")
        await start_early_telemetry_monitoring_if_needed(telemetry_integrator, logger)
        
        # Test 5: Add another download (monitoring should already be running)
        print(f"\n📊 Test 5: Adding second download (monitoring should continue)")
        job_id2 = telemetry_integrator.track_movie_download(
            title="Another Test Movie (2024)",
            radarr_id=1000,
            quality="UHD-2160p"
        )
        print(f"   Second job ID: {job_id2[:8]}...")
        print(f"   Total active downloads: {telemetry_integrator.get_active_download_count()}")
        
        # Wait a bit to see monitoring in action
        print(f"\n📊 Test 6: Monitoring for 30 seconds...")
        await asyncio.sleep(30)
        
        print(f"\n✅ Early telemetry test completed successfully!")
        print(f"   Final state - Early monitoring: {_early_monitoring_started}")
        print(f"   Final active downloads: {telemetry_integrator.get_active_download_count()}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Test error: {e}")
        return False
    
    finally:
        # Cleanup
        if 'telemetry_integrator' in locals():
            await telemetry_integrator.__aexit__(None, None, None)
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Early Telemetry Test")
    success = asyncio.run(test_early_telemetry())
    if success:
        print("🎉 All tests passed!")
    else:
        print("💥 Some tests failed!")
        sys.exit(1)
