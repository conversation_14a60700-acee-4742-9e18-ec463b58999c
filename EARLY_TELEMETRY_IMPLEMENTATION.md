# Early Telemetry Implementation

## Problem Solved

**Issue**: "There Will Be Blood" ISO extraction failed because telemetry monitoring started too late, causing disk space issues.

**Root Cause**: 
- Script 1 ran for 14+ hours doing analysis
- Downloads started immediately when movies were added to Radarr
- Telemetry monitoring only started when Script 1 completed
- By then, all downloads were complete and disk space was consumed
- ISO extraction failed due to insufficient space

## Solution Implemented

### Early Telemetry Trigger
Modified Script 1 to start telemetry monitoring **immediately after the first movie/TV show is added**, not after all processing completes.

### Key Changes Made

#### 1. Early Monitoring Function (`01_intake_and_nzb_search.py`)
```python
async def start_early_telemetry_monitoring_if_needed(telemetry_integrator, logger):
    """Start telemetry monitoring immediately after first download is tracked."""
    global _early_monitoring_started
    
    if _early_monitoring_started:
        return  # Already started
    
    _early_monitoring_started = True
    # Start monitoring in background without blocking Script 1
    asyncio.create_task(telemetry_integrator.telemetry.start_monitoring(interval=10))
```

#### 2. Movie Tracking Integration
- Added early monitoring trigger to `track_movie_success_with_telemetry()`
- Monitoring starts as soon as first movie is successfully added to Radarr

#### 3. TV Show Tracking Integration  
- Added early monitoring trigger to `track_tv_show_success_with_telemetry()`
- Monitoring starts as soon as first TV show is successfully added to Sonarr

#### 4. Duplicate Prevention
- Modified end-of-script monitoring to check if early monitoring is already running
- Prevents duplicate monitoring processes

## New Flow

### Before (Problematic):
1. Script 1 starts (00:09:55)
2. Script 1 runs for 14+ hours analyzing movies
3. Downloads start immediately when movies added to Radarr
4. Script 2/Telemetry starts (14:00:04) - 14 hours later!
5. Downloads already complete, disk space consumed
6. ISO extraction fails due to space

### After (Fixed):
1. Script 1 starts (00:09:55)
2. **First movie added to Radarr → Telemetry monitoring starts immediately**
3. Downloads monitored in real-time as Script 1 continues
4. Space management and completion tracking happen immediately
5. ISO extraction succeeds because space is managed properly

## Benefits

1. **Real-time Monitoring**: Downloads tracked as they happen, not hours later
2. **Better Space Management**: Early detection of completions allows better disk space planning
3. **Immediate Feedback**: User sees download progress while continuing to add more content
4. **Order-Independent**: Processing order no longer affects monitoring effectiveness
5. **Fallback Protection**: Intelligent fallback system can respond immediately to failures

## Testing

Created `test_early_telemetry.py` to verify:
- Early monitoring starts correctly
- Duplicate triggers are ignored
- Multiple downloads are tracked properly
- Monitoring continues while Script 1 processes more content

## Configuration

The early telemetry is enabled by default. To disable (not recommended):
```ini
[TELEMETRY]
start_early = false
```

## Impact on "There Will Be Blood" Issue

This fix directly addresses the root cause:
- Telemetry will start monitoring as soon as the first movie is added
- Downloads will be tracked in real-time during the 14-hour analysis period
- Completed downloads will be detected immediately
- Disk space will be managed properly throughout the process
- ISO extraction will have adequate space when it runs

The ISO extraction logic itself was correct - the issue was timing and space management caused by delayed telemetry startup.
