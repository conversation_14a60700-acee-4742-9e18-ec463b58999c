=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-15 02:39:04
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-15_02-39-04-AM.txt
==================================================

[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-15_02-39-04-AM.txt
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-15 02:39:04
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDERR] [+0:00:00] 2025-09-15 02:39:04,901 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDERR] [+0:00:00] 2025-09-15 02:39:04,902 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-15 02:39:04] [STDERR] [+0:00:00] 2025-09-15 02:39:04,902 - interactive_pipeline_01 - WARNING - ⚠️ Failed to evaluate auto-start for Stage 02: cannot access local variable '_get' where it is not associated with a value
[2025-09-15 02:39:04] [STDERR] [+0:00:00] 2025-09-15 02:39:04,902 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-15 02:39:04] [STDERR] [+0:00:00] 2025-09-15 02:39:04,903 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-15 02:39:04] [STDERR] [+0:00:00] 2025-09-15 02:39:04,903 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-15 02:39:04] [STDERR] [+0:00:00] 2025-09-15 02:39:04,903 - interactive_pipeline_01 - INFO -    📊 Loaded 75 existing movie records
[2025-09-15 02:39:04] [STDERR] [+0:00:00] 2025-09-15 02:39:04,903 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:04] [STDOUT] [+0:00:00]   4. Quit
[2025-09-15 02:39:04] [STDOUT] [+0:00:00] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 📁 Loaded 6 movies from C:\Users\<USER>\Videos\PlexAutomator\new_movie_requests.txt
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] ============================================================
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 🎬 Movies Available for Processing:
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] ============================================================
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]    1. 13 Going on 30 (2004)
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]    2. Don't Breathe (2016)
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]    3. Top Gun: Maverick (2022)
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]    4. There Will Be Blood (2007)
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]    5. Star Trek Into Darkness (2013)
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]    6. The Dark Knight (2008)
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 📝 Selection Options:
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]   • Single: Enter number (e.g., '3')
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]   • All: Enter 'all' or 'a'
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]   • None: Enter 'none' or 'n' to skip
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:06] [STDOUT] [+0:00:02]   • Quit: Enter 'quit' or 'q'
[2025-09-15 02:39:06] [STDOUT] [+0:00:02] 
[2025-09-15 02:39:11] [STDOUT] [+0:00:06] ✅ Selected 2 movies:
[2025-09-15 02:39:11] [STDOUT] [+0:00:06] 
[2025-09-15 02:39:11] [STDOUT] [+0:00:06]     1. There Will Be Blood (2007)
[2025-09-15 02:39:11] [STDOUT] [+0:00:06] 
[2025-09-15 02:39:11] [STDOUT] [+0:00:06]     2. Don't Breathe (2016)
[2025-09-15 02:39:11] [STDOUT] [+0:00:06] 
[2025-09-15 02:39:12] [STDOUT] [+0:00:07] 
[2025-09-15 02:39:12] [STDOUT] [+0:00:07] 🎬 Processing 2 selected movies...
[2025-09-15 02:39:12] [STDOUT] [+0:00:07] 
[2025-09-15 02:39:12] [STDOUT] [+0:00:07] ============================================================
[2025-09-15 02:39:12] [STDOUT] [+0:00:07] 
[2025-09-15 02:39:12] [STDERR] [+0:00:07] 2025-09-15 02:39:12,790 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-15 02:39:12] [STDOUT] [+0:00:07] 
[2025-09-15 02:39:12] [STDOUT] [+0:00:07] 📍 Progress: 1/2
[2025-09-15 02:39:12] [STDOUT] [+0:00:07] 
[2025-09-15 02:39:12] [STDOUT] [+0:00:07] 🎬 Processing: There Will Be Blood (2007)
[2025-09-15 02:39:12] [STDOUT] [+0:00:07] 
[2025-09-15 02:39:12] [STDERR] [+0:00:07] 2025-09-15 02:39:12,790 - interactive_pipeline_01 - INFO - Processing movie: There Will Be Blood (2007)
[2025-09-15 02:39:13] [STDERR] [+0:00:08] 2025-09-15 02:39:13,002 - utils.metadata_apis - INFO - Generated 2 search variants: ['There Will Be Blood', 'the There Will Be Blood']
[2025-09-15 02:39:13] [STDERR] [+0:00:08] 2025-09-15 02:39:13,134 - utils.metadata_apis - INFO - TMDb search for 'There Will Be Blood' (Year: 2007) found 20 results.
[2025-09-15 02:39:13] [STDERR] [+0:00:08] 2025-09-15 02:39:13,134 - utils.metadata_apis - INFO - TMDb search 'There Will Be Blood' with year 2007: 20 results
[2025-09-15 02:39:13] [STDERR] [+0:00:08] 2025-09-15 02:39:13,260 - utils.metadata_apis - INFO - TMDb search for 'There Will Be Blood' (Year: None) found 20 results.
[2025-09-15 02:39:13] [STDERR] [+0:00:08] 2025-09-15 02:39:13,260 - utils.metadata_apis - INFO - TMDb search 'There Will Be Blood' without year: 20 results
[2025-09-15 02:39:13] [STDERR] [+0:00:08] 2025-09-15 02:39:13,260 - utils.metadata_apis - INFO - Total unique results from all variants: 39
[2025-09-15 02:39:13] [STDERR] [+0:00:08] 2025-09-15 02:39:13,261 - utils.metadata_apis - WARNING - Multiple versions found for 'there will be blood': years ['2007', '2013']
[2025-09-15 02:39:13] [STDERR] [+0:00:09] 2025-09-15 02:39:13,902 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'account:' -> 'accounts'
[2025-09-15 02:39:14] [STDERR] [+0:00:09] 2025-09-15 02:39:14,177 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'jojo's' -> 'yoko's', 'adventure:' -> 'adventure'
[2025-09-15 02:39:14] [STDERR] [+0:00:09] 2025-09-15 02:39:14,428 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'shura' -> 'sura'
[2025-09-15 02:39:15] [STDERR] [+0:00:10] 2025-09-15 02:39:15,027 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'rika:' -> 'riga'
[2025-09-15 02:39:15] [STDERR] [+0:00:10] 2025-09-15 02:39:15,642 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'blood:' -> 'bloods'
[2025-09-15 02:39:15] [STDERR] [+0:00:10] 2025-09-15 02:39:15,698 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'etc' -> 'eth'
[2025-09-15 02:39:15] [STDERR] [+0:00:10] 2025-09-15 02:39:15,803 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'splatter:' -> 'splatter'
[2025-09-15 02:39:15] [STDERR] [+0:00:11] 2025-09-15 02:39:15,940 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'johan' -> 'john', 'falk:' -> 'walks'
[2025-09-15 02:39:16] [STDERR] [+0:00:11] 2025-09-15 02:39:16,088 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'glamour:' -> 'glamour'
[2025-09-15 02:39:16] [STDERR] [+0:00:11] 2025-09-15 02:39:16,551 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'milk,' -> 'milk'
[2025-09-15 02:39:17] [STDERR] [+0:00:12] 2025-09-15 02:39:17,304 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'account:' -> 'accounts'
[2025-09-15 02:39:17] [STDERR] [+0:00:12] 2025-09-15 02:39:17,572 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'jojo's' -> 'yoko's', 'adventure:' -> 'adventure'
[2025-09-15 02:39:17] [STDERR] [+0:00:12] 2025-09-15 02:39:17,813 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'shura' -> 'sura'
[2025-09-15 02:39:18] [STDERR] [+0:00:13] 2025-09-15 02:39:18,435 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'rika:' -> 'riga'
[2025-09-15 02:39:19] [STDERR] [+0:00:14] 2025-09-15 02:39:19,036 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'blood:' -> 'bloods'
[2025-09-15 02:39:19] [STDERR] [+0:00:14] 2025-09-15 02:39:19,086 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'etc' -> 'eth'
[2025-09-15 02:39:19] [STDERR] [+0:00:14] 2025-09-15 02:39:19,189 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'splatter:' -> 'splatter'
[2025-09-15 02:39:19] [STDERR] [+0:00:14] 2025-09-15 02:39:19,333 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'johan' -> 'john', 'falk:' -> 'walks'
[2025-09-15 02:39:19] [STDERR] [+0:00:14] 2025-09-15 02:39:19,484 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'glamour:' -> 'glamour'
[2025-09-15 02:39:19] [STDERR] [+0:00:15] 2025-09-15 02:39:19,936 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'milk,' -> 'milk'
[2025-09-15 02:39:20] [STDERR] [+0:00:16] 2025-09-15 02:39:20,937 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'l'odore' -> 'leonore', 'sangue' -> 'langue'
[2025-09-15 02:39:21] [STDERR] [+0:00:16] 2025-09-15 02:39:21,682 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'batismo' -> 'baptisms', 'sangue' -> 'langue'
[2025-09-15 02:39:22] [STDERR] [+0:00:17] 2025-09-15 02:39:22,112 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'nuestro' -> 'maestro'
[2025-09-15 02:39:22] [STDERR] [+0:00:17] 2025-09-15 02:39:22,502 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'account:' -> 'accounts'
[2025-09-15 02:39:23] [STDERR] [+0:00:19] 2025-09-15 02:39:23,971 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'sangre' -> 'sanger', 'eterna' -> 'sterna'
[2025-09-15 02:39:24] [STDERR] [+0:00:19] 2025-09-15 02:39:24,325 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'jojo's' -> 'yoko's', 'adventure:' -> 'adventure'
[2025-09-15 02:39:25] [STDERR] [+0:00:20] 2025-09-15 02:39:25,131 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'pura' -> 'pure', 'sangre' -> 'sanger'
[2025-09-15 02:39:26] [STDERR] [+0:00:21] 2025-09-15 02:39:26,388 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'shura' -> 'sura'
[2025-09-15 02:39:27] [STDERR] [+0:00:22] 2025-09-15 02:39:27,382 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'd'un' -> 'dun', 'poète' -> 'porte'
[2025-09-15 02:39:28] [STDERR] [+0:00:23] 2025-09-15 02:39:28,062 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'rika:' -> 'riga'
[2025-09-15 02:39:29] [STDERR] [+0:00:24] 2025-09-15 02:39:29,065 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'nobre' -> 'noble', 'deputado' -> 'deputed'
[2025-09-15 02:39:30] [STDERR] [+0:00:25] 2025-09-15 02:39:30,245 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'froid' -> 'frond'
[2025-09-15 02:39:30] [STDERR] [+0:00:25] 2025-09-15 02:39:30,518 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'wird' -> 'wiry', 'kein' -> 'skein', 'blut' -> 'brut', 'geben' -> 'eben'
[2025-09-15 02:39:31] [STDERR] [+0:00:26] 2025-09-15 02:39:31,437 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'khoon' -> 'shoon'
[2025-09-15 02:39:31] [STDERR] [+0:00:26] 2025-09-15 02:39:31,664 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'blood:' -> 'bloods'
[2025-09-15 02:39:31] [STDERR] [+0:00:26] 2025-09-15 02:39:31,713 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'blood:' -> 'bloods'
[2025-09-15 02:39:31] [STDERR] [+0:00:27] 2025-09-15 02:39:31,938 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'etc' -> 'eth'
[2025-09-15 02:39:31] [STDERR] [+0:00:27] 2025-09-15 02:39:31,986 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'etc' -> 'eth'
[2025-09-15 02:39:32] [STDERR] [+0:00:27] 2025-09-15 02:39:32,264 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'addio' -> 'audio'
[2025-09-15 02:39:32] [STDERR] [+0:00:27] 2025-09-15 02:39:32,490 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'splatter:' -> 'splatter'
[2025-09-15 02:39:32] [STDERR] [+0:00:28] 2025-09-15 02:39:32,944 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'johan' -> 'john', 'falk:' -> 'walks'
[2025-09-15 02:39:33] [STDERR] [+0:00:28] 2025-09-15 02:39:33,749 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'johan' -> 'john', 'falk:' -> 'walks'
[2025-09-15 02:39:34] [STDERR] [+0:00:30] 2025-09-15 02:39:34,947 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'ceremonia' -> 'ceremonial'
[2025-09-15 02:39:35] [STDERR] [+0:00:30] 2025-09-15 02:39:35,167 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'glamour:' -> 'glamour'
[2025-09-15 02:39:35] [STDERR] [+0:00:30] 2025-09-15 02:39:35,502 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'terreur' -> 'terrene', 'montée' -> 'monte', 'déclin' -> 'decline'
[2025-09-15 02:39:37] [STDERR] [+0:00:32] 2025-09-15 02:39:37,150 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'milk,' -> 'milk'
[2025-09-15 02:39:37] [STDERR] [+0:00:32] 2025-09-15 02:39:37,201 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'latte,' -> 'latte', 'rossa' -> 'rosa', 'sangue' -> 'langue'
[2025-09-15 02:39:37] [STDERR] [+0:00:32] 2025-09-15 02:39:37,473 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'diyet' -> 'diet'
[2025-09-15 02:39:37] [STDERR] [+0:00:33] 2025-09-15 02:39:37,959 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'français' -> 'francis'
[2025-09-15 02:39:39] [STDERR] [+0:00:34] 2025-09-15 02:39:39,404 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'sangue' -> 'langue', 'azul' -> 'paul'
[2025-09-15 02:39:39] [STDERR] [+0:00:34] 2025-09-15 02:39:39,675 - utils.metadata_apis - INFO - Auto-approved match: High confidence match (100.0%) - auto-approved
[2025-09-15 02:39:39] [STDERR] [+0:00:34] 2025-09-15 02:39:39,675 - utils.metadata_apis - INFO - Selected TMDb match for 'There Will Be Blood (2007)': ID=7345, Title='There Will Be Blood', Confidence=100.0%
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,019 - utils.metadata_apis - INFO - Fetched details for TMDb ID 7345: There Will Be Blood
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] ✅ Found metadata: There Will Be Blood (2007)
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,019 - interactive_pipeline_01 - INFO - Successfully found metadata for: There Will Be Blood (2007)
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,324 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2007: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,325 - interactive_pipeline_01 - INFO - Searching Radarr for: There Will Be Blood 2007
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,589 - interactive_pipeline_01 - INFO - Found match: There Will Be Blood (2007)
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,591 - interactive_pipeline_01 - INFO - 🔍 Checking for duplicates: TMDB ID 7345 in 0 existing movies
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,592 - interactive_pipeline_01 - INFO - ✅ NO DUPLICATE: Movie TMDB 7345 not found in Radarr - safe to add
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,595 - interactive_pipeline_01 - INFO - 🔁 Using existing Radarr root folder: E:\
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,595 - interactive_pipeline_01 - INFO - 📋 ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,595 - interactive_pipeline_01 - INFO - 🎬 Adding movie to Radarr with 1 quality profile(s): There Will Be Blood 2007
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,596 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 4 (searchForMovie=False)...
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,710 - interactive_pipeline_01 - INFO -    ✅ Successfully added: There Will Be Blood 2007 (ID: 667, Profile: 4)
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 📥 Queued "There Will Be Blood (2007)" for download...
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,711 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-15T02:39:40.711477", "event": "download_queued", "job_id": "radarr_667", "title": "There Will Be Blood (2007)", "source": "radarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "radarr_id": 667, "quality": "Unknown"}
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,711 - interactive_pipeline_01 - INFO - 📋 Enhanced tracking: There Will Be Blood (2007)
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,711 - interactive_pipeline_01 - INFO -    🆔 Radarr ID: 667
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,712 - interactive_pipeline_01 - INFO -    📊 Job ID: radarr_667
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 📊 Movie queued for download: There Will Be Blood (2007)
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDOUT] [+0:00:35]    🔬 Enhanced tracking: radarr_6...
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDOUT] [+0:00:35]    🆔 Radarr ID: 667
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDOUT] [+0:00:35]    🛡️ Fallback protection: Enabled
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,714 - interactive_pipeline_01 - INFO - Phase 1: Enhanced telemetry job started: radarr_667 for movie 667
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,714 - interactive_pipeline_01 - INFO - Movie ID 667 tracked for accurate correlation
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,714 - interactive_pipeline_01 - INFO - 🚀 EARLY TELEMETRY: Starting the same monitoring that normally starts after Script 1
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 🚀 Starting telemetry monitoring NOW (same system, earlier timing)
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDERR] [+0:00:35] 2025-09-15 02:39:40,714 - interactive_pipeline_01 - INFO - ✅ Early telemetry monitoring started - same dashboard, earlier timing
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] ✅ Telemetry dashboard active - you can continue adding more movies/shows
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 🤔 Download Strategy Choice for: There Will Be Blood
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] Choose how you want to handle downloads for this movie:
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 2. ⚡ Radarr Auto-Grab - Let Radarr immediately search and grab based on quality profiles
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 3. ⏭️  Skip - Add to Radarr but don't start any downloads yet
[2025-09-15 02:39:40] [STDOUT] [+0:00:35] 
[2025-09-15 02:39:46] [STDERR] [+0:00:41] 2025-09-15 02:39:46,552 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2007: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 🐛 DEBUG: sanitized movie_title = 'There_Will_Be_Blood'
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 🐛 DEBUG: final out_path = 'workspace\preflight_decisions\There_Will_Be_Blood.json'
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDERR] [+0:00:41] 2025-09-15 02:39:46,552 - interactive_pipeline_01 - INFO - ✅ Found existing preflight decision: workspace\preflight_decisions\There_Will_Be_Blood.json
[2025-09-15 02:39:46] [STDERR] [+0:00:41] 2025-09-15 02:39:46,553 - interactive_pipeline_01 - INFO - 📝 Loaded existing movie preflight decision for: There_Will_Be_Blood
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] ✅ Using existing preflight decision for: There Will Be Blood
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 📊 Preflight Results: 31 analyzed, 31 acceptable, 0 errors
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 📊 Combined Results: 1 total movie analyzed, 31 acceptable releases found
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 🔬 Movie Preflight Analysis Results:
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #1. 🎬 There Will Be Blood - 2007 - German - Ac3HD720p  mkv
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 2.28 GB (2,449,159,785 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.2200 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #2. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.x265-LAMA
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 2.68 GB (2,876,401,144 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0225 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #3. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.x265.10Bit.DD5.1-Pahe
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 2.80 GB (3,006,690,674 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0296 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #4. 🎬 There.Will.Be.Blood.2007.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 3.16 GB (3,387,925,131 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0053 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #5. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.Opus.5.1.AV1-GanG
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 6.50 GB (6,979,231,592 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0004 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #6. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.x264.DDP.5.1-MovieMan
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 6.56 GB (7,039,131,346 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0100 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #7. 🎬 There.Will.Be.Blood.2007.PROPER.720p.BluRay.x264-SiNNERS
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 9.02 GB (9,680,429,252 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0701 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #8. 🎬 There.Will.Be.Blood.2007.REPACK.720p.BluRay.x264-DON
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 12.11 GB (13,007,033,637 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0853 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #9. 🎬 There.Will.Be.Blood.2007.720p.BluRay.DD+5.1.x264-playHD
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 12.75 GB (13,688,384,521 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0013 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #10. 🎬 There.Will.Be.Blood.2007.German.DTS.DL.1080p.BluRay.x264-SightHD
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 14.78 GB (15,865,414,403 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0206 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #11. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.x264-ELBOWDOWN
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 14.94 GB (16,042,381,485 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.1045 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #12. 🎬 There.Will.Be.Blood.2007.1080p.AMZN.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 16.63 GB (17,851,425,010 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0074 | Missing: 0.3% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #13. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.REPACK.DDP5.1.x264-Solar
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 19.90 GB (21,370,280,616 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0087 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #14. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.REPACK.DDP5.1.x264-SoLaR
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 19.91 GB (21,372,929,190 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0095 | Missing: 0.3% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #15. 🎬 There.Will.Be.Blood.2007.Bluray.Remux.1080p.AC3.5.1.EFPG
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 19.96 GB (21,426,661,301 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0136 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #16. 🎬 There.Will.Be.Blood.2007.REPACK.1080p.BluRay.DD5.1.x264-DON
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 19.96 GB (21,427,122,862 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0092 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #17. 🎬 There.Will.Be.Blood.(2007).Bluray.Remux.1080p.AC3.5.1.-.EFPG
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 19.97 GB (21,437,695,075 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0136 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #18. 🎬 There.Will.Be.Blood.2007.720p.BluRay.DTS.X264-ESiR
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 20.76 GB (22,296,186,652 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0206 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #19. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.DD+5.1.x264-playHD
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 23.03 GB (24,726,602,485 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0013 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #20. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.DD.5.1.x264-playHD
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 23.03 GB (24,727,706,318 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0012 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #21. 🎬 There.Will.Be.Blood.2007.BluRay.1080p.LPCM.5.1.x264-CHD
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 23.67 GB (25,416,741,535 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0423 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #22. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.REMUX.MULTi.VC-1.FLAC.5.1-MOOS
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 27.30 GB (29,308,612,148 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.1223 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #23. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.REMUX.MULTi.VC-1.FLAC.5.1-MOOS.
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 28.20 GB (30,277,285,970 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0721 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #24. 🎬 There.Will.Be.Blood.2007.Hybrid.BluRay.Remux.1080p.AVC.TrueHD.5.1-decibeL
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 28.67 GB (30,782,333,054 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0905 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #25. 🎬 There.Will.Be.Blood.2007.REPACK2.1080p.BluRay.REMUX.AVC.TrueHD.5.1-BLURANiUM
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 28.96 GB (31,097,536,795 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0032 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #26. 🎬 There.Will.Be.Blood.2007.German.DL.1080p.BluRay.VC1-VEiL
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 33.12 GB (35,562,538,205 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0002 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #27. 🎬 There.Will.Be.Blood.2007.BluRay.1080p.TrueHD.5.1.VC-1.REMUX-FraMeSToR
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 36.36 GB (39,043,133,275 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0233 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #28. 🎬 There.Will.Be.Blood.2007.BluRay.Remux.VC-1.TrueHD.5.1.Multi-Audio
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 36.42 GB (39,101,311,341 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.1079 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #29. 🎬 There.Will.Be.Blood.2007.REPACK.1080p.BluRay.x264-DON
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 38.04 GB (40,843,981,458 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.1384 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #30. 🎬 There.Will.Be.Blood.2007.1080p.BluRay.Hybrid.REMUX.AVC.TrueHD.5.1-TRiToN
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 40.41 GB (43,391,804,615 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0203 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    #31. 🎬 There.Will.Be.Blood.2007.MULTi.COMPLETE.BLURAY.iNTERNAL-VEiL
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        💾 Size: 47.13 GB (50,607,670,493 bytes)
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]        ⚡ Risk: 0.0002 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 🔬 Preflight selection (best candidate):
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    • 47.13 GB  |  ACCEPT  |  risk: 0.0002  |  missing: 0.0%
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:46] [STDOUT] [+0:00:41]    • Release: There.Will.Be.Blood.2007.MULTi.COMPLETE.BLURAY.iNTERNAL-VEiL
[2025-09-15 02:39:46] [STDOUT] [+0:00:41] 
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 🔍 DEBUG: Checking candidate storage conditions...
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDOUT] [+0:00:43]    telemetry_integrator: True
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDOUT] [+0:00:43]    telemetry_integrator.telemetry: True
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDOUT] [+0:00:43]    best: True
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDOUT] [+0:00:43]    all_candidates: True
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 💾 Storing candidate information for fallback system...
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,116 - interactive_pipeline_01 - INFO - 📄 Stored candidate info for Radarr ID 667
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,116 - interactive_pipeline_01 - INFO -    🎯 Candidate: There.Will.Be.Blood.2007.MULTi.COMPLETE.BLURAY.iNTERNAL-VEiL
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,116 - interactive_pipeline_01 - INFO -    👤 User selection index: 30
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] ✅ Stored candidate #31 of 31 acceptable candidates for fallback
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,116 - interactive_pipeline_01 - INFO - 🔄 Starting real-time download monitoring (interval: 5s)
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 🔎 Verifying download in queue for "There Will Be Blood (2007)" (attempt 1/6)...
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,140 - interactive_pipeline_01 - INFO - 🎬 Movie: Mapped indexer 'NZBFinder (Prowlarr)' → ID: 2
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,140 - interactive_pipeline_01 - INFO - 🎬 Movie: Download payload: {'guid': 'https://nzbfinder.ws/details/a7b88746-7362-400d-9c41-95dc70e5d04d', 'indexerId': 2}
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,140 - interactive_pipeline_01 - INFO - 🎬 Movie: Checking if 'There.Will.Be.Blood.2007.MULTi.COMPLETE.BLURAY.iNTERNAL-VEiL' exists in cache...
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,296 - interactive_pipeline_01 - INFO - 🔍 Download not found in SABnzbd: 'There Will Be Blood (2007)' (ID: None)
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,296 - interactive_pipeline_01 - INFO - 🔧 Intelligent Fallback System initialized
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,296 - interactive_pipeline_01 - INFO -    📁 Preflight decisions: workspace\preflight_decisions
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,297 - interactive_pipeline_01 - INFO -    🎬 Radarr URL: http://localhost:7878
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,297 - interactive_pipeline_01 - INFO -    🔑 API Key configured: ✅
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,297 - interactive_pipeline_01 - INFO -    📊 Telemetry integration: ✅
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,297 - interactive_pipeline_01 - INFO - 🛡️ Intelligent fallback system initialized with telemetry integration
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 📊 Real-time telemetry monitoring started
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 📄 Telemetry dashboard log: logs\telemetry_dashboard_2025-09-15_02-39-48-AM.txt
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDOUT] [+0:00:43]    (Dashboard output will be written to separate file to keep main log clean)
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 🔎 Verifying download in queue for "There Will Be Blood (2007)" (attempt 2/6)...
[2025-09-15 02:39:48] [STDOUT] [+0:00:43] 
[2025-09-15 02:39:48] [STDERR] [+0:00:43] 2025-09-15 02:39:48,664 - interactive_pipeline_01 - INFO - 🎬 Movie: ✅ Found release in cache, proceeding with download
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,288 - interactive_pipeline_01 - INFO - 🎬 Movie: ✅ Direct download successful: There.Will.Be.Blood.2007.MULTi.COMPLETE.BLURAY.iNTERNAL-VEiL
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] ✅ Movie download started: There.Will.Be.Blood.2007.MULTi.COMPLETE.BLURAY.iNTERNAL-VEiL
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47]    📥 Size: 47.13 GB | Risk: 0.0002 | Decision: ACCEPT
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,289 - interactive_pipeline_01 - INFO - 📝 Updated NZB filename for radarr_667: 'There Will Be Blood (2007)' → 'There.Will.Be.Blood.2007.MULTi.COMPLETE.BLURAY.iNTERNAL-VEiL'
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,289 - interactive_pipeline_01 - INFO - 📝 Updated telemetry with NZB filename: There.Will.Be.Blood.2007.MULTi.COMPLETE.BLURAY.iNTERNAL-VEiL
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] ✅ Preflight found and started download for 1 movie
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 🔬 Preflight Movie Selection (downloading):
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47]    #1. 🎬 There.Will.Be.Blood.2007.MULTi.COMPLETE.BLURAY.iNTERNAL-VEiL
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47]        💾 Size: 47.13 GB (50,607,670,493 bytes)
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47]        ⚡ Risk: 0.0002 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 📊 Movie Preflight Summary: 1 movie | Total: 47.13 GB
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 🎯 Download started immediately after analysis
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,292 - interactive_pipeline_01 - INFO - Stored metadata for: There Will Be Blood (2007)
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 📍 Progress: 2/2
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 🎬 Processing: Don't Breathe (2016)
[2025-09-15 02:39:52] [STDOUT] [+0:00:47] 
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,292 - interactive_pipeline_01 - INFO - Processing movie: Don't Breathe (2016)
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,492 - utils.metadata_apis - INFO - Generated 2 search variants: ["Don't Breathe", "the Don't Breathe"]
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,599 - utils.metadata_apis - INFO - TMDb search for 'Don't Breathe' (Year: 2016) found 1 results.
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,600 - utils.metadata_apis - INFO - TMDb search 'Don't Breathe' with year 2016: 1 results
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,722 - utils.metadata_apis - INFO - TMDb search for 'Don't Breathe' (Year: None) found 5 results.
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,722 - utils.metadata_apis - INFO - TMDb search 'Don't Breathe' without year: 5 results
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,842 - utils.metadata_apis - INFO - TMDb search for 'the Don't Breathe' (Year: 2016) found 1 results.
[2025-09-15 02:39:52] [STDERR] [+0:00:47] 2025-09-15 02:39:52,843 - utils.metadata_apis - INFO - TMDb search 'the Don't Breathe' with year 2016: 1 results
[2025-09-15 02:39:52] [STDERR] [+0:00:48] 2025-09-15 02:39:52,965 - utils.metadata_apis - INFO - TMDb search for 'the Don't Breathe' (Year: None) found 4 results.
[2025-09-15 02:39:52] [STDERR] [+0:00:48] 2025-09-15 02:39:52,965 - utils.metadata_apis - INFO - TMDb search 'the Don't Breathe' without year: 4 results
[2025-09-15 02:39:52] [STDERR] [+0:00:48] 2025-09-15 02:39:52,965 - utils.metadata_apis - INFO - Total unique results from all variants: 5
[2025-09-15 02:39:52] [STDERR] [+0:00:48] 2025-09-15 02:39:52,965 - utils.metadata_apis - WARNING - Multiple versions found for 'don't breathe': years ['2016', '2014', '2022']
[2025-09-15 02:39:52] [STDERR] [+0:00:48] 2025-09-15 02:39:52,965 - utils.metadata_apis - INFO - Prioritized 1 year matches for vague query with year 2016
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,428 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'pozabi' -> 'pohai', 'dihati' -> 'diwali'
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,581 - _internal.utils.fuzzy_matching - INFO - Selected 'Don't Breathe' from 3 versions (composite score: 143.6)
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,582 - utils.metadata_apis - INFO - Auto-approved match: High confidence match (100.0%) - auto-approved
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,582 - utils.metadata_apis - INFO - Selected TMDb match for 'Don't Breathe (2016)': ID=300669, Title='Don't Breathe', Confidence=100.0%
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,691 - utils.metadata_apis - INFO - Fetched details for TMDb ID 300669: Don't Breathe
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] ✅ Found metadata: Don't Breathe (2016)
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,692 - interactive_pipeline_01 - INFO - Successfully found metadata for: Don't Breathe (2016)
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,695 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2016: 2016+ movie: Using 4K only (Profile 5) - largest file preferred
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,695 - interactive_pipeline_01 - INFO - Searching Radarr for: Don't Breathe 2016
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,752 - interactive_pipeline_01 - INFO - Found match: Don't Breathe (2016)
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,754 - interactive_pipeline_01 - INFO - 🔍 Checking for duplicates: TMDB ID 300669 in 1 existing movies
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,754 - interactive_pipeline_01 - INFO - 📊 Radarr TMDB IDs: [7345]
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,755 - interactive_pipeline_01 - INFO - ✅ NO DUPLICATE: Movie TMDB 300669 not found in Radarr - safe to add
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,756 - interactive_pipeline_01 - INFO - 🔁 Using existing Radarr root folder: E:\
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,757 - interactive_pipeline_01 - INFO - 📋 2016+ movie: Using 4K only (Profile 5) - largest file preferred
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,757 - interactive_pipeline_01 - INFO - 🎬 Adding movie to Radarr with 1 quality profile(s): Don't Breathe 2016
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,757 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 5 (searchForMovie=False)...
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,846 - interactive_pipeline_01 - INFO -    ✅ Successfully added: Don't Breathe 2016 (ID: 668, Profile: 5)
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 📥 Queued "Don't Breathe (2016)" for download...
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,847 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-15T02:39:55.847816", "event": "download_queued", "job_id": "radarr_668", "title": "Don't Breathe (2016)", "source": "radarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "radarr_id": 668, "quality": "Unknown"}
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,847 - interactive_pipeline_01 - INFO - 📋 Enhanced tracking: Don't Breathe (2016)
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,848 - interactive_pipeline_01 - INFO -    🆔 Radarr ID: 668
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,848 - interactive_pipeline_01 - INFO -    📊 Job ID: radarr_668
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 📊 Movie queued for download: Don't Breathe (2016)
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDOUT] [+0:00:50]    🔬 Enhanced tracking: radarr_6...
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDOUT] [+0:00:50]    🆔 Radarr ID: 668
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDOUT] [+0:00:50]    🛡️ Fallback protection: Enabled
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,850 - interactive_pipeline_01 - INFO - Phase 1: Enhanced telemetry job started: radarr_668 for movie 668
[2025-09-15 02:39:55] [STDERR] [+0:00:50] 2025-09-15 02:39:55,850 - interactive_pipeline_01 - INFO - Movie ID 668 tracked for accurate correlation
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 🤔 Download Strategy Choice for: Don't Breathe
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] Choose how you want to handle downloads for this movie:
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 2. ⚡ Radarr Auto-Grab - Let Radarr immediately search and grab based on quality profiles
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 3. ⏭️  Skip - Add to Radarr but don't start any downloads yet
[2025-09-15 02:39:55] [STDOUT] [+0:00:50] 
[2025-09-15 02:39:59] [STDERR] [+0:00:54] 2025-09-15 02:39:59,410 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2016: 2016+ movie: Using 4K only (Profile 5) - largest file preferred
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 🐛 DEBUG: sanitized movie_title = 'Don't_Breathe'
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 🐛 DEBUG: final out_path = 'workspace\preflight_decisions\Don't_Breathe.json'
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDERR] [+0:00:54] 2025-09-15 02:39:59,411 - interactive_pipeline_01 - INFO - ✅ Found existing preflight decision: workspace\preflight_decisions\Don't_Breathe.json
[2025-09-15 02:39:59] [STDERR] [+0:00:54] 2025-09-15 02:39:59,412 - interactive_pipeline_01 - INFO - 📝 Loaded existing movie preflight decision for: Don't_Breathe
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] ✅ Using existing preflight decision for: Don't Breathe
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 📊 Preflight Results: 33 analyzed, 29 acceptable, 0 errors
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 📊 Combined Results: 1 total movie analyzed, 29 acceptable releases found
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 🔬 Movie Preflight Analysis Results:
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #1. 🎬 Dont.Breathe.2016.1080p.BRRip.DDP.5.1.H.265.-iVy
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 1.20 GB (1,283,125,553 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0103 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #2. 🎬 Dont.Breathe.2016.1080p.10bit.BluRay.6CH.x265.HEVC-PSA
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 1.30 GB (1,398,947,356 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0874 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #3. 🎬 Dont.Breathe.2016.1080p.BluRay.x265-LAMA
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 1.50 GB (1,606,744,029 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0224 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #4. 🎬 Dont.Breathe.2016.1080p.BluRay.x265
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 1.57 GB (1,685,634,014 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0288 | Missing: 5.6% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #5. 🎬 Dont.Breathe.2016.1080p.BluRay.H264.AAC-RARBG
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 1.92 GB (2,060,351,520 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0160 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #6. 🎬 Dont.Breathe.2016.1080p.BluRay.x265.DTS
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 1.94 GB (2,078,861,365 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0651 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #7. 🎬 Dont.Breathe.2016.1080p.BluRay.x264-SPARKS
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 2.58 GB (2,771,946,812 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0297 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #8. 🎬 Dont.Breathe.2016.1080p.BluRay.x265.10bit.Tigole
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 4.47 GB (4,803,779,178 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0026 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #9. 🎬 Dont.Breathe.2016.REPACK.1080p.BluRay.x264-nikt0
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 4.50 GB (4,829,443,240 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0866 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #10. 🎬 Dont.Breathe.2016.1080p.BluRay.x264-OFT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 4.50 GB (4,829,690,033 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0012 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #11. 🎬 Dont.Breathe.2016.German.DL.1080p.BluRay.x264-ENCOUNTERS
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 4.65 GB (4,992,816,710 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.1408 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #12. 🎬 Dont.Breathe.2016.1080p.Blu-Ray
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 5.34 GB (5,734,152,369 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0003 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #13. 🎬 Dont.Breathe.2016.NORDiC.1080p.AMZN.WEB-DL.H.264.DDP5.1-DKV
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 5.73 GB (6,154,386,921 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0237 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #14. 🎬 Dont.Breathe.2016.720p.BluRay.DTS.x264-FuzerHD
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 6.33 GB (6,792,231,668 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0084 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #15. 🎬 Dont.Breathe.2016.BluRay.1080p.DTS.x264-PRoDJi
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 7.22 GB (7,752,279,142 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0966 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #16. 🎬 Dont.Breathe.2016.1080p.BluRay.DTS.x264-VietHD
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 10.17 GB (10,921,432,998 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.1636 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #17. 🎬 Dont.Breathe.2016.1080p.BluRay.DTS.x264-ZQ
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 10.56 GB (11,337,201,579 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.1639 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #18. 🎬 Dont.Breathe.2016.1080p.BluRay.x265.10bit.DTS-HD-MA.5.1-UnKn0wn
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 14.50 GB (15,565,358,045 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0108 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #19. 🎬 Dont.Breathe.2016.1080p.BluRay.DTS-HD.MA.5.1.x264-FuzerHD
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 18.39 GB (19,750,409,151 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.2043 | Missing: 26.9% | Decision: RISKY_LOW_PARITY
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #20. 🎬 Dont.Breathe.2016.1080p.BluRay.REMUX.AVC.DTS-HD.MA.5.1-RU4HD
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 19.41 GB (20,844,022,354 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0659 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #21. 🎬 Dont.Breathe.2016.1080p.BluRay.REMUX.AVC.DTS-HD.MA.5.1-EPSiLON
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 19.50 GB (20,941,532,523 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0883 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #22. 🎬 Dont.Breathe.2016.1080p.Remux.AVC.DTS-HD.MA.5.1-playBD
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 19.54 GB (20,978,597,935 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0699 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #23. 🎬 Dont.Breathe.2016.1080p.Blu-ray.Remux.DUAL.AVC.DTS.HD.MA.5.1-BdC
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 19.76 GB (21,212,356,096 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0406 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #24. 🎬 Dont.Breathe.2016.1080p.BluRay.REMUX.AVC.DTS-HD-MA.5.1-UnKn0wn
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 20.42 GB (21,925,474,213 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0121 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #25. 🎬 Dont.Breathe.2016.NORDiC.REMUX.1080p.BluRay.AVC.DTS-HD.MA5.1-TWA
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 21.18 GB (22,745,496,690 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0421 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #26. 🎬 Dont.Breathe.2016.GBR.BluRay.Remux.1080p.AVC.DTS-HD.MA.5.1-NCmt
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 21.52 GB (23,101,702,359 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.1614 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #27. 🎬 Dont.Breathe.2016.Blu-ray.CEE.1080p.AVC.DTS-HD.MA.5.1-NoGroup
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 30.56 GB (32,810,725,939 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0194 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #28. 🎬 Dont.Breathe.2016.MULTi.COMPLETE.BLURAY-GMB
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 30.67 GB (32,931,981,382 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0659 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    #29. 🎬 Dont.Breathe.2016.MULTi.COMPLETE.BLURAY-4FR
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        💾 Size: 33.64 GB (36,124,193,970 bytes)
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]        ⚡ Risk: 0.0284 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 🔬 Preflight selection (best candidate):
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    • 33.64 GB  |  ACCEPT  |  risk: 0.0284  |  missing: 0.0%
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:39:59] [STDOUT] [+0:00:54]    • Release: Dont.Breathe.2016.MULTi.COMPLETE.BLURAY-4FR
[2025-09-15 02:39:59] [STDOUT] [+0:00:54] 
[2025-09-15 02:40:00] [STDOUT] [+0:00:55] 🔍 DEBUG: Checking candidate storage conditions...
[2025-09-15 02:40:00] [STDOUT] [+0:00:55] 
[2025-09-15 02:40:00] [STDOUT] [+0:00:55]    telemetry_integrator: True
[2025-09-15 02:40:00] [STDOUT] [+0:00:55] 
[2025-09-15 02:40:00] [STDOUT] [+0:00:55]    telemetry_integrator.telemetry: True
[2025-09-15 02:40:00] [STDOUT] [+0:00:55] 
[2025-09-15 02:40:00] [STDOUT] [+0:00:55]    best: True
[2025-09-15 02:40:00] [STDOUT] [+0:00:55] 
[2025-09-15 02:40:00] [STDOUT] [+0:00:55]    all_candidates: True
[2025-09-15 02:40:00] [STDOUT] [+0:00:55] 
[2025-09-15 02:40:00] [STDOUT] [+0:00:55] 💾 Storing candidate information for fallback system...
[2025-09-15 02:40:00] [STDOUT] [+0:00:55] 
[2025-09-15 02:40:00] [STDERR] [+0:00:55] 2025-09-15 02:40:00,462 - interactive_pipeline_01 - INFO - 📄 Stored candidate info for Radarr ID 668
[2025-09-15 02:40:00] [STDERR] [+0:00:55] 2025-09-15 02:40:00,463 - interactive_pipeline_01 - INFO -    🎯 Candidate: Dont.Breathe.2016.MULTi.COMPLETE.BLURAY-4FR
[2025-09-15 02:40:00] [STDERR] [+0:00:55] 2025-09-15 02:40:00,463 - interactive_pipeline_01 - INFO -    👤 User selection index: 28
[2025-09-15 02:40:00] [STDOUT] [+0:00:55] ✅ Stored candidate #29 of 29 acceptable candidates for fallback
[2025-09-15 02:40:00] [STDOUT] [+0:00:55] 
[2025-09-15 02:40:00] [STDERR] [+0:00:55] 2025-09-15 02:40:00,483 - interactive_pipeline_01 - INFO - 🎬 Movie: Mapped indexer 'NZBFinder (Prowlarr)' → ID: 2
[2025-09-15 02:40:00] [STDERR] [+0:00:55] 2025-09-15 02:40:00,483 - interactive_pipeline_01 - INFO - 🎬 Movie: Download payload: {'guid': 'https://nzbfinder.ws/details/1b514c67-9261-464b-9413-b751676a153a', 'indexerId': 2}
[2025-09-15 02:40:00] [STDERR] [+0:00:55] 2025-09-15 02:40:00,483 - interactive_pipeline_01 - INFO - 🎬 Movie: Checking if 'Dont.Breathe.2016.MULTi.COMPLETE.BLURAY-4FR' exists in cache...
[2025-09-15 02:40:00] [STDERR] [+0:00:55] 2025-09-15 02:40:00,582 - interactive_pipeline_01 - INFO - 📦 SABnzbd match found: 'There Will Be Blood (2007)' -> 'there.will.be.blood.2007.multi.complete.bluray.internal-veil' (similarity: 1.00)
[2025-09-15 02:40:00] [STDERR] [+0:00:55] 2025-09-15 02:40:00,645 - interactive_pipeline_01 - INFO - 🔍 Download not found in SABnzbd: 'Don't Breathe (2016)' (ID: None)
[2025-09-15 02:40:01] [STDERR] [+0:00:56] 2025-09-15 02:40:01,092 - interactive_pipeline_01 - INFO - 🎬 Movie: ✅ Found release in cache, proceeding with download
[2025-09-15 02:40:05] [STDERR] [+0:01:00] 2025-09-15 02:40:05,170 - interactive_pipeline_01 - INFO - 🎬 Movie: ✅ Direct download successful: Dont.Breathe.2016.MULTi.COMPLETE.BLURAY-4FR
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] ✅ Movie download started: Dont.Breathe.2016.MULTi.COMPLETE.BLURAY-4FR
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00]    📥 Size: 33.64 GB | Risk: 0.0284 | Decision: ACCEPT
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDERR] [+0:01:00] 2025-09-15 02:40:05,171 - interactive_pipeline_01 - INFO - 📝 Updated NZB filename for radarr_668: 'Don't Breathe (2016)' → 'Dont.Breathe.2016.MULTi.COMPLETE.BLURAY-4FR'
[2025-09-15 02:40:05] [STDERR] [+0:01:00] 2025-09-15 02:40:05,171 - interactive_pipeline_01 - INFO - 📝 Updated telemetry with NZB filename: Dont.Breathe.2016.MULTi.COMPLETE.BLURAY-4FR
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] ✅ Preflight found and started download for 1 movie
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 🔬 Preflight Movie Selection (downloading):
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00]    #1. 🎬 Dont.Breathe.2016.MULTi.COMPLETE.BLURAY-4FR
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00]        💾 Size: 33.64 GB (36,124,193,970 bytes)
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00]        ⚡ Risk: 0.0284 | Missing: 0.0% | Decision: ACCEPT
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 📊 Movie Preflight Summary: 1 movie | Total: 33.64 GB
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 🎯 Download started immediately after analysis
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDERR] [+0:01:00] 2025-09-15 02:40:05,175 - interactive_pipeline_01 - INFO - Stored metadata for: Don't Breathe (2016)
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] ============================================================
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 📊 Processing Complete!
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] ============================================================
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 🎬 Movies processed: 2
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 📺 TV shows processed: 0
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 📊 Total content processed: 2
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] ✅ Early telemetry monitoring already active for 2 downloads
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00]    Monitoring started when first download was added - no need to start again
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 🎉 All downloads verified and started successfully!
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDERR] [+0:01:00] 2025-09-15 02:40:05,176 - interactive_pipeline_01 - INFO - Telemetry verification: All downloads confirmed
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 🔬 Running Phase 1 enhanced download verification...
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00]    Checking actual downloads via Radarr API for accurate status...
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00]    ⏳ No downloads confirmed yet - may still be processing
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDERR] [+0:01:00] 2025-09-15 02:40:05,232 - interactive_pipeline_01 - INFO - Phase 1: No confirmed downloads found yet
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 🎬 Movies queued and verified with real-time monitoring
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 📥 Check Radarr for download progress
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDERR] [+0:01:00] 2025-09-15 02:40:05,232 - interactive_pipeline_01 - INFO - 🔬 Script 1 complete - telemetry session kept alive for early monitoring
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 📊 Script 1 complete - telemetry monitoring continues in background
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00]    Dashboard will keep updating until downloads finish
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDERR] [+0:01:00] 2025-09-15 02:40:05,233 - interactive_pipeline_01 - INFO - ===== Interactive Pipeline 01 Execution Complete =====
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] ------------------------------------------------------------
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 🏁 Terminal logging ended for 01_intake_and_nzb_search
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 🕐 Ended at: 2025-09-15 02:40:05
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] ⏱️ Total duration: 0:01:00.335589
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-15_02-39-04-AM.txt
[2025-09-15 02:40:05] [STDOUT] [+0:01:00] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 01_intake_and_nzb_search
Ended: 2025-09-15 02:40:05
Duration: 0:01:00.335589
==================================================
