#!/usr/bin/env python3
"""Debug script to check TV episode data structure"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd() / '_internal'))
from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager

# Initialize manager
workspace_root = Path.cwd()
filesystem_manager = FilesystemFirstStateManager(workspace_root)

# Discover TV episodes
tv_episodes_by_stage = filesystem_manager.discover_tv_episodes_by_stage()

mkv_pending_episodes = tv_episodes_by_stage.get("mkv_processing_pending", [])
print(f"Found {len(mkv_pending_episodes)} TV episodes ready for MKV processing:")

for i, episode in enumerate(mkv_pending_episodes[:2]):
    print(f"\nEpisode {i+1} data structure:")
    for key, value in episode.items():
        print(f"  {key}: {value}")