=== TERMINAL OUTPUT LOG ===
Script: 02_download_and_organize
Started: 2025-09-15 02:25:07
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-15_02-25-07-AM.txt
==================================================

[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 📝 Terminal logging started for 02_download_and_organize
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-15_02-25-07-AM.txt
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-15 02:25:07
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] *** UNIFIED Stage 02: Download and Organize ***
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] ==================================================
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] + Consolidated from multiple O2 scripts into one unified implementation
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] >> Modern Radarr API integration
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] -- Simplified workflow: Radarr -> SABnzbd -> Plex
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] >> Clean, maintainable codebase
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00]    Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDERR] [+0:00:00] 2025-09-15 02:25:07,759 - pipeline_02 - INFO - ===== Starting Pipeline 02 Execution =====
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDERR] [+0:00:00] 2025-09-15 02:25:07,760 - pipeline_02 - INFO - Settings loaded successfully
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00]   4. Quit
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] 
[2025-09-15 02:25:07] [STDOUT] [+0:00:00] Enter your choice [1-4]: 
[2025-09-15 02:25:14] [STDOUT] [+0:00:06] Please enter a number between 1 and 4
[2025-09-15 02:25:14] [STDOUT] [+0:00:06] 
[2025-09-15 02:25:14] [STDOUT] [+0:00:06] 
[2025-09-15 02:25:14] [STDOUT] [+0:00:06] Enter your choice [1-4]: 
