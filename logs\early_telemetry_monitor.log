
=== EARLY TELEMETRY MONITORING STARTED ===
Started by Script 1 at first download detection
This process will continue monitoring until all downloads complete
==================================================

       Virtual environment not found, running with system Python



------------------------------------------------------------
*** UNIFIED Stage 02: Download and Organize ***
==================================================
+ Consolidated from multiple O2 scripts into one unified implementation
>> Modern Radarr API integration
-- Simplified workflow: Radarr -> SABnzbd -> Plex
>> Clean, maintainable codebase
   Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)
2025-09-15 02:18:20,701 - pipeline_02 - INFO - ===== Starting Pipeline 02 Execution =====
INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-15 02:18:20,703 - pipeline_02 - INFO - Settings loaded successfully

============================================================

============================================================

What type of content would you like to process?
  1. Movies only
  2. TV Shows only
  3. Both Movies and TV Shows
  4. Quit

Enter your choice [1-4]: 