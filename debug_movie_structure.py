#!/usr/bin/env python3
"""Debug script to check movie data structure"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd() / '_internal'))
from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager

# Initialize manager
workspace_root = Path.cwd()
filesystem_manager = FilesystemFirstStateManager(workspace_root)

# Discover movies 
movies_by_stage = filesystem_manager.discover_movies_by_stage()

mkv_pending_movies = movies_by_stage.get("mkv_processing_pending", [])
print(f"Found {len(mkv_pending_movies)} movies ready for MKV processing:")

for i, movie in enumerate(mkv_pending_movies[:2]):
    print(f"\nMovie {i+1} data structure:")
    for key, value in movie.items():
        print(f"  {key}: {value}")