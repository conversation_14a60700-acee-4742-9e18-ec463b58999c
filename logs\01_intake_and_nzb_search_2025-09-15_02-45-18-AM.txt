=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-15 02:45:18
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-15_02-45-18-AM.txt
==================================================

[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-15_02-45-18-AM.txt
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-15 02:45:18
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDERR] [+0:00:00] 2025-09-15 02:45:18,567 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDERR] [+0:00:00] 2025-09-15 02:45:18,569 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-15 02:45:18] [STDERR] [+0:00:00] 2025-09-15 02:45:18,569 - interactive_pipeline_01 - WARNING - ⚠️ Failed to evaluate auto-start for Stage 02: cannot access local variable '_get' where it is not associated with a value
[2025-09-15 02:45:18] [STDERR] [+0:00:00] 2025-09-15 02:45:18,569 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-15 02:45:18] [STDERR] [+0:00:00] 2025-09-15 02:45:18,569 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-15 02:45:18] [STDERR] [+0:00:00] 2025-09-15 02:45:18,570 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-15 02:45:18] [STDERR] [+0:00:00] 2025-09-15 02:45:18,570 - interactive_pipeline_01 - INFO -    📊 Loaded 77 existing movie records
[2025-09-15 02:45:18] [STDERR] [+0:00:00] 2025-09-15 02:45:18,570 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:18] [STDOUT] [+0:00:00]   4. Quit
[2025-09-15 02:45:18] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 📁 Loaded 6 movies from C:\Users\<USER>\Videos\PlexAutomator\new_movie_requests.txt
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 🎬 Movies Available for Processing:
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] ============================================================
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]    1. 13 Going on 30 (2004)
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]    2. Don't Breathe (2016)
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]    3. Top Gun: Maverick (2022)
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]    4. There Will Be Blood (2007)
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]    5. Star Trek Into Darkness (2013)
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]    6. The Dark Knight (2008)
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 📝 Selection Options:
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]   • Single: Enter number (e.g., '3')
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]   • All: Enter 'all' or 'a'
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]   • None: Enter 'none' or 'n' to skip
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
[2025-09-15 02:45:19] [STDOUT] [+0:00:00]   • Quit: Enter 'quit' or 'q'
[2025-09-15 02:45:19] [STDOUT] [+0:00:00] 
